// Manual Firebase Initialization Script
// Run this after enabling Firebase Authentication and Firestore

const admin = require('firebase-admin');

// Initialize Firebase Admin SDK with your service account
// You'll need to download the service account key from Firebase Console
const serviceAccount = **********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************;

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
  projectId: 'linkinblink-f544a'
});

const db = admin.firestore();
const auth = admin.auth();

// Sample data to initialize
const initializeData = async () => {
  try {
    console.log('🚀 Starting Firebase initialization...');

    // Create users in Firebase Authentication
    console.log('👤 Creating user accounts...');

    // Create Super Admin
    const adminUser = await auth.createUser({
      email: '<EMAIL>',
      password: 'admin123456',
      displayName: 'Super Administrator'
    });

    // Create Vendor
    const vendorUser = await auth.createUser({
      email: '<EMAIL>',
      password: 'vendor123456',
      displayName: 'Hotel Vendor'
    });

    // Create Staff
    const staffUser = await auth.createUser({
      email: '<EMAIL>',
      password: 'staff123456',
      displayName: 'Hotel Staff Member'
    });

    console.log('✅ User accounts created successfully!');

    // Create user profiles in Firestore
    console.log('📝 Creating user profiles...');

    await db.collection('users').doc(adminUser.uid).set({
      email: '<EMAIL>',
      name: 'Super Administrator',
      role: 'super_admin',
      phone: '******-0001',
      isActive: true,
      createdAt: admin.firestore.Timestamp.now(),
      updatedAt: admin.firestore.Timestamp.now()
    });

    await db.collection('users').doc(vendorUser.uid).set({
      email: '<EMAIL>',
      name: 'Hotel Vendor',
      role: 'vendor',
      phone: '******-0002',
      vendorId: 'vendor-1',
      isActive: true,
      createdAt: admin.firestore.Timestamp.now(),
      updatedAt: admin.firestore.Timestamp.now()
    });

    await db.collection('users').doc(staffUser.uid).set({
      email: '<EMAIL>',
      name: 'Hotel Staff Member',
      role: 'staff',
      phone: '******-0003',
      hotelId: 'hotel-1',
      vendorId: 'vendor-1',
      isActive: true,
      createdAt: admin.firestore.Timestamp.now(),
      updatedAt: admin.firestore.Timestamp.now()
    });

    // Create sample hotels
    console.log('🏨 Creating sample hotels...');

    await db.collection('hotels').doc('hotel-1').set({
      name: 'Grand Plaza Hotel',
      description: 'Luxury hotel in the heart of the city',
      address: '123 Main Street',
      city: 'New York',
      state: 'NY',
      country: 'USA',
      zipCode: '10001',
      phone: '******-1000',
      email: '<EMAIL>',
      website: 'https://grandplaza.com',
      vendorId: 'vendor-1',
      vendorName: 'Hotel Vendor',
      rating: 4.5,
      amenities: ['WiFi', 'Pool', 'Gym', 'Restaurant', 'Spa'],
      images: [],
      isActive: true,
      createdAt: admin.firestore.Timestamp.now(),
      updatedAt: admin.firestore.Timestamp.now()
    });

    // Create sample rooms
    console.log('🛏️ Creating sample rooms...');

    await db.collection('rooms').doc('room-1').set({
      hotelId: 'hotel-1',
      roomNumber: '101',
      type: 'single',
      status: 'available',
      price: 150,
      capacity: 1,
      amenities: ['WiFi', 'TV', 'AC'],
      images: [],
      description: 'Comfortable single room',
      floor: 1,
      isActive: true,
      createdAt: admin.firestore.Timestamp.now(),
      updatedAt: admin.firestore.Timestamp.now()
    });

    await db.collection('rooms').doc('room-2').set({
      hotelId: 'hotel-1',
      roomNumber: '102',
      type: 'double',
      status: 'available',
      price: 200,
      capacity: 2,
      amenities: ['WiFi', 'TV', 'AC', 'Mini Bar'],
      images: [],
      description: 'Spacious double room',
      floor: 1,
      isActive: true,
      createdAt: admin.firestore.Timestamp.now(),
      updatedAt: admin.firestore.Timestamp.now()
    });

    // Create sample staff
    console.log('👥 Creating sample staff...');

    await db.collection('staff').doc('staff-1').set({
      hotelId: 'hotel-1',
      vendorId: 'vendor-1',
      name: 'John Manager',
      email: '<EMAIL>',
      phone: '******-1001',
      role: 'manager',
      department: 'Management',
      salary: 60000,
      hireDate: admin.firestore.Timestamp.now(),
      status: 'active',
      address: '789 Staff Street, New York, NY 10001',
      emergencyContact: {
        name: 'Jane Manager',
        phone: '******-1002',
        relationship: 'Spouse'
      },
      createdAt: admin.firestore.Timestamp.now(),
      updatedAt: admin.firestore.Timestamp.now()
    });

    // Create sample booking
    console.log('📅 Creating sample booking...');

    await db.collection('bookings').doc('booking-1').set({
      hotelId: 'hotel-1',
      hotelName: 'Grand Plaza Hotel',
      roomId: 'room-1',
      roomNumber: '101',
      userId: 'guest-1',
      guestName: 'Alice Johnson',
      guestEmail: '<EMAIL>',
      guestPhone: '******-3001',
      checkInDate: admin.firestore.Timestamp.fromDate(new Date('2025-07-15')),
      checkOutDate: admin.firestore.Timestamp.fromDate(new Date('2025-07-18')),
      adults: 1,
      children: 0,
      totalAmount: 450,
      status: 'confirmed',
      paymentStatus: 'paid',
      specialRequests: 'Late check-in',
      createdAt: admin.firestore.Timestamp.now(),
      updatedAt: admin.firestore.Timestamp.now()
    });

    console.log('🎉 Firebase initialization completed successfully!');
    console.log('');
    console.log('🔑 Login Credentials:');
    console.log('');
    console.log('🔴 SUPER ADMIN PANEL:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: admin123456');
    console.log('');
    console.log('🔵 HOTEL/VENDOR PORTAL:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: vendor123456');
    console.log('');
    console.log('🟢 STAFF PORTAL:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: staff123456');
    console.log('');
    console.log('🌐 Access your application at: http://localhost:3000');

    process.exit(0);
  } catch (error) {
    console.error('❌ Error during initialization:', error);
    process.exit(1);
  }
};

initializeData();
