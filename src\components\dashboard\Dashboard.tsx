import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  CircularProgress,
  Alert,
  Button,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Chip,
  Avatar,
  IconButton,
  Divider,
  LinearProgress,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow
} from '@mui/material';
import {
  Hotel as HotelIcon,
  People as PeopleIcon,
  BookOnline as BookingIcon,
  AttachMoney as RevenueIcon,
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Notifications as NotificationIcon,
  CheckCircle as CheckIcon,
  Warning as WarningIcon,
  Error as ErrorIcon,
  Add as AddIcon,
  Visibility as ViewIcon,
  Edit as EditIcon,
  Schedule as ScheduleIcon,
  Room as RoomIcon,
  CleaningServices as CleaningIcon,
  Build as MaintenanceIcon,
  PersonAdd as PersonAddIcon,
  EventNote as EventIcon,
  Assessment as ReportIcon
} from '@mui/icons-material';
import { useAuth } from '../../hooks/useAuth';
import { getHotelsByVendor, getHotelStats } from '../../services/hotelService';
import { getStaffStats } from '../../services/staffService';
import { getBookingStats } from '../../services/bookingService';
import { format } from 'date-fns';

interface StatCardProps {
  title: string;
  value: string | number;
  icon: React.ReactElement;
  color: string;
  trend?: {
    value: number;
    isPositive: boolean;
    period: string;
  };
  onClick?: () => void;
}

const StatCard: React.FC<StatCardProps> = ({ title, value, icon, color, trend, onClick }) => (
  <Card sx={{ cursor: onClick ? 'pointer' : 'default' }} onClick={onClick}>
    <CardContent>
      <Box display="flex" alignItems="center" justifyContent="space-between">
        <Box>
          <Typography color="textSecondary" gutterBottom variant="body2">
            {title}
          </Typography>
          <Typography variant="h4" component="h2">
            {value}
          </Typography>
          {trend && (
            <Box display="flex" alignItems="center" mt={1}>
              {trend.isPositive ? (
                <TrendingUpIcon sx={{ color: 'success.main', fontSize: 16, mr: 0.5 }} />
              ) : (
                <TrendingDownIcon sx={{ color: 'error.main', fontSize: 16, mr: 0.5 }} />
              )}
              <Typography
                variant="body2"
                sx={{ color: trend.isPositive ? 'success.main' : 'error.main' }}
              >
                {trend.value}% {trend.period}
              </Typography>
            </Box>
          )}
        </Box>
        <Box
          sx={{
            backgroundColor: color,
            borderRadius: '50%',
            p: 2,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
        >
          {React.cloneElement(icon, { sx: { color: 'white', fontSize: 32 } })}
        </Box>
      </Box>
    </CardContent>
  </Card>
);

// Quick Action Button Component
interface QuickActionProps {
  title: string;
  icon: React.ReactElement;
  color: string;
  onClick: () => void;
}

const QuickAction: React.FC<QuickActionProps> = ({ title, icon, color, onClick }) => (
  <Button
    variant="outlined"
    startIcon={icon}
    onClick={onClick}
    sx={{
      borderColor: color,
      color: color,
      '&:hover': {
        borderColor: color,
        backgroundColor: `${color}10`
      },
      mb: 1,
      width: '100%',
      justifyContent: 'flex-start'
    }}
  >
    {title}
  </Button>
);

// Recent Activity Item Component
interface ActivityItemProps {
  type: 'booking' | 'checkin' | 'checkout' | 'staff' | 'maintenance' | 'other';
  title: string;
  description: string;
  time: string;
  user?: string;
}

const ActivityItem: React.FC<ActivityItemProps> = ({ type, title, description, time, user }) => {
  const getIcon = () => {
    switch (type) {
      case 'booking': return <BookingIcon sx={{ color: '#1976d2' }} />;
      case 'checkin': return <CheckIcon sx={{ color: '#388e3c' }} />;
      case 'checkout': return <CheckIcon sx={{ color: '#f57c00' }} />;
      case 'staff': return <PeopleIcon sx={{ color: '#7b1fa2' }} />;
      case 'maintenance': return <MaintenanceIcon sx={{ color: '#d32f2f' }} />;
      default: return <NotificationIcon sx={{ color: '#757575' }} />;
    }
  };

  return (
    <ListItem>
      <ListItemIcon>
        {getIcon()}
      </ListItemIcon>
      <ListItemText
        primary={title}
        secondary={
          <Box>
            <Typography variant="body2" color="textSecondary">
              {description}
            </Typography>
            <Typography variant="caption" color="textSecondary">
              {time} {user && `• by ${user}`}
            </Typography>
          </Box>
        }
      />
    </ListItem>
  );
};

const Dashboard: React.FC = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [stats, setStats] = useState({
    totalHotels: 0,
    totalStaff: 0,
    totalBookings: 0,
    totalRevenue: 0,
    occupancyRate: 0,
    availableRooms: 0,
    pendingTasks: 0,
    todayCheckIns: 0,
    todayCheckOuts: 0
  });

  const [recentActivities, setRecentActivities] = useState<ActivityItemProps[]>([]);
  const [roomStatus, setRoomStatus] = useState({
    available: 0,
    occupied: 0,
    maintenance: 0,
    cleaning: 0
  });

  const [upcomingTasks, setUpcomingTasks] = useState<any[]>([]);
  const [notifications, setNotifications] = useState<any[]>([]);

  useEffect(() => {
    const fetchDashboardData = async () => {
      if (!user) return;

      try {
        setLoading(true);
        setError(null);

        let totalHotels = 0;
        let totalStaff = 0;
        let totalBookings = 0;
        let totalRevenue = 0;
        let occupancyRate = 0;
        let availableRooms = 0;
        let pendingTasks = 0;
        let todayCheckIns = 0;
        let todayCheckOuts = 0;

        // Mock recent activities
        const mockActivities: ActivityItemProps[] = [
          {
            type: 'booking',
            title: 'New Booking Received',
            description: 'Room 101 booked for 3 nights',
            time: '2 minutes ago',
            user: 'John Doe'
          },
          {
            type: 'checkin',
            title: 'Guest Checked In',
            description: 'Room 205 - Sarah Wilson',
            time: '15 minutes ago',
            user: 'Front Desk'
          },
          {
            type: 'maintenance',
            title: 'Maintenance Request',
            description: 'AC repair needed in Room 301',
            time: '1 hour ago',
            user: 'Housekeeping'
          },
          {
            type: 'checkout',
            title: 'Guest Checked Out',
            description: 'Room 150 - Michael Brown',
            time: '2 hours ago',
            user: 'Front Desk'
          }
        ];

        if (user.role === 'super_admin') {
          // Super admin sees all data
          totalHotels = 25;
          totalStaff = 150;
          totalBookings = 500;
          totalRevenue = 125000;
          occupancyRate = 78;
          availableRooms = 45;
          pendingTasks = 12;
          todayCheckIns = 18;
          todayCheckOuts = 15;
        } else if (user.role === 'admin' || user.role === 'vendor') {
          // Vendor/Admin sees their hotels' data
          if (user.vendorId) {
            const hotels = await getHotelsByVendor(user.vendorId);
            totalHotels = hotels.length;

            // Aggregate stats from all hotels
            for (const hotel of hotels) {
              try {
                const hotelStats = await getHotelStats(hotel.id);
                const staffStats = await getStaffStats(hotel.id);
                const bookingStats = await getBookingStats(hotel.id);

                totalStaff += staffStats.totalStaff;
                totalBookings += bookingStats.totalBookings;
                totalRevenue += bookingStats.totalRevenue;

                // Mock additional data
                occupancyRate = 75;
                availableRooms = 12;
                pendingTasks = 5;
                todayCheckIns = 8;
                todayCheckOuts = 6;
              } catch (err) {
                console.error(`Error fetching stats for hotel ${hotel.id}:`, err);
              }
            }
          }
        } else if (user.role === 'staff') {
          // Staff sees limited data for their hotel
          if (user.hotelId) {
            totalHotels = 1;
            const staffStats = await getStaffStats(user.hotelId);
            const bookingStats = await getBookingStats(user.hotelId);

            totalStaff = staffStats.totalStaff;
            totalBookings = bookingStats.totalBookings;
            totalRevenue = bookingStats.totalRevenue;
            occupancyRate = 72;
            availableRooms = 8;
            pendingTasks = 3;
            todayCheckIns = 4;
            todayCheckOuts = 3;
          }
        }

        setStats({
          totalHotels,
          totalStaff,
          totalBookings,
          totalRevenue,
          occupancyRate,
          availableRooms,
          pendingTasks,
          todayCheckIns,
          todayCheckOuts
        });

        setRecentActivities(mockActivities);

        setRoomStatus({
          available: availableRooms,
          occupied: Math.floor(availableRooms * 1.5),
          maintenance: 2,
          cleaning: 3
        });

        // Mock upcoming tasks
        setUpcomingTasks([
          { id: 1, title: 'Room 101 Checkout', time: '11:00 AM', type: 'checkout' },
          { id: 2, title: 'Room 205 Cleaning', time: '12:30 PM', type: 'cleaning' },
          { id: 3, title: 'Staff Meeting', time: '2:00 PM', type: 'meeting' },
          { id: 4, title: 'Room 301 Maintenance', time: '3:30 PM', type: 'maintenance' }
        ]);

        // Mock notifications
        setNotifications([
          { id: 1, message: 'Low inventory: Towels', type: 'warning', time: '10 min ago' },
          { id: 2, message: 'New booking confirmation needed', type: 'info', time: '25 min ago' },
          { id: 3, message: 'Maintenance completed: Room 205', type: 'success', time: '1 hour ago' }
        ]);

      } catch (err: any) {
        console.error('Error fetching dashboard data:', err);
        setError(err.message || 'Failed to load dashboard data');
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, [user]);

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 3 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Box>
          <Typography variant="h4" component="h1">
            Welcome back, {user?.name}!
          </Typography>
          <Typography variant="body1" color="textSecondary">
            Here's your dashboard overview for today
          </Typography>
        </Box>
        <Typography variant="body2" color="textSecondary">
          {format(new Date(), 'EEEE, MMMM do, yyyy')}
        </Typography>
      </Box>

      {/* Main Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Revenue"
            value={`$${stats.totalRevenue.toLocaleString()}`}
            icon={<RevenueIcon />}
            color="#1976d2"
            trend={{ value: 12.5, isPositive: true, period: 'vs last month' }}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Occupancy Rate"
            value={`${stats.occupancyRate}%`}
            icon={<HotelIcon />}
            color="#388e3c"
            trend={{ value: 5.2, isPositive: true, period: 'vs last week' }}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Bookings"
            value={stats.totalBookings}
            icon={<BookingIcon />}
            color="#f57c00"
            trend={{ value: 8.1, isPositive: true, period: 'vs last month' }}
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Available Rooms"
            value={stats.availableRooms}
            icon={<RoomIcon />}
            color="#7b1fa2"
          />
        </Grid>
      </Grid>

      {/* Today's Overview */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="textSecondary" gutterBottom variant="body2">
                    Today's Check-ins
                  </Typography>
                  <Typography variant="h4" component="h2">
                    {stats.todayCheckIns}
                  </Typography>
                </Box>
                <CheckIcon sx={{ color: '#388e3c', fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="textSecondary" gutterBottom variant="body2">
                    Today's Check-outs
                  </Typography>
                  <Typography variant="h4" component="h2">
                    {stats.todayCheckOuts}
                  </Typography>
                </Box>
                <CheckIcon sx={{ color: '#f57c00', fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="textSecondary" gutterBottom variant="body2">
                    Pending Tasks
                  </Typography>
                  <Typography variant="h4" component="h2">
                    {stats.pendingTasks}
                  </Typography>
                </Box>
                <ScheduleIcon sx={{ color: '#d32f2f', fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between">
                <Box>
                  <Typography color="textSecondary" gutterBottom variant="body2">
                    Active Staff
                  </Typography>
                  <Typography variant="h4" component="h2">
                    {stats.totalStaff}
                  </Typography>
                </Box>
                <PeopleIcon sx={{ color: '#7b1fa2', fontSize: 40 }} />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Main Content Grid */}
      <Grid container spacing={3}>
        {/* Recent Activity */}
        <Grid item xs={12} md={6}>
          <Card sx={{ height: '400px' }}>
            <CardContent>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                <Typography variant="h6">
                  Recent Activity
                </Typography>
                <Button size="small" endIcon={<ViewIcon />}>
                  View All
                </Button>
              </Box>
              <List sx={{ maxHeight: '300px', overflow: 'auto' }}>
                {recentActivities.map((activity, index) => (
                  <React.Fragment key={index}>
                    <ActivityItem {...activity} />
                    {index < recentActivities.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Quick Actions */}
        <Grid item xs={12} md={3}>
          <Card sx={{ height: '400px' }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Quick Actions
              </Typography>
              <Box>
                <QuickAction
                  title="New Booking"
                  icon={<AddIcon />}
                  color="#1976d2"
                  onClick={() => {}}
                />
                <QuickAction
                  title="Check-in Guest"
                  icon={<CheckIcon />}
                  color="#388e3c"
                  onClick={() => {}}
                />
                <QuickAction
                  title="Add Staff"
                  icon={<PersonAddIcon />}
                  color="#7b1fa2"
                  onClick={() => {}}
                />
                <QuickAction
                  title="Room Status"
                  icon={<RoomIcon />}
                  color="#f57c00"
                  onClick={() => {}}
                />
                <QuickAction
                  title="Housekeeping"
                  icon={<CleaningIcon />}
                  color="#00796b"
                  onClick={() => {}}
                />
                <QuickAction
                  title="Reports"
                  icon={<ReportIcon />}
                  color="#5d4037"
                  onClick={() => {}}
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Room Status Overview */}
        <Grid item xs={12} md={3}>
          <Card sx={{ height: '400px' }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Room Status
              </Typography>
              <Box>
                <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                  <Box display="flex" alignItems="center">
                    <Box
                      sx={{
                        width: 12,
                        height: 12,
                        borderRadius: '50%',
                        backgroundColor: '#4caf50',
                        mr: 1
                      }}
                    />
                    <Typography variant="body2">Available</Typography>
                  </Box>
                  <Typography variant="h6">{roomStatus.available}</Typography>
                </Box>

                <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                  <Box display="flex" alignItems="center">
                    <Box
                      sx={{
                        width: 12,
                        height: 12,
                        borderRadius: '50%',
                        backgroundColor: '#f44336',
                        mr: 1
                      }}
                    />
                    <Typography variant="body2">Occupied</Typography>
                  </Box>
                  <Typography variant="h6">{roomStatus.occupied}</Typography>
                </Box>

                <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                  <Box display="flex" alignItems="center">
                    <Box
                      sx={{
                        width: 12,
                        height: 12,
                        borderRadius: '50%',
                        backgroundColor: '#ff9800',
                        mr: 1
                      }}
                    />
                    <Typography variant="body2">Cleaning</Typography>
                  </Box>
                  <Typography variant="h6">{roomStatus.cleaning}</Typography>
                </Box>

                <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
                  <Box display="flex" alignItems="center">
                    <Box
                      sx={{
                        width: 12,
                        height: 12,
                        borderRadius: '50%',
                        backgroundColor: '#9e9e9e',
                        mr: 1
                      }}
                    />
                    <Typography variant="body2">Maintenance</Typography>
                  </Box>
                  <Typography variant="h6">{roomStatus.maintenance}</Typography>
                </Box>

                <Divider sx={{ mb: 2 }} />

                <Typography variant="subtitle2" gutterBottom>
                  Upcoming Tasks
                </Typography>
                {upcomingTasks.slice(0, 3).map((task) => (
                  <Box key={task.id} display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                    <Typography variant="body2" sx={{ fontSize: '0.8rem' }}>
                      {task.title}
                    </Typography>
                    <Typography variant="caption" color="textSecondary">
                      {task.time}
                    </Typography>
                  </Box>
                ))}
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Notifications Bar */}
      {notifications.length > 0 && (
        <Card sx={{ mt: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Recent Notifications
            </Typography>
            <Grid container spacing={2}>
              {notifications.map((notification) => (
                <Grid item xs={12} md={4} key={notification.id}>
                  <Alert
                    severity={notification.type}
                    sx={{ mb: 1 }}
                    action={
                      <IconButton size="small">
                        <ViewIcon fontSize="small" />
                      </IconButton>
                    }
                  >
                    <Box>
                      <Typography variant="body2">
                        {notification.message}
                      </Typography>
                      <Typography variant="caption" color="textSecondary">
                        {notification.time}
                      </Typography>
                    </Box>
                  </Alert>
                </Grid>
              ))}
            </Grid>
          </CardContent>
        </Card>
      )}
    </Box>
  );
};

export default Dashboard;
