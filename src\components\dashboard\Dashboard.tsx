import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  CircularProgress,
  Alert
} from '@mui/material';
import {
  Hotel as HotelIcon,
  People as PeopleIcon,
  BookOnline as BookingIcon,
  AttachMoney as RevenueIcon
} from '@mui/icons-material';
import { useAuth } from '../../hooks/useAuth';
import { getHotelsByVendor, getHotelStats } from '../../services/hotelService';
import { getStaffStats } from '../../services/staffService';
import { getBookingStats } from '../../services/bookingService';

interface StatCardProps {
  title: string;
  value: string | number;
  icon: React.ReactElement;
  color: string;
}

const StatCard: React.FC<StatCardProps> = ({ title, value, icon, color }) => (
  <Card>
    <CardContent>
      <Box display="flex" alignItems="center" justifyContent="space-between">
        <Box>
          <Typography color="textSecondary" gutterBottom variant="body2">
            {title}
          </Typography>
          <Typography variant="h4" component="h2">
            {value}
          </Typography>
        </Box>
        <Box
          sx={{
            backgroundColor: color,
            borderRadius: '50%',
            p: 2,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
        >
          {React.cloneElement(icon, { sx: { color: 'white', fontSize: 32 } })}
        </Box>
      </Box>
    </CardContent>
  </Card>
);

const Dashboard: React.FC = () => {
  const { user } = useAuth();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [stats, setStats] = useState({
    totalHotels: 0,
    totalStaff: 0,
    totalBookings: 0,
    totalRevenue: 0
  });

  useEffect(() => {
    const fetchDashboardData = async () => {
      if (!user) return;

      try {
        setLoading(true);
        setError(null);

        let totalHotels = 0;
        let totalStaff = 0;
        let totalBookings = 0;
        let totalRevenue = 0;

        if (user.role === 'super_admin') {
          // Super admin sees all data - would need to implement getAllHotels, etc.
          // For now, showing placeholder data
          totalHotels = 25;
          totalStaff = 150;
          totalBookings = 500;
          totalRevenue = 125000;
        } else if (user.role === 'admin' || user.role === 'vendor') {
          // Vendor/Admin sees their hotels' data
          if (user.vendorId) {
            const hotels = await getHotelsByVendor(user.vendorId);
            totalHotels = hotels.length;

            // Aggregate stats from all hotels
            for (const hotel of hotels) {
              try {
                const hotelStats = await getHotelStats(hotel.id);
                const staffStats = await getStaffStats(hotel.id);
                const bookingStats = await getBookingStats(hotel.id);

                totalStaff += staffStats.totalStaff;
                totalBookings += bookingStats.totalBookings;
                totalRevenue += bookingStats.totalRevenue;
              } catch (err) {
                console.error(`Error fetching stats for hotel ${hotel.id}:`, err);
              }
            }
          }
        } else if (user.role === 'staff') {
          // Staff sees limited data for their hotel
          if (user.hotelId) {
            totalHotels = 1;
            const staffStats = await getStaffStats(user.hotelId);
            const bookingStats = await getBookingStats(user.hotelId);

            totalStaff = staffStats.totalStaff;
            totalBookings = bookingStats.totalBookings;
            totalRevenue = bookingStats.totalRevenue;
          }
        }

        setStats({
          totalHotels,
          totalStaff,
          totalBookings,
          totalRevenue
        });
      } catch (err: any) {
        console.error('Error fetching dashboard data:', err);
        setError(err.message || 'Failed to load dashboard data');
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, [user]);

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 3 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Box>
      <Typography variant="h4" component="h1" gutterBottom>
        Welcome back, {user?.name}!
      </Typography>
      <Typography variant="body1" color="textSecondary" paragraph>
        Here's an overview of your dashboard
      </Typography>

      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Hotels"
            value={stats.totalHotels}
            icon={<HotelIcon />}
            color="#1976d2"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Staff"
            value={stats.totalStaff}
            icon={<PeopleIcon />}
            color="#388e3c"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Bookings"
            value={stats.totalBookings}
            icon={<BookingIcon />}
            color="#f57c00"
          />
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <StatCard
            title="Total Revenue"
            value={`$${stats.totalRevenue.toLocaleString()}`}
            icon={<RevenueIcon />}
            color="#7b1fa2"
          />
        </Grid>
      </Grid>

      <Grid container spacing={3}>
        <Grid item xs={12} md={8}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Recent Activity
              </Typography>
              <Typography variant="body2" color="textSecondary">
                Recent bookings, staff updates, and other activities will be displayed here.
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Quick Actions
              </Typography>
              <Typography variant="body2" color="textSecondary">
                Quick action buttons for common tasks will be displayed here.
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Dashboard;
