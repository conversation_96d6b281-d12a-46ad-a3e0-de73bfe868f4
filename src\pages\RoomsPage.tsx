import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  CardActions,
  Grid,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Bed as RoomIcon,
  Visibility as ViewIcon,
  CheckCircle as AvailableIcon,
  Cancel as OccupiedIcon,
  Build as MaintenanceIcon
} from '@mui/icons-material';
import { useAuth } from '../hooks/useAuth';
import { Room, RoomStatus, RoomType } from '../types';
import { 
  getRoomsByHotel,
  createRoom,
  updateRoom,
  deleteRoom
} from '../services/roomService';
import { getHotelsByVendor } from '../services/hotelService';

const RoomsPage: React.FC = () => {
  const { user } = useAuth();
  const [rooms, setRooms] = useState<Room[]>([]);
  const [hotels, setHotels] = useState<any[]>([]);
  const [selectedHotel, setSelectedHotel] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingRoom, setEditingRoom] = useState<Room | null>(null);
  const [formData, setFormData] = useState({
    roomNumber: '',
    type: 'single' as RoomType,
    status: 'available' as RoomStatus,
    price: 0,
    capacity: 1,
    bedType: '',
    size: 0,
    amenities: [] as string[],
    description: '',
    floor: 1,
    view: ''
  });

  useEffect(() => {
    fetchHotels();
  }, [user]);

  useEffect(() => {
    if (selectedHotel) {
      fetchRooms();
    }
  }, [selectedHotel]);

  const fetchHotels = async () => {
    if (!user) return;

    try {
      let hotelData: any[] = [];
      
      if (user.role === 'super_admin') {
        // For super admin, would need to fetch all hotels
        hotelData = [];
      } else if ((user.role === 'admin' || user.role === 'vendor') && user.vendorId) {
        hotelData = await getHotelsByVendor(user.vendorId);
      }

      setHotels(hotelData);
      if (hotelData.length > 0) {
        setSelectedHotel(hotelData[0].id);
      }
    } catch (err: any) {
      console.error('Error fetching hotels:', err);
      setError(err.message || 'Failed to load hotels');
    }
  };

  const fetchRooms = async () => {
    if (!selectedHotel) return;

    try {
      setLoading(true);
      setError(null);
      const roomData = await getRoomsByHotel(selectedHotel);
      setRooms(roomData);
    } catch (err: any) {
      console.error('Error fetching rooms:', err);
      setError(err.message || 'Failed to load rooms');
    } finally {
      setLoading(false);
    }
  };

  const handleOpenDialog = (room?: Room) => {
    if (room) {
      setEditingRoom(room);
      setFormData({
        roomNumber: room.roomNumber,
        type: room.type,
        status: room.status,
        price: room.price,
        capacity: room.capacity,
        bedType: room.bedType || '',
        size: room.size || 0,
        amenities: room.amenities || [],
        description: room.description || '',
        floor: room.floor || 1,
        view: room.view || ''
      });
    } else {
      setEditingRoom(null);
      setFormData({
        roomNumber: '',
        type: 'single',
        status: 'available',
        price: 0,
        capacity: 1,
        bedType: '',
        size: 0,
        amenities: [],
        description: '',
        floor: 1,
        view: ''
      });
    }
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setEditingRoom(null);
  };

  const handleFormChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async () => {
    if (!user || !selectedHotel) return;

    try {
      const selectedHotelData = hotels.find(h => h.id === selectedHotel);
      
      if (editingRoom) {
        await updateRoom(editingRoom.id, formData);
      } else {
        await createRoom({
          ...formData,
          hotelId: selectedHotel,
          hotelName: selectedHotelData?.name || '',
          currency: 'USD',
          sizeUnit: 'sqm',
          images: [],
          isActive: true
        });
      }
      
      handleCloseDialog();
      fetchRooms();
    } catch (err: any) {
      console.error('Error saving room:', err);
      setError(err.message || 'Failed to save room');
    }
  };

  const handleDelete = async (roomId: string) => {
    if (!window.confirm('Are you sure you want to delete this room?')) return;

    try {
      await deleteRoom(roomId);
      fetchRooms();
    } catch (err: any) {
      console.error('Error deleting room:', err);
      setError(err.message || 'Failed to delete room');
    }
  };

  const getStatusIcon = (status: RoomStatus) => {
    switch (status) {
      case 'available':
        return <AvailableIcon sx={{ color: 'green' }} />;
      case 'occupied':
        return <OccupiedIcon sx={{ color: 'red' }} />;
      case 'maintenance':
        return <MaintenanceIcon sx={{ color: 'orange' }} />;
      default:
        return <AvailableIcon />;
    }
  };

  const getStatusColor = (status: RoomStatus) => {
    switch (status) {
      case 'available':
        return 'success';
      case 'occupied':
        return 'error';
      case 'maintenance':
        return 'warning';
      default:
        return 'default';
    }
  };

  if (loading && !selectedHotel) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1">
          Room Management
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => handleOpenDialog()}
          disabled={!selectedHotel}
        >
          Add Room
        </Button>
      </Box>

      {hotels.length > 1 && (
        <Box mb={3}>
          <FormControl sx={{ minWidth: 200 }}>
            <InputLabel>Select Hotel</InputLabel>
            <Select
              value={selectedHotel}
              label="Select Hotel"
              onChange={(e) => setSelectedHotel(e.target.value)}
            >
              {hotels.map((hotel) => (
                <MenuItem key={hotel.id} value={hotel.id}>
                  {hotel.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Box>
      )}

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {loading ? (
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <CircularProgress />
        </Box>
      ) : (
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Room Number</TableCell>
                <TableCell>Type</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Price</TableCell>
                <TableCell>Capacity</TableCell>
                <TableCell>Floor</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {rooms.map((room) => (
                <TableRow key={room.id}>
                  <TableCell>
                    <Box display="flex" alignItems="center">
                      <RoomIcon sx={{ mr: 1 }} />
                      {room.roomNumber}
                    </Box>
                  </TableCell>
                  <TableCell>
                    <Chip 
                      label={room.type.charAt(0).toUpperCase() + room.type.slice(1)} 
                      variant="outlined" 
                    />
                  </TableCell>
                  <TableCell>
                    <Box display="flex" alignItems="center">
                      {getStatusIcon(room.status)}
                      <Chip 
                        label={room.status.charAt(0).toUpperCase() + room.status.slice(1)}
                        color={getStatusColor(room.status) as any}
                        size="small"
                        sx={{ ml: 1 }}
                      />
                    </Box>
                  </TableCell>
                  <TableCell>${room.price}/night</TableCell>
                  <TableCell>{room.capacity} guests</TableCell>
                  <TableCell>Floor {room.floor}</TableCell>
                  <TableCell>
                    <IconButton
                      size="small"
                      onClick={() => handleOpenDialog(room)}
                      color="primary"
                    >
                      <EditIcon />
                    </IconButton>
                    <IconButton
                      size="small"
                      onClick={() => handleDelete(room.id)}
                      color="error"
                    >
                      <DeleteIcon />
                    </IconButton>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      {/* Add/Edit Room Dialog */}
      <Dialog open={dialogOpen} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingRoom ? 'Edit Room' : 'Add New Room'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Room Number"
                value={formData.roomNumber}
                onChange={(e) => handleFormChange('roomNumber', e.target.value)}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Room Type</InputLabel>
                <Select
                  value={formData.type}
                  label="Room Type"
                  onChange={(e) => handleFormChange('type', e.target.value)}
                >
                  <MenuItem value="single">Single</MenuItem>
                  <MenuItem value="double">Double</MenuItem>
                  <MenuItem value="suite">Suite</MenuItem>
                  <MenuItem value="deluxe">Deluxe</MenuItem>
                  <MenuItem value="oceanview">Ocean View</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Status</InputLabel>
                <Select
                  value={formData.status}
                  label="Status"
                  onChange={(e) => handleFormChange('status', e.target.value)}
                >
                  <MenuItem value="available">Available</MenuItem>
                  <MenuItem value="occupied">Occupied</MenuItem>
                  <MenuItem value="maintenance">Maintenance</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Price per Night"
                type="number"
                value={formData.price}
                onChange={(e) => handleFormChange('price', Number(e.target.value))}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Capacity"
                type="number"
                value={formData.capacity}
                onChange={(e) => handleFormChange('capacity', Number(e.target.value))}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Floor"
                type="number"
                value={formData.floor}
                onChange={(e) => handleFormChange('floor', Number(e.target.value))}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Description"
                multiline
                rows={3}
                value={formData.description}
                onChange={(e) => handleFormChange('description', e.target.value)}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button onClick={handleSubmit} variant="contained">
            {editingRoom ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default RoomsPage;
