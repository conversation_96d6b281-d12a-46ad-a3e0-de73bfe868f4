import {
  collection,
  doc,
  getDoc,
  getDocs,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit as firestoreLimit,
  Timestamp,
  serverTimestamp
} from 'firebase/firestore';
import { db } from '../firebase/config';
import { StaffMember, StaffFilterOptions, StaffRole } from '../types';
import { handleIndexError, isIndexError } from '../utils/firestoreIndexHelper';

// Collection name
const STAFF_COLLECTION = 'staff';

/**
 * Get all staff for a hotel
 */
export const getStaffForHotel = async (
  hotelId: string,
  options?: StaffFilterOptions
): Promise<StaffMember[]> => {
  try {
    // Simplified query to avoid composite index requirement
    const staffQuery = query(
      collection(db, STAFF_COLLECTION),
      where('hotelId', '==', hotelId)
    );

    const querySnapshot = await getDocs(staffQuery);
    let staff: StaffMember[] = [];

    querySnapshot.forEach((doc) => {
      staff.push({
        id: doc.id,
        ...doc.data() as Omit<StaffMember, 'id'>
      });
    });

    // Apply filters in JavaScript to avoid composite index requirements
    if (options?.role) {
      staff = staff.filter(member => member.role === options.role);
    }

    if (options?.status) {
      staff = staff.filter(member => member.status === options.status);
    }

    // Sort by name in JavaScript
    staff.sort((a, b) => a.name.localeCompare(b.name));

    // Apply limit if provided
    if (options?.limit) {
      staff = staff.slice(0, options.limit);
    }

    return staff;
  } catch (error) {
    if (isIndexError(error)) {
      const indexInfo = handleIndexError(error, 'staff queries');
      console.warn('Firestore index required for staff queries:', indexInfo);

      if (indexInfo.indexUrl) {
        console.log('Create index here:', indexInfo.indexUrl);
      }
    }

    console.error('Error getting staff for hotel:', error);
    throw error;
  }
};

/**
 * Get a staff member by ID
 */
export const getStaffMemberById = async (staffId: string): Promise<StaffMember | null> => {
  try {
    const docRef = doc(db, STAFF_COLLECTION, staffId);
    const docSnap = await getDoc(docRef);
    
    if (docSnap.exists()) {
      return {
        id: docSnap.id,
        ...docSnap.data() as Omit<StaffMember, 'id'>
      };
    } else {
      return null;
    }
  } catch (error) {
    console.error('Error getting staff member by ID:', error);
    throw error;
  }
};

/**
 * Create a new staff member
 */
export const createStaffMember = async (
  staffMember: Omit<StaffMember, 'id' | 'createdAt' | 'updatedAt'>
): Promise<StaffMember> => {
  try {
    const now = Timestamp.now();
    const newStaffMember: Omit<StaffMember, 'id'> = {
      ...staffMember,
      status: 'active',
      createdAt: now,
      updatedAt: now
    };
    
    const docRef = await addDoc(collection(db, STAFF_COLLECTION), newStaffMember);
    
    return {
      ...newStaffMember,
      id: docRef.id
    };
  } catch (error) {
    console.error('Error creating staff member:', error);
    throw error;
  }
};

/**
 * Update a staff member
 */
export const updateStaffMember = async (
  staffId: string,
  updates: Partial<Omit<StaffMember, 'id' | 'createdAt' | 'updatedAt'>>
): Promise<void> => {
  try {
    const updateData = {
      ...updates,
      updatedAt: serverTimestamp()
    };
    
    await updateDoc(doc(db, STAFF_COLLECTION, staffId), updateData);
  } catch (error) {
    console.error('Error updating staff member:', error);
    throw error;
  }
};

/**
 * Delete a staff member (soft delete)
 */
export const deleteStaffMember = async (staffId: string): Promise<void> => {
  try {
    await updateDoc(doc(db, STAFF_COLLECTION, staffId), {
      status: 'terminated',
      updatedAt: serverTimestamp()
    });
  } catch (error) {
    console.error('Error deleting staff member:', error);
    throw error;
  }
};

/**
 * Get staff by role
 */
export const getStaffByRole = async (
  hotelId: string,
  role: StaffRole,
  options?: StaffFilterOptions
): Promise<StaffMember[]> => {
  try {
    let staffQuery = query(
      collection(db, STAFF_COLLECTION),
      where('hotelId', '==', hotelId),
      where('role', '==', role),
      orderBy('name', 'asc')
    );
    
    // Apply status filter if provided
    if (options?.status) {
      staffQuery = query(
        staffQuery,
        where('status', '==', options.status)
      );
    }
    
    // Apply limit if provided
    if (options?.limit) {
      staffQuery = query(
        staffQuery,
        firestoreLimit(options.limit)
      );
    }
    
    const querySnapshot = await getDocs(staffQuery);
    const staff: StaffMember[] = [];
    
    querySnapshot.forEach((doc) => {
      staff.push({
        id: doc.id,
        ...doc.data() as Omit<StaffMember, 'id'>
      });
    });
    
    return staff;
  } catch (error) {
    console.error('Error getting staff by role:', error);
    throw error;
  }
};

/**
 * Get staff statistics for a hotel
 */
export const getStaffStats = async (hotelId: string) => {
  try {
    const staff = await getStaffForHotel(hotelId);

    const departmentBreakdown: Record<string, number> = {};
    staff.forEach((member: StaffMember) => {
      const dept = member.department || 'Other';
      departmentBreakdown[dept] = (departmentBreakdown[dept] || 0) + 1;
    });

    const stats = {
      totalStaff: staff.length,
      activeStaff: staff.filter((s: StaffMember) => s.status === 'active').length,
      inactiveStaff: staff.filter((s: StaffMember) => s.status === 'inactive').length,
      departmentBreakdown
    };

    return stats;
  } catch (error) {
    console.error('Error getting staff stats:', error);
    throw error;
  }
};

/**
 * Search staff members
 */
export const searchStaff = async (
  hotelId: string,
  searchTerm: string,
  options?: StaffFilterOptions
): Promise<StaffMember[]> => {
  try {
    const staffQuery = query(
      collection(db, STAFF_COLLECTION),
      where('hotelId', '==', hotelId),
      orderBy('name', 'asc')
    );
    
    const querySnapshot = await getDocs(staffQuery);
    const staff: StaffMember[] = [];
    
    querySnapshot.forEach((doc) => {
      const staffData = {
        id: doc.id,
        ...doc.data() as Omit<StaffMember, 'id'>
      };
      
      // Filter by search term
      const searchLower = searchTerm.toLowerCase();
      if (
        staffData.name.toLowerCase().includes(searchLower) ||
        staffData.email.toLowerCase().includes(searchLower) ||
        staffData.role.toLowerCase().includes(searchLower) ||
        staffData.department.toLowerCase().includes(searchLower)
      ) {
        staff.push(staffData);
      }
    });
    
    // Apply additional filters
    let filteredStaff = staff;
    
    if (options?.role) {
      filteredStaff = filteredStaff.filter(s => s.role === options.role);
    }
    
    if (options?.status) {
      filteredStaff = filteredStaff.filter(s => s.status === options.status);
    }
    
    if (options?.department) {
      filteredStaff = filteredStaff.filter(s => s.department === options.department);
    }
    
    // Apply limit if specified
    if (options?.limit) {
      return filteredStaff.slice(0, options.limit);
    }
    
    return filteredStaff;
  } catch (error) {
    console.error('Error searching staff:', error);
    throw error;
  }
};


