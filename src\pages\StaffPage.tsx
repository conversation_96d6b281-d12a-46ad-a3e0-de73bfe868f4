import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  CardActions,
  Grid,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Alert,
  Avatar,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Person as PersonIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  Work as WorkIcon
} from '@mui/icons-material';
import { useAuth } from '../hooks/useAuth';
import { StaffMember, StaffRole } from '../types';
import {
  getStaffForHotel,
  createStaffMember,
  updateStaffMember,
  deleteStaffMember
} from '../services/staffService';
import { getHotelsByVendor } from '../services/hotelService';
import { Timestamp } from 'firebase/firestore';

const StaffPage: React.FC = () => {
  const { user } = useAuth();
  const [staff, setStaff] = useState<StaffMember[]>([]);
  const [hotels, setHotels] = useState<any[]>([]);
  const [selectedHotel, setSelectedHotel] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingStaff, setEditingStaff] = useState<StaffMember | null>(null);
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    role: 'receptionist' as StaffRole,
    department: '',
    salary: 0,
    address: '',
    emergencyContact: {
      name: '',
      phone: '',
      relationship: ''
    }
  });

  useEffect(() => {
    fetchHotels();
  }, [user]);

  useEffect(() => {
    if (selectedHotel) {
      fetchStaff();
    }
  }, [selectedHotel]);

  const fetchHotels = async () => {
    if (!user) return;

    try {
      let hotelData: any[] = [];
      
      if (user.role === 'super_admin') {
        // For super admin, would need to fetch all hotels
        hotelData = [];
      } else if ((user.role === 'admin' || user.role === 'vendor') && user.vendorId) {
        hotelData = await getHotelsByVendor(user.vendorId);
      } else if (user.hotelId) {
        // For staff, just show their hotel
        hotelData = [{ id: user.hotelId, name: 'Your Hotel' }];
      }

      setHotels(hotelData);
      if (hotelData.length > 0) {
        setSelectedHotel(hotelData[0].id);
      }
    } catch (err: any) {
      console.error('Error fetching hotels:', err);
      setError(err.message || 'Failed to load hotels');
    }
  };

  const fetchStaff = async () => {
    if (!selectedHotel) return;

    try {
      setLoading(true);
      setError(null);

      const staffData = await getStaffForHotel(selectedHotel);
      setStaff(staffData);
    } catch (err: any) {
      console.error('Error fetching staff:', err);
      setError(err.message || 'Failed to load staff');
    } finally {
      setLoading(false);
    }
  };

  const handleOpenDialog = (staffMember?: StaffMember) => {
    if (staffMember) {
      setEditingStaff(staffMember);
      setFormData({
        name: staffMember.name,
        email: staffMember.email,
        phone: staffMember.phone,
        role: staffMember.role,
        department: staffMember.department,
        salary: staffMember.salary,
        address: staffMember.address || '',
        emergencyContact: staffMember.emergencyContact || {
          name: '',
          phone: '',
          relationship: ''
        }
      });
    } else {
      setEditingStaff(null);
      setFormData({
        name: '',
        email: '',
        phone: '',
        role: 'receptionist',
        department: '',
        salary: 0,
        address: '',
        emergencyContact: {
          name: '',
          phone: '',
          relationship: ''
        }
      });
    }
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setEditingStaff(null);
  };

  const handleFormChange = (field: string, value: any) => {
    if (field.startsWith('emergencyContact.')) {
      const contactField = field.split('.')[1];
      setFormData(prev => ({
        ...prev,
        emergencyContact: {
          ...prev.emergencyContact,
          [contactField]: value
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: value
      }));
    }
  };

  const handleSubmit = async () => {
    if (!user || !selectedHotel) return;

    try {
      const staffData = {
        ...formData,
        hotelId: selectedHotel,
        vendorId: user.vendorId || user.id,
        hireDate: Timestamp.fromDate(new Date()),
        status: 'active' as const
      };

      if (editingStaff) {
        await updateStaffMember(editingStaff.id, staffData);
      } else {
        await createStaffMember(staffData);
      }
      
      handleCloseDialog();
      fetchStaff();
    } catch (err: any) {
      console.error('Error saving staff member:', err);
      setError(err.message || 'Failed to save staff member');
    }
  };

  const handleDelete = async (staffId: string) => {
    if (!window.confirm('Are you sure you want to delete this staff member?')) return;

    try {
      await deleteStaffMember(staffId);
      fetchStaff();
    } catch (err: any) {
      console.error('Error deleting staff member:', err);
      setError(err.message || 'Failed to delete staff member');
    }
  };

  const getRoleColor = (role: StaffRole) => {
    const colors = {
      manager: 'primary',
      receptionist: 'secondary',
      housekeeping: 'success',
      maintenance: 'warning',
      security: 'error',
      chef: 'info',
      waiter: 'default'
    };
    return colors[role] || 'default';
  };

  if (loading && !selectedHotel) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1">
          Staff Management
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => handleOpenDialog()}
          disabled={!selectedHotel}
        >
          Add Staff Member
        </Button>
      </Box>

      {hotels.length > 1 && (
        <Box mb={3}>
          <FormControl sx={{ minWidth: 200 }}>
            <InputLabel>Select Hotel</InputLabel>
            <Select
              value={selectedHotel}
              label="Select Hotel"
              onChange={(e) => setSelectedHotel(e.target.value)}
            >
              {hotels.map((hotel) => (
                <MenuItem key={hotel.id} value={hotel.id}>
                  {hotel.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Box>
      )}

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {selectedHotel && (
        <TableContainer component={Paper}>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Staff Member</TableCell>
                <TableCell>Role</TableCell>
                <TableCell>Department</TableCell>
                <TableCell>Contact</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {loading ? (
                <TableRow>
                  <TableCell colSpan={6} align="center">
                    <CircularProgress />
                  </TableCell>
                </TableRow>
              ) : staff.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} align="center">
                    No staff members found
                  </TableCell>
                </TableRow>
              ) : (
                staff.map((staffMember) => (
                  <TableRow key={staffMember.id}>
                    <TableCell>
                      <Box display="flex" alignItems="center">
                        <Avatar sx={{ mr: 2 }}>
                          {staffMember.name.charAt(0).toUpperCase()}
                        </Avatar>
                        <Box>
                          <Typography variant="body1" fontWeight="medium">
                            {staffMember.name}
                          </Typography>
                          <Typography variant="body2" color="textSecondary">
                            {staffMember.email}
                          </Typography>
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={staffMember.role.replace('_', ' ')}
                        color={getRoleColor(staffMember.role) as any}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>{staffMember.department}</TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {staffMember.phone}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={staffMember.status}
                        color={staffMember.status === 'active' ? 'success' : 'default'}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <IconButton
                        size="small"
                        onClick={() => handleOpenDialog(staffMember)}
                        color="primary"
                      >
                        <EditIcon />
                      </IconButton>
                      <IconButton
                        size="small"
                        onClick={() => handleDelete(staffMember.id)}
                        color="error"
                      >
                        <DeleteIcon />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </TableContainer>
      )}

      {/* Add/Edit Staff Dialog */}
      <Dialog open={dialogOpen} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingStaff ? 'Edit Staff Member' : 'Add New Staff Member'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Full Name"
                value={formData.name}
                onChange={(e) => handleFormChange('name', e.target.value)}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Email"
                type="email"
                value={formData.email}
                onChange={(e) => handleFormChange('email', e.target.value)}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Phone"
                value={formData.phone}
                onChange={(e) => handleFormChange('phone', e.target.value)}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Role</InputLabel>
                <Select
                  value={formData.role}
                  label="Role"
                  onChange={(e) => handleFormChange('role', e.target.value)}
                >
                  <MenuItem value="manager">Manager</MenuItem>
                  <MenuItem value="receptionist">Receptionist</MenuItem>
                  <MenuItem value="housekeeping">Housekeeping</MenuItem>
                  <MenuItem value="maintenance">Maintenance</MenuItem>
                  <MenuItem value="security">Security</MenuItem>
                  <MenuItem value="chef">Chef</MenuItem>
                  <MenuItem value="waiter">Waiter</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Department"
                value={formData.department}
                onChange={(e) => handleFormChange('department', e.target.value)}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Salary"
                type="number"
                value={formData.salary}
                onChange={(e) => handleFormChange('salary', Number(e.target.value))}
                required
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Address"
                multiline
                rows={2}
                value={formData.address}
                onChange={(e) => handleFormChange('address', e.target.value)}
              />
            </Grid>
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Emergency Contact
              </Typography>
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                label="Contact Name"
                value={formData.emergencyContact.name}
                onChange={(e) => handleFormChange('emergencyContact.name', e.target.value)}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                label="Contact Phone"
                value={formData.emergencyContact.phone}
                onChange={(e) => handleFormChange('emergencyContact.phone', e.target.value)}
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                label="Relationship"
                value={formData.emergencyContact.relationship}
                onChange={(e) => handleFormChange('emergencyContact.relationship', e.target.value)}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button onClick={handleSubmit} variant="contained">
            {editingStaff ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default StaffPage;
