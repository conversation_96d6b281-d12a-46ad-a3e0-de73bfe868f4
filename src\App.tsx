import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { CssBaseline } from '@mui/material';
import { AuthProvider } from './hooks/useAuth';
import ProtectedRoute from './components/auth/ProtectedRoute';
import DashboardLayout from './components/dashboard/DashboardLayout';
import Dashboard from './components/dashboard/Dashboard';
import HotelsPage from './pages/HotelsPage';
import StaffPage from './pages/StaffPage';
import BookingsPage from './pages/BookingsPage';
import SchedulePage from './pages/SchedulePage';
import SetupPage from './components/setup/SetupPage';
import PortalSelector from './components/portals/PortalSelector';
import HotelPortalLogin from './components/portals/HotelPortalLogin';
import StaffPortalLogin from './components/portals/StaffPortalLogin';
import AdminPortalLogin from './components/portals/AdminPortalLogin';

// Create a theme
const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
  },
});

// Placeholder components for routes
const SettingsPage = () => <div>Settings Page - Coming Soon</div>;
const ProfilePage = () => <div>Profile Page - Coming Soon</div>;
const UnauthorizedPage = () => <div>Unauthorized - You don't have permission to access this page</div>;

function App() {
  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <AuthProvider>
        <Router>
          <Routes>
            {/* Portal Selection */}
            <Route path="/" element={<PortalSelector />} />

            {/* Portal Login Routes */}
            <Route path="/hotel-portal" element={<HotelPortalLogin />} />
            <Route path="/staff-portal" element={<StaffPortalLogin />} />
            <Route path="/admin-portal" element={<AdminPortalLogin />} />

            {/* Setup Route */}
            <Route path="/setup" element={<SetupPage />} />
            <Route path="/unauthorized" element={<UnauthorizedPage />} />
            
            {/* Hotel/Vendor Dashboard */}
            <Route
              path="/hotel-dashboard"
              element={
                <ProtectedRoute requiredRoles={['vendor', 'admin']}>
                  <DashboardLayout />
                </ProtectedRoute>
              }
            >
              <Route index element={<Dashboard />} />
              <Route path="hotels" element={<HotelsPage />} />
              <Route path="staff" element={<StaffPage />} />
              <Route path="bookings" element={<BookingsPage />} />
              <Route path="schedule" element={<SchedulePage />} />
              <Route path="settings" element={<SettingsPage />} />
              <Route path="profile" element={<ProfilePage />} />
            </Route>

            {/* Staff Dashboard */}
            <Route
              path="/staff-dashboard"
              element={
                <ProtectedRoute requiredRoles={['staff']}>
                  <DashboardLayout />
                </ProtectedRoute>
              }
            >
              <Route index element={<Dashboard />} />
              <Route path="schedule" element={<SchedulePage />} />
              <Route path="bookings" element={<BookingsPage />} />
              <Route path="profile" element={<ProfilePage />} />
            </Route>

            {/* Admin Dashboard */}
            <Route
              path="/admin-dashboard"
              element={
                <ProtectedRoute requiredRoles={['super_admin']}>
                  <DashboardLayout />
                </ProtectedRoute>
              }
            >
              <Route index element={<Dashboard />} />
              <Route path="hotels" element={<HotelsPage />} />
              <Route path="staff" element={<StaffPage />} />
              <Route path="bookings" element={<BookingsPage />} />
              <Route path="schedule" element={<SchedulePage />} />
              <Route path="settings" element={<SettingsPage />} />
              <Route path="profile" element={<ProfilePage />} />
            </Route>
            
            {/* Catch all route */}
            <Route path="*" element={<Navigate to="/" replace />} />
          </Routes>
        </Router>
      </AuthProvider>
    </ThemeProvider>
  );
}

export default App;
