import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ThemeProvider, createTheme } from '@mui/material/styles';
import { CssBaseline } from '@mui/material';
import { AuthProvider } from './hooks/useAuth';
import { NotificationProvider } from './hooks/useNotification';
import { initializeIfEmpty } from './utils/initializeData';
import ProtectedRoute from './components/auth/ProtectedRoute';
import DashboardLayout from './components/dashboard/DashboardLayout';
import Dashboard from './components/dashboard/Dashboard';
import CompleteDashboard from './pages/CompleteDashboard';
import HotelsPage from './pages/HotelsPage';
import VendorHotelPage from './pages/VendorHotelPage';
import RoomsPage from './pages/RoomsPage';
import VendorRoomsPage from './pages/VendorRoomsPage';
import StaffPage from './pages/StaffPage';
import VendorStaffPage from './pages/VendorStaffPage';
import BookingsPage from './pages/BookingsPage';
import VendorBookingsPage from './pages/VendorBookingsPage';
import SchedulePage from './pages/SchedulePage';
import VendorSchedulePage from './pages/VendorSchedulePage';
import AnalyticsPage from './pages/AnalyticsPage';
import VendorAnalytics from './components/dashboard/VendorAnalytics';
import SettingsPage from './pages/SettingsPage';
import AdminHotelManagement from './pages/AdminHotelManagement';
import BookingManagement from './pages/BookingManagement';
import UserManagement from './pages/UserManagement';
import VendorGuestManagement from './pages/VendorGuestManagement';
import ProfilePage from './pages/ProfilePage';
import SetupPage from './components/setup/SetupPage';
import PortalSelector from './components/portals/PortalSelector';
import HotelPortalLogin from './components/portals/HotelPortalLogin';
import StaffPortalLogin from './components/portals/StaffPortalLogin';
import AdminPortalLogin from './components/portals/AdminPortalLogin';
import TestPage from './pages/TestPage';

// Create a theme
const theme = createTheme({
  palette: {
    primary: {
      main: '#1976d2',
    },
    secondary: {
      main: '#dc004e',
    },
  },
});

// Placeholder components for routes
const UnauthorizedPage = () => <div>Unauthorized - You don't have permission to access this page</div>;

function App() {
  // Initialize database with sample data if empty
  React.useEffect(() => {
    initializeIfEmpty().catch(console.error);
  }, []);

  return (
    <ThemeProvider theme={theme}>
      <CssBaseline />
      <AuthProvider>
        <NotificationProvider>
          <Router>
          <Routes>
            {/* Portal Selection */}
            <Route path="/" element={<PortalSelector />} />

            {/* Portal Login Routes */}
            <Route path="/hotel-portal" element={<HotelPortalLogin />} />
            <Route path="/staff-portal" element={<StaffPortalLogin />} />
            <Route path="/admin-portal" element={<AdminPortalLogin />} />

            {/* Setup Route */}
            <Route path="/setup" element={<SetupPage />} />
            <Route path="/unauthorized" element={<UnauthorizedPage />} />
            
            {/* Vendor Dashboard */}
            <Route
              path="/hotel-dashboard"
              element={
                <ProtectedRoute requiredRoles={['vendor']}>
                  <DashboardLayout />
                </ProtectedRoute>
              }
            >
              <Route index element={<CompleteDashboard />} />
              <Route path="my-hotel" element={<VendorHotelPage />} />
              <Route path="rooms" element={<VendorRoomsPage />} />
              <Route path="staff" element={<VendorStaffPage />} />
              <Route path="bookings" element={<VendorBookingsPage />} />
              <Route path="my-users" element={<VendorGuestManagement />} />
              <Route path="schedule" element={<VendorSchedulePage />} />
              <Route path="analytics" element={<VendorAnalytics />} />
              <Route path="settings" element={<SettingsPage />} />
              <Route path="profile" element={<ProfilePage />} />
            </Route>

            {/* Staff Dashboard */}
            <Route
              path="/staff-dashboard"
              element={
                <ProtectedRoute requiredRoles={['staff']}>
                  <DashboardLayout />
                </ProtectedRoute>
              }
            >
              <Route index element={<CompleteDashboard />} />
              <Route path="schedule" element={<SchedulePage />} />
              <Route path="bookings" element={<BookingsPage />} />
              <Route path="profile" element={<ProfilePage />} />
            </Route>

            {/* Admin Dashboard */}
            <Route
              path="/admin-dashboard"
              element={
                <ProtectedRoute requiredRoles={['super_admin']}>
                  <DashboardLayout />
                </ProtectedRoute>
              }
            >
              <Route index element={<CompleteDashboard />} />
              <Route path="hotels" element={<AdminHotelManagement />} />
              <Route path="bookings" element={<BookingManagement />} />
              <Route path="users" element={<UserManagement />} />
              <Route path="rooms" element={<RoomsPage />} />
              <Route path="staff" element={<StaffPage />} />
              <Route path="schedule" element={<SchedulePage />} />
              <Route path="analytics" element={<AnalyticsPage />} />
              <Route path="settings" element={<SettingsPage />} />
              <Route path="profile" element={<ProfilePage />} />
            </Route>
            
            {/* Test page for Firebase connection verification */}
            <Route path="/test" element={<TestPage />} />

            {/* Catch all route */}
            <Route path="*" element={<Navigate to="/" replace />} />
          </Routes>
          </Router>
        </NotificationProvider>
      </AuthProvider>
    </ThemeProvider>
  );
}

export default App;
