import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  Grid,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  Alert,
  CircularProgress
} from '@mui/material';
import {
  Visibility as ViewIcon,
  Person as PersonIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  Schedule as ScheduleIcon,
  CheckCircle as ActiveIcon,
  Cancel as InactiveIcon,
  Hotel as HotelIcon,
  Group as GroupIcon,
  AttachMoney as MoneyIcon,
  BookOnline as BookingIcon
} from '@mui/icons-material';

import { useAuth } from '../hooks/useAuth';
import { Booking } from '../types';
import { getBookingsForHotel } from '../services/bookingService';
import { format } from 'date-fns';

interface Guest {
  id: string;
  name: string;
  email: string;
  phone: string;
  totalBookings: number;
  lastBooking: Date;
  totalSpent: number;
  status: 'active' | 'inactive';
  bookings: Booking[];
}

const VendorGuestManagement: React.FC = () => {
  const { user } = useAuth();
  const [guests, setGuests] = useState<Guest[]>([]);
  const [loading, setLoading] = useState(true);
  const [viewDialogOpen, setViewDialogOpen] = useState(false);
  const [viewingGuest, setViewingGuest] = useState<Guest | null>(null);
  const [tabValue, setTabValue] = useState(0);

  // Mock data for vendor's hotel guests
  const mockGuests: Guest[] = [
    {
      id: '1',
      name: 'John Smith',
      email: '<EMAIL>',
      phone: '+****************',
      totalBookings: 3,
      lastBooking: new Date('2024-01-20'),
      totalSpent: 1250.00,
      status: 'active',
      bookings: []
    },
    {
      id: '2',
      name: 'Sarah Johnson',
      email: '<EMAIL>',
      phone: '+****************',
      totalBookings: 5,
      lastBooking: new Date('2024-01-18'),
      totalSpent: 2100.00,
      status: 'active',
      bookings: []
    },
    {
      id: '3',
      name: 'Mike Wilson',
      email: '<EMAIL>',
      phone: '+****************',
      totalBookings: 2,
      lastBooking: new Date('2024-01-15'),
      totalSpent: 800.00,
      status: 'active',
      bookings: []
    },
    {
      id: '4',
      name: 'Emily Davis',
      email: '<EMAIL>',
      phone: '+****************',
      totalBookings: 1,
      lastBooking: new Date('2024-01-10'),
      totalSpent: 450.00,
      status: 'inactive',
      bookings: []
    }
  ];

  const fetchGuests = async () => {
    if (!user?.hotelId) {
      console.warn('Vendor user does not have hotelId assigned');
      setGuests(mockGuests);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      
      // Get all bookings for this hotel
      const bookings = await getBookingsForHotel(user.hotelId);
      
      // Group bookings by guest to create guest profiles
      const guestMap = new Map<string, Guest>();
      
      bookings.forEach(booking => {
        const guestKey = booking.guestEmail.toLowerCase();
        
        if (guestMap.has(guestKey)) {
          const existingGuest = guestMap.get(guestKey)!;
          existingGuest.totalBookings += 1;
          existingGuest.totalSpent += booking.totalAmount || 0;
          existingGuest.bookings.push(booking);
          
          // Update last booking date
          const bookingDate = booking.bookingDate instanceof Date ? booking.bookingDate : new Date(booking.bookingDate);
          if (bookingDate > existingGuest.lastBooking) {
            existingGuest.lastBooking = bookingDate;
          }
        } else {
          const newGuest: Guest = {
            id: `guest-${Date.now()}-${Math.random()}`,
            name: booking.guestName,
            email: booking.guestEmail,
            phone: booking.guestPhone,
            totalBookings: 1,
            lastBooking: booking.bookingDate instanceof Date ? booking.bookingDate : new Date(booking.bookingDate),
            totalSpent: booking.totalAmount || 0,
            status: 'active',
            bookings: [booking]
          };
          guestMap.set(guestKey, newGuest);
        }
      });
      
      // Convert map to array and sort by last booking date
      const guestList = Array.from(guestMap.values()).sort((a, b) => 
        b.lastBooking.getTime() - a.lastBooking.getTime()
      );
      
      setGuests(guestList);
    } catch (error) {
      console.error('Error fetching guests:', error);
      // Fallback to mock data
      setGuests(mockGuests);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchGuests();
  }, [user]);

  const handleViewGuest = (guest: Guest) => {
    setViewingGuest(guest);
    setViewDialogOpen(true);
  };

  const handleCloseViewDialog = () => {
    setViewDialogOpen(false);
    setViewingGuest(null);
  };

  // Filter guests based on tab
  const getFilteredGuests = () => {
    switch (tabValue) {
      case 1:
        return guests.filter(guest => guest.status === 'active');
      case 2:
        return guests.filter(guest => guest.status === 'inactive');
      default:
        return guests;
    }
  };

  // Calculate statistics
  const totalGuests = guests.length;
  const activeGuests = guests.filter(guest => guest.status === 'active').length;
  const inactiveGuests = guests.filter(guest => guest.status === 'inactive').length;
  const totalRevenue = guests.reduce((sum, guest) => sum + guest.totalSpent, 0);

  // Show error message if user doesn't have hotelId
  if (user && !user.hotelId) {
    return (
      <Box>
        <Typography variant="h4" component="h1" mb={3}>
          My Hotel Guests
        </Typography>
        <Alert severity="warning" sx={{ mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Hotel Assignment Required
          </Typography>
          <Typography variant="body2">
            Your vendor account is not assigned to a hotel. Please contact the system administrator to assign you to a hotel so you can view guests.
          </Typography>
        </Alert>
        <Card>
          <CardContent>
            <Typography variant="body1" color="text.secondary">
              Once your account is properly configured with a hotel assignment, you'll be able to:
            </Typography>
            <ul>
              <li>View all hotel guests and their booking history</li>
              <li>Track guest spending and loyalty</li>
              <li>Monitor guest activity and preferences</li>
              <li>Analyze guest demographics and trends</li>
            </ul>
          </CardContent>
        </Card>
      </Box>
    );
  }

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1">
          My Hotel Guests
        </Typography>
      </Box>

      {/* Statistics Cards */}
      <Grid container spacing={3} mb={3}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <GroupIcon color="primary" sx={{ fontSize: 40, mr: 2 }} />
                <Box>
                  <Typography variant="h4">{totalGuests}</Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total Guests
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <ActiveIcon color="success" sx={{ fontSize: 40, mr: 2 }} />
                <Box>
                  <Typography variant="h4">{activeGuests}</Typography>
                  <Typography variant="body2" color="text.secondary">
                    Active Guests
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <InactiveIcon color="error" sx={{ fontSize: 40, mr: 2 }} />
                <Box>
                  <Typography variant="h4">{inactiveGuests}</Typography>
                  <Typography variant="body2" color="text.secondary">
                    Inactive Guests
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center">
                <MoneyIcon color="success" sx={{ fontSize: 40, mr: 2 }} />
                <Box>
                  <Typography variant="h4">${totalRevenue.toFixed(0)}</Typography>
                  <Typography variant="body2" color="text.secondary">
                    Total Revenue
                  </Typography>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Tabs */}
      <Card>
        <Tabs value={tabValue} onChange={(e, newValue) => setTabValue(newValue)}>
          <Tab label={`All Guests (${totalGuests})`} />
          <Tab label={`Active (${activeGuests})`} />
          <Tab label={`Inactive (${inactiveGuests})`} />
        </Tabs>

        <CardContent>
          {loading ? (
            <Box display="flex" justifyContent="center" p={3}>
              <CircularProgress />
            </Box>
          ) : (
            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Guest</TableCell>
                    <TableCell>Contact</TableCell>
                    <TableCell>Bookings</TableCell>
                    <TableCell>Last Visit</TableCell>
                    <TableCell>Total Spent</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {getFilteredGuests().map((guest) => (
                    <TableRow key={guest.id}>
                      <TableCell>
                        <Box display="flex" alignItems="center">
                          <PersonIcon sx={{ mr: 1 }} />
                          <Box>
                            <Typography variant="body2" fontWeight="bold">
                              {guest.name}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              ID: {guest.id}
                            </Typography>
                          </Box>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Box>
                          <Typography variant="body2">{guest.email}</Typography>
                          <Typography variant="caption" color="text.secondary">
                            {guest.phone}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={`${guest.totalBookings} bookings`}
                          color="primary"
                          variant="outlined"
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {format(guest.lastBooking, 'MMM dd, yyyy')}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" fontWeight="bold" color="success.main">
                          ${guest.totalSpent.toFixed(2)}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={guest.status}
                          color={guest.status === 'active' ? 'success' : 'error'}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        <IconButton
                          size="small"
                          onClick={() => handleViewGuest(guest)}
                          color="primary"
                        >
                          <ViewIcon />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}
        </CardContent>
      </Card>

      {/* View Guest Dialog */}
      <Dialog open={viewDialogOpen} onClose={handleCloseViewDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          <Box display="flex" alignItems="center">
            <PersonIcon sx={{ mr: 1 }} />
            Guest Details
          </Box>
        </DialogTitle>
        <DialogContent>
          {viewingGuest && (
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <List>
                  <ListItem>
                    <ListItemIcon>
                      <PersonIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Name"
                      secondary={viewingGuest.name}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      <EmailIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Email"
                      secondary={viewingGuest.email}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      <PhoneIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Phone"
                      secondary={viewingGuest.phone}
                    />
                  </ListItem>
                </List>
              </Grid>
              <Grid item xs={12} md={6}>
                <List>
                  <ListItem>
                    <ListItemIcon>
                      <BookingIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Total Bookings"
                      secondary={viewingGuest.totalBookings}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      <ScheduleIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Last Booking"
                      secondary={format(viewingGuest.lastBooking, 'MMM dd, yyyy')}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      <MoneyIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Total Spent"
                      secondary={`$${viewingGuest.totalSpent.toFixed(2)}`}
                    />
                  </ListItem>
                </List>
              </Grid>
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseViewDialog}>Close</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default VendorGuestManagement;
