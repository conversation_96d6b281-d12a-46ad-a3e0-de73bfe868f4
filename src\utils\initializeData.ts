import {
  collection,
  doc,
  setDoc,
  getDocs,
  query,
  where,
  Timestamp
} from 'firebase/firestore';
import {
  createUserWithEmailAndPassword
} from 'firebase/auth';
import { db, auth } from '../firebase/config';

// Sample data for initialization
const sampleData = {
  // Sample hotel data
  hotels: [
    {
      id: 'hotel-1',
      name: 'Grand Plaza Hotel',
      description: 'Luxury hotel in the heart of the city',
      address: '123 Main Street',
      city: 'New York',
      state: 'NY',
      country: 'USA',
      zipCode: '10001',
      phone: '******-1000',
      email: '<EMAIL>',
      website: 'https://grandplaza.com',
      vendorId: 'vendor-1',
      vendorName: 'Hotel Vendor 1',
      rating: 4.5,
      amenities: ['WiFi', 'Pool', 'Gym', 'Restaurant', 'Spa'],
      images: [],
      isActive: true,
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    }
  ],

  // Sample room data
  rooms: [
    {
      id: 'room-1',
      hotelId: 'hotel-1',
      roomNumber: '101',
      type: 'single',
      status: 'available',
      price: 150,
      capacity: 1,
      amenities: ['WiFi', 'TV', 'AC'],
      images: [],
      description: 'Comfortable single room',
      floor: 1,
      isActive: true,
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    },
    {
      id: 'room-2',
      hotelId: 'hotel-1',
      roomNumber: '102',
      type: 'double',
      status: 'available',
      price: 200,
      capacity: 2,
      amenities: ['WiFi', 'TV', 'AC', 'Mini Bar'],
      images: [],
      description: 'Spacious double room',
      floor: 1,
      isActive: true,
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    }
  ],

  // Sample staff data
  staff: [
    {
      id: 'staff-1',
      hotelId: 'hotel-1',
      vendorId: 'vendor-1',
      name: 'John Manager',
      email: '<EMAIL>',
      phone: '******-1001',
      role: 'manager',
      department: 'Management',
      salary: 60000,
      hireDate: Timestamp.now(),
      status: 'active',
      address: '789 Staff Street, New York, NY 10001',
      emergencyContact: {
        name: 'Jane Manager',
        phone: '******-1002',
        relationship: 'Spouse'
      },
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    }
  ]
};

/**
 * Initialize sample data in Firestore
 * This function can be called from the admin panel
 */
export const initializeSampleData = async (): Promise<void> => {
  try {
    console.log('Initializing sample data...');

    // Initialize hotels
    for (const hotel of sampleData.hotels) {
      const { id, ...hotelData } = hotel;
      await setDoc(doc(db, 'hotels', id), hotelData);
      console.log(`Created hotel: ${hotel.name}`);
    }

    // Initialize rooms
    for (const room of sampleData.rooms) {
      const { id, ...roomData } = room;
      await setDoc(doc(db, 'rooms', id), roomData);
      console.log(`Created room: ${room.roomNumber}`);
    }

    // Initialize staff
    for (const staffMember of sampleData.staff) {
      const { id, ...staffData } = staffMember;
      await setDoc(doc(db, 'staff', id), staffData);
      console.log(`Created staff: ${staffMember.name}`);
    }

    console.log('✅ Sample data initialized successfully!');
  } catch (error) {
    console.error('❌ Error initializing sample data:', error);
    throw error;
  }
};

/**
 * Create admin user account
 */
export const createAdminUser = async (email: string, password: string): Promise<void> => {
  try {
    console.log('Creating admin user...');
    
    // Create user account
    const userCredential = await createUserWithEmailAndPassword(auth, email, password);
    const user = userCredential.user;

    // Create user profile in Firestore
    await setDoc(doc(db, 'users', user.uid), {
      email: user.email,
      name: 'Super Administrator',
      role: 'super_admin',
      phone: '******-0001',
      isActive: true,
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    });

    console.log('✅ Admin user created successfully!');
  } catch (error) {
    console.error('❌ Error creating admin user:', error);
    throw error;
  }
};

/**
 * Create vendor user account
 */
export const createVendorUser = async (email: string, password: string): Promise<void> => {
  try {
    console.log('Creating vendor user...');

    // Create user account
    const userCredential = await createUserWithEmailAndPassword(auth, email, password);
    const user = userCredential.user;

    // Create user profile in Firestore
    await setDoc(doc(db, 'users', user.uid), {
      email: user.email,
      name: 'Hotel Vendor',
      role: 'vendor',
      phone: '******-0002',
      vendorId: 'vendor-1',
      hotelId: 'hotel-1', // Assign vendor to the first hotel
      isActive: true,
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    });

    console.log('✅ Vendor user created successfully!');
  } catch (error) {
    console.error('❌ Error creating vendor user:', error);
    throw error;
  }
};

/**
 * Create staff user account
 */
export const createStaffUser = async (email: string, password: string): Promise<void> => {
  try {
    console.log('Creating staff user...');

    // Create user account
    const userCredential = await createUserWithEmailAndPassword(auth, email, password);
    const user = userCredential.user;

    // Create user profile in Firestore
    await setDoc(doc(db, 'users', user.uid), {
      email: user.email,
      name: 'Hotel Staff Member',
      role: 'staff',
      phone: '******-0003',
      hotelId: 'hotel-1',
      vendorId: 'vendor-1',
      isActive: true,
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    });

    console.log('✅ Staff user created successfully!');
  } catch (error) {
    console.error('❌ Error creating staff user:', error);
    throw error;
  }
};

/**
 * Create comprehensive sample data
 */
export const createComprehensiveData = async (): Promise<void> => {
  try {
    console.log('🏨 Creating comprehensive hotel data...');

    // Create hotels
    await setDoc(doc(db, 'hotels', 'hotel-1'), {
      name: 'Grand Plaza Hotel',
      description: 'Luxury hotel in the heart of the city with world-class amenities and exceptional service.',
      address: '123 Main Street',
      city: 'New York',
      state: 'NY',
      country: 'USA',
      zipCode: '10001',
      phone: '******-1000',
      email: '<EMAIL>',
      website: 'https://grandplaza.com',
      vendorId: 'vendor-1',
      vendorName: 'Hotel Vendor',
      rating: 4.5,
      totalRooms: 150,
      availableRooms: 120,
      amenities: ['Free WiFi', 'Swimming Pool', 'Fitness Center', 'Restaurant', 'Spa & Wellness', 'Business Center', 'Room Service', 'Concierge', 'Parking', 'Pet Friendly'],
      images: ['https://images.unsplash.com/photo-1566073771259-6a8506099945', 'https://images.unsplash.com/photo-1564501049412-61c2a3083791'],
      checkInTime: '15:00',
      checkOutTime: '11:00',
      policies: {
        cancellation: 'Free cancellation up to 24 hours before check-in',
        pets: 'Pets allowed with additional fee',
        smoking: 'Non-smoking property'
      },
      isActive: true,
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    });

    await setDoc(doc(db, 'hotels', 'hotel-2'), {
      name: 'Seaside Resort & Spa',
      description: 'Beautiful beachfront resort with stunning ocean views and premium spa services.',
      address: '456 Ocean Drive',
      city: 'Miami',
      state: 'FL',
      country: 'USA',
      zipCode: '33139',
      phone: '******-2000',
      email: '<EMAIL>',
      website: 'https://seasideresort.com',
      vendorId: 'vendor-1',
      vendorName: 'Hotel Vendor',
      rating: 4.8,
      totalRooms: 200,
      availableRooms: 180,
      amenities: ['Beachfront', 'Free WiFi', 'Multiple Pools', 'Spa Services', 'Water Sports', 'Fine Dining', 'Kids Club', 'Tennis Court', 'Valet Parking', '24/7 Room Service'],
      images: ['https://images.unsplash.com/photo-1571896349842-33c89424de2d', 'https://images.unsplash.com/photo-1520250497591-112f2f40a3f4'],
      checkInTime: '16:00',
      checkOutTime: '12:00',
      policies: {
        cancellation: 'Free cancellation up to 48 hours before check-in',
        pets: 'No pets allowed',
        smoking: 'Designated smoking areas only'
      },
      isActive: true,
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    });

    console.log('✅ Hotels created successfully!');

    // Create rooms
    console.log('🛏️ Creating rooms...');

    await setDoc(doc(db, 'rooms', 'room-1'), {
      hotelId: 'hotel-1',
      hotelName: 'Grand Plaza Hotel',
      roomNumber: '101',
      type: 'single',
      status: 'available',
      price: 150,
      currency: 'USD',
      capacity: 1,
      bedType: 'Queen Bed',
      size: 25,
      sizeUnit: 'sqm',
      amenities: ['Free WiFi', 'Smart TV', 'Air Conditioning', 'Mini Bar', 'Coffee Maker', 'Safe', 'Hair Dryer', 'Iron & Board'],
      images: ['https://images.unsplash.com/photo-1631049307264-da0ec9d70304'],
      description: 'Comfortable single room with modern amenities and city view.',
      floor: 1,
      view: 'City View',
      isActive: true,
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    });

    await setDoc(doc(db, 'rooms', 'room-2'), {
      hotelId: 'hotel-1',
      hotelName: 'Grand Plaza Hotel',
      roomNumber: '102',
      type: 'double',
      status: 'available',
      price: 200,
      currency: 'USD',
      capacity: 2,
      bedType: 'King Bed',
      size: 35,
      sizeUnit: 'sqm',
      amenities: ['Free WiFi', 'Smart TV', 'Air Conditioning', 'Mini Bar', 'Coffee Maker', 'Safe', 'Hair Dryer', 'Iron & Board', 'Balcony', 'Sofa'],
      images: ['https://images.unsplash.com/photo-1618773928121-c32242e63f39'],
      description: 'Spacious double room with king bed and private balcony.',
      floor: 1,
      view: 'Garden View',
      isActive: true,
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    });

    await setDoc(doc(db, 'rooms', 'room-3'), {
      hotelId: 'hotel-1',
      hotelName: 'Grand Plaza Hotel',
      roomNumber: '201',
      type: 'suite',
      status: 'occupied',
      price: 350,
      currency: 'USD',
      capacity: 4,
      bedType: 'King + Sofa Bed',
      size: 60,
      sizeUnit: 'sqm',
      amenities: ['Free WiFi', 'Smart TV', 'Air Conditioning', 'Mini Bar', 'Coffee Maker', 'Safe', 'Hair Dryer', 'Iron & Board', 'Balcony', 'Living Area', 'Kitchenette', 'Jacuzzi'],
      images: ['https://images.unsplash.com/photo-1582719478250-c89cae4dc85b'],
      description: 'Luxury suite with separate living area and premium amenities.',
      floor: 2,
      view: 'City View',
      isActive: true,
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    });

    console.log('✅ Rooms created successfully!');

    // Create staff
    console.log('👥 Creating staff...');

    await setDoc(doc(db, 'staff', 'staff-1'), {
      hotelId: 'hotel-1',
      vendorId: 'vendor-1',
      employeeId: 'EMP001',
      name: 'John Manager',
      email: '<EMAIL>',
      phone: '******-1001',
      role: 'manager',
      department: 'Management',
      position: 'General Manager',
      salary: 75000,
      currency: 'USD',
      hireDate: Timestamp.fromDate(new Date('2024-01-15')),
      status: 'active',
      address: '789 Staff Street, New York, NY 10001',
      dateOfBirth: Timestamp.fromDate(new Date('1985-03-20')),
      emergencyContact: {
        name: 'Jane Manager',
        phone: '******-1002',
        relationship: 'Spouse'
      },
      skills: ['Leadership', 'Customer Service', 'Operations Management'],
      certifications: ['Hotel Management Certificate', 'First Aid'],
      workSchedule: {
        monday: { start: '08:00', end: '17:00' },
        tuesday: { start: '08:00', end: '17:00' },
        wednesday: { start: '08:00', end: '17:00' },
        thursday: { start: '08:00', end: '17:00' },
        friday: { start: '08:00', end: '17:00' },
        saturday: { start: '09:00', end: '15:00' },
        sunday: 'off'
      },
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    });

    await setDoc(doc(db, 'staff', 'staff-2'), {
      hotelId: 'hotel-1',
      vendorId: 'vendor-1',
      employeeId: 'EMP002',
      name: 'Sarah Receptionist',
      email: '<EMAIL>',
      phone: '******-1003',
      role: 'receptionist',
      department: 'Front Desk',
      position: 'Front Desk Agent',
      salary: 35000,
      currency: 'USD',
      hireDate: Timestamp.fromDate(new Date('2024-03-10')),
      status: 'active',
      address: '456 Reception Ave, New York, NY 10002',
      dateOfBirth: Timestamp.fromDate(new Date('1992-07-15')),
      emergencyContact: {
        name: 'Mike Johnson',
        phone: '******-1004',
        relationship: 'Brother'
      },
      skills: ['Customer Service', 'Multi-language', 'Computer Skills'],
      certifications: ['Customer Service Excellence'],
      workSchedule: {
        monday: { start: '06:00', end: '14:00' },
        tuesday: { start: '06:00', end: '14:00' },
        wednesday: { start: '06:00', end: '14:00' },
        thursday: { start: '06:00', end: '14:00' },
        friday: { start: '06:00', end: '14:00' },
        saturday: 'off',
        sunday: 'off'
      },
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    });

    console.log('✅ Staff created successfully!');

    // Create bookings
    console.log('📅 Creating bookings...');

    await setDoc(doc(db, 'bookings', 'booking-1'), {
      hotelId: 'hotel-1',
      hotelName: 'Grand Plaza Hotel',
      roomId: 'room-1',
      roomNumber: '101',
      roomType: 'single',
      bookingId: 'BK001',
      userId: 'guest-1',
      guestName: 'Alice Johnson',
      guestEmail: '<EMAIL>',
      guestPhone: '******-3001',
      checkInDate: Timestamp.fromDate(new Date('2025-07-15')),
      checkOutDate: Timestamp.fromDate(new Date('2025-07-18')),
      nights: 3,
      adults: 1,
      children: 0,
      roomRate: 150,
      totalAmount: 450,
      currency: 'USD',
      taxes: 45,
      fees: 15,
      finalAmount: 510,
      status: 'confirmed',
      paymentStatus: 'paid',
      paymentMethod: 'Credit Card',
      specialRequests: 'Late check-in requested, non-smoking room',
      source: 'Direct Booking',
      notes: 'VIP guest, provide welcome amenities',
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    });

    await setDoc(doc(db, 'bookings', 'booking-2'), {
      hotelId: 'hotel-1',
      hotelName: 'Grand Plaza Hotel',
      roomId: 'room-3',
      roomNumber: '201',
      roomType: 'suite',
      bookingId: 'BK002',
      userId: 'guest-2',
      guestName: 'Robert Smith',
      guestEmail: '<EMAIL>',
      guestPhone: '******-3002',
      checkInDate: Timestamp.fromDate(new Date('2025-07-14')),
      checkOutDate: Timestamp.fromDate(new Date('2025-07-16')),
      nights: 2,
      adults: 2,
      children: 1,
      roomRate: 350,
      totalAmount: 700,
      currency: 'USD',
      taxes: 70,
      fees: 25,
      finalAmount: 795,
      status: 'checked-in',
      paymentStatus: 'paid',
      paymentMethod: 'Debit Card',
      specialRequests: 'Extra bed for child, early check-in',
      source: 'Online Travel Agency',
      notes: 'Family with young child, provide child amenities',
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    });

    console.log('✅ Bookings created successfully!');
  } catch (error) {
    console.error('❌ Error creating hotel data:', error);
    throw error;
  }
};

/**
 * Complete initialization - creates users and sample data
 */
export const completeInitialization = async (): Promise<void> => {
  try {
    // Create admin user
    await createAdminUser('<EMAIL>', 'admin123456');

    // Create vendor user
    await createVendorUser('<EMAIL>', 'vendor123456');

    // Create staff user
    await createStaffUser('<EMAIL>', 'staff123456');

    // Initialize sample data
    await initializeSampleData();

    // Create comprehensive data
    await createComprehensiveData();

    console.log('🎉 Complete initialization finished!');
    console.log('');
    console.log('🔴 Super Admin Portal:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: admin123456');
    console.log('');
    console.log('🔵 Hotel/Vendor Portal:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: vendor123456');
    console.log('');
    console.log('🟢 Staff Portal:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: staff123456');

  } catch (error) {
    console.error('❌ Error during complete initialization:', error);
    throw error;
  }
};

/**
 * Check if the database is already initialized
 */
export const isDatabaseInitialized = async (): Promise<boolean> => {
  try {
    // Check if any hotels exist
    const hotelsSnapshot = await getDocs(collection(db, 'hotels'));
    return !hotelsSnapshot.empty;
  } catch (error) {
    console.error('Error checking database initialization:', error);
    return false;
  }
};

/**
 * Fix existing vendor users to have hotelId
 */
export const fixVendorUsers = async (): Promise<void> => {
  try {
    console.log('🔧 Checking and fixing vendor users...');

    // Get all vendor users
    const vendorQuery = query(
      collection(db, 'users'),
      where('role', '==', 'vendor')
    );

    const vendorSnapshot = await getDocs(vendorQuery);

    for (const vendorDoc of vendorSnapshot.docs) {
      const vendorData = vendorDoc.data() as any;

      // If vendor doesn't have hotelId, assign them to hotel-1
      if (!vendorData.hotelId) {
        console.log(`🔧 Fixing vendor user ${vendorData.name} - adding hotelId`);

        await setDoc(doc(db, 'users', vendorDoc.id), {
          ...vendorData,
          hotelId: 'hotel-1',
          updatedAt: Timestamp.now()
        });

        console.log(`✅ Fixed vendor user ${vendorData.name}`);
      }
    }

    console.log('✅ Vendor users check completed!');
  } catch (error) {
    console.error('❌ Error fixing vendor users:', error);
  }
};

/**
 * Initialize database with sample data if it's empty
 */
export const initializeIfEmpty = async (): Promise<void> => {
  try {
    const isInitialized = await isDatabaseInitialized();

    if (!isInitialized) {
      console.log('🚀 Database is empty. Initializing with sample data...');
      await initializeSampleData();
      console.log('✅ Database initialized successfully!');
    } else {
      console.log('✅ Database already contains data. Skipping initialization.');
      // Fix existing vendor users if needed
      await fixVendorUsers();
    }
  } catch (error) {
    console.error('❌ Error during conditional initialization:', error);
    throw error;
  }
};
