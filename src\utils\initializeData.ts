import { 
  collection, 
  doc, 
  setDoc, 
  Timestamp 
} from 'firebase/firestore';
import { 
  createUserWithEmailAndPassword,
  signInWithEmailAndPassword 
} from 'firebase/auth';
import { db, auth } from '../firebase/config';

// Sample data for initialization
const sampleData = {
  // Sample hotel data
  hotels: [
    {
      id: 'hotel-1',
      name: 'Grand Plaza Hotel',
      description: 'Luxury hotel in the heart of the city',
      address: '123 Main Street',
      city: 'New York',
      state: 'NY',
      country: 'USA',
      zipCode: '10001',
      phone: '******-1000',
      email: '<EMAIL>',
      website: 'https://grandplaza.com',
      vendorId: 'vendor-1',
      vendorName: 'Hotel Vendor 1',
      rating: 4.5,
      amenities: ['WiFi', 'Pool', 'Gym', 'Restaurant', 'Spa'],
      images: [],
      isActive: true,
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    }
  ],

  // Sample room data
  rooms: [
    {
      id: 'room-1',
      hotelId: 'hotel-1',
      roomNumber: '101',
      type: 'single',
      status: 'available',
      price: 150,
      capacity: 1,
      amenities: ['WiFi', 'TV', 'AC'],
      images: [],
      description: 'Comfortable single room',
      floor: 1,
      isActive: true,
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    },
    {
      id: 'room-2',
      hotelId: 'hotel-1',
      roomNumber: '102',
      type: 'double',
      status: 'available',
      price: 200,
      capacity: 2,
      amenities: ['WiFi', 'TV', 'AC', 'Mini Bar'],
      images: [],
      description: 'Spacious double room',
      floor: 1,
      isActive: true,
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    }
  ],

  // Sample staff data
  staff: [
    {
      id: 'staff-1',
      hotelId: 'hotel-1',
      vendorId: 'vendor-1',
      name: 'John Manager',
      email: '<EMAIL>',
      phone: '******-1001',
      role: 'manager',
      department: 'Management',
      salary: 60000,
      hireDate: Timestamp.now(),
      status: 'active',
      address: '789 Staff Street, New York, NY 10001',
      emergencyContact: {
        name: 'Jane Manager',
        phone: '******-1002',
        relationship: 'Spouse'
      },
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    }
  ]
};

/**
 * Initialize sample data in Firestore
 * This function can be called from the admin panel
 */
export const initializeSampleData = async (): Promise<void> => {
  try {
    console.log('Initializing sample data...');

    // Initialize hotels
    for (const hotel of sampleData.hotels) {
      const { id, ...hotelData } = hotel;
      await setDoc(doc(db, 'hotels', id), hotelData);
      console.log(`Created hotel: ${hotel.name}`);
    }

    // Initialize rooms
    for (const room of sampleData.rooms) {
      const { id, ...roomData } = room;
      await setDoc(doc(db, 'rooms', id), roomData);
      console.log(`Created room: ${room.roomNumber}`);
    }

    // Initialize staff
    for (const staffMember of sampleData.staff) {
      const { id, ...staffData } = staffMember;
      await setDoc(doc(db, 'staff', id), staffData);
      console.log(`Created staff: ${staffMember.name}`);
    }

    console.log('✅ Sample data initialized successfully!');
  } catch (error) {
    console.error('❌ Error initializing sample data:', error);
    throw error;
  }
};

/**
 * Create admin user account
 */
export const createAdminUser = async (email: string, password: string): Promise<void> => {
  try {
    console.log('Creating admin user...');
    
    // Create user account
    const userCredential = await createUserWithEmailAndPassword(auth, email, password);
    const user = userCredential.user;

    // Create user profile in Firestore
    await setDoc(doc(db, 'users', user.uid), {
      email: user.email,
      name: 'Super Administrator',
      role: 'super_admin',
      phone: '******-0001',
      isActive: true,
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    });

    console.log('✅ Admin user created successfully!');
  } catch (error) {
    console.error('❌ Error creating admin user:', error);
    throw error;
  }
};

/**
 * Create vendor user account
 */
export const createVendorUser = async (email: string, password: string): Promise<void> => {
  try {
    console.log('Creating vendor user...');
    
    // Create user account
    const userCredential = await createUserWithEmailAndPassword(auth, email, password);
    const user = userCredential.user;

    // Create user profile in Firestore
    await setDoc(doc(db, 'users', user.uid), {
      email: user.email,
      name: 'Hotel Vendor',
      role: 'vendor',
      phone: '******-0002',
      vendorId: 'vendor-1',
      isActive: true,
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    });

    console.log('✅ Vendor user created successfully!');
  } catch (error) {
    console.error('❌ Error creating vendor user:', error);
    throw error;
  }
};

/**
 * Complete initialization - creates users and sample data
 */
export const completeInitialization = async (): Promise<void> => {
  try {
    // Create admin user
    await createAdminUser('<EMAIL>', 'admin123456');
    
    // Create vendor user  
    await createVendorUser('<EMAIL>', 'vendor123456');
    
    // Initialize sample data
    await initializeSampleData();
    
    console.log('🎉 Complete initialization finished!');
    console.log('');
    console.log('👤 Admin Login:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: admin123456');
    console.log('');
    console.log('🏨 Vendor Login:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: vendor123456');
    
  } catch (error) {
    console.error('❌ Error during complete initialization:', error);
    throw error;
  }
};
