import { Timestamp } from 'firebase/firestore';
import { RoomType } from './index';

// Enhanced booking status for hotel operations
export type EnhancedBookingStatus = 
  | 'pending'           // Initial booking, awaiting confirmation
  | 'confirmed'         // Booking confirmed, payment received
  | 'checked_in'        // Guest has checked in
  | 'checked_out'       // Guest has checked out
  | 'cancelled'         // Booking cancelled
  | 'no_show'          // Guest didn't show up
  | 'modified'         // Booking has been modified
  | 'expired';         // Booking expired (past check-in date)

// Payment status
export type PaymentStatus = 
  | 'pending'          // Payment not yet processed
  | 'partial'          // Partial payment received
  | 'paid'             // Full payment received
  | 'refunded'         // Payment refunded
  | 'failed'           // Payment failed
  | 'disputed';        // Payment disputed

// Booking source (where the booking came from)
export type BookingSource = 
  | 'direct'           // Hotel's own website/system
  | 'walk_in'          // Walk-in guest
  | 'phone'            // Phone booking
  | 'email'            // Email booking
  | 'booking_com'      // Booking.com
  | 'expedia'          // Expedia
  | 'airbnb'           // Airbnb
  | 'agoda'            // Agoda
  | 'mobile_app'       // Hotel's mobile app
  | 'other_ota';       // Other online travel agency

// Guest details for booking
export interface BookingGuestDetails {
  adults: number;
  children: number;
  infants: number;
  childrenAges?: number[];
  guestNames?: string[]; // Names of all guests
}

// Guest preferences for booking
export interface BookingGuestPreferences {
  bedType?: 'single' | 'double' | 'king' | 'queen' | 'twin';
  smokingPreference?: 'smoking' | 'non_smoking';
  floorPreference?: 'low' | 'high' | 'middle';
  roomLocation?: 'quiet' | 'city_view' | 'ocean_view' | 'garden_view';
  accessibilityNeeds?: string[];
}

// Additional services that can be booked
export interface BookingAdditionalService {
  serviceId: string;
  serviceName: string;
  serviceCategory: 'spa' | 'dining' | 'transport' | 'activities' | 'other';
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  serviceDate?: Timestamp;
  serviceTime?: string;
  notes?: string;
}

// Payment information
export interface BookingPaymentDetails {
  cardLast4?: string;
  cardType?: 'visa' | 'mastercard' | 'amex' | 'discover';
  paymentGateway?: string;
  transactionId?: string;
  authorizationCode?: string;
  paymentDate?: Timestamp;
  refundAmount?: number;
  refundDate?: Timestamp;
  refundReason?: string;
}

// Cancellation information
export interface BookingCancellation {
  cancelledAt: Timestamp;
  cancelledBy: string;
  cancellationReason: string;
  cancellationFee: number;
  refundAmount: number;
  refundProcessed: boolean;
  refundDate?: Timestamp;
  cancellationPolicy: string;
}

// Modification history
export interface BookingModification {
  modifiedAt: Timestamp;
  modifiedBy: string;
  modificationType: 'dates' | 'room' | 'guests' | 'services' | 'payment' | 'other';
  changes: string;
  previousValues: any;
  additionalCharges?: number;
  refundAmount?: number;
}

// Enhanced booking interface for production hotel system
export interface EnhancedBooking {
  id: string;
  
  // Hotel and Room Information
  hotelId: string;
  hotelName: string;
  roomId: string;
  roomNumber: string;
  roomType: RoomType;
  
  // Guest Information
  guestId?: string;        // Reference to guest profile
  primaryGuestName: string;
  primaryGuestEmail: string;
  primaryGuestPhone: string;
  guestDetails: BookingGuestDetails;
  guestPreferences?: BookingGuestPreferences;
  
  // Booking Details
  checkInDate: Timestamp;
  checkOutDate: Timestamp;
  numberOfNights: number;
  
  // Pricing Breakdown
  roomRate: number;        // Base room rate per night
  totalRoomCharges: number; // Total room charges (rate × nights)
  taxes: number;
  fees: number;
  discounts?: number;
  totalAmount: number;     // Final total amount
  currency: string;
  
  // Status
  status: EnhancedBookingStatus;
  paymentStatus: PaymentStatus;
  
  // Booking Source and Channel
  source: BookingSource;
  bookingReference?: string; // External booking reference (OTA confirmation)
  channelCommission?: number;
  commissionRate?: number;
  
  // Payment Information
  paymentMethod?: 'cash' | 'credit_card' | 'debit_card' | 'bank_transfer' | 'digital_wallet';
  paymentDetails?: BookingPaymentDetails;
  depositAmount?: number;
  depositPaid?: boolean;
  balanceDue?: number;
  
  // Operational Information
  expectedCheckInTime?: string; // "14:00"
  expectedCheckOutTime?: string; // "11:00"
  actualCheckInTime?: Timestamp;
  actualCheckOutTime?: Timestamp;
  checkedInBy?: string;
  checkedOutBy?: string;
  keyCardsIssued?: number;
  
  // Additional Services
  additionalServices?: BookingAdditionalService[];
  
  // Special Requests and Notes
  specialRequests?: string[];
  dietaryRequirements?: string[];
  accessibilityNeeds?: string[];
  
  // Cancellation Information
  cancellationPolicy?: string;
  cancellationDeadline?: Timestamp;
  freeCancellationUntil?: Timestamp;
  cancellationDetails?: BookingCancellation;
  
  // Modification History
  modificationHistory?: BookingModification[];
  
  // Communication and Notes
  internalNotes?: string;
  guestNotes?: string;
  frontDeskNotes?: string;
  housekeepingNotes?: string;
  
  // Guest Satisfaction
  guestRating?: number;
  guestFeedback?: string;
  guestComplaint?: string;
  
  // Marketing and Analytics
  marketingSource?: string;
  promoCode?: string;
  loyaltyPointsUsed?: number;
  loyaltyPointsEarned?: number;
  
  // System Information
  createdAt: Timestamp;
  updatedAt: Timestamp;
  createdBy?: string;
  lastModifiedBy?: string;
  
  // Integration Data
  externalSystemData?: {
    [key: string]: any; // For OTA integrations
  };
}

// Booking summary for dashboard and reports
export interface BookingStats {
  totalBookings: number;
  pendingBookings: number;
  confirmedBookings: number;
  checkedInBookings: number;
  checkedOutBookings: number;
  cancelledBookings: number;
  noShowBookings: number;
  totalRevenue: number;
  averageBookingValue: number;
  occupancyRate: number;
  averageLengthOfStay: number;
  bookingsBySource: Record<BookingSource, number>;
  bookingsByRoomType: Record<RoomType, number>;
}

// Booking search and filter options
export interface BookingSearchOptions {
  hotelId?: string;
  guestName?: string;
  guestEmail?: string;
  bookingReference?: string;
  status?: EnhancedBookingStatus[];
  source?: BookingSource[];
  checkInDateFrom?: Date;
  checkInDateTo?: Date;
  checkOutDateFrom?: Date;
  checkOutDateTo?: Date;
  roomNumber?: string;
  roomType?: RoomType[];
  paymentStatus?: PaymentStatus[];
  limit?: number;
  offset?: number;
  sortBy?: 'checkInDate' | 'checkOutDate' | 'createdAt' | 'totalAmount' | 'guestName';
  sortOrder?: 'asc' | 'desc';
}
