#!/bin/bash

# Initialize Firebase project and Firestore collections

# Check if Firebase CLI is installed
if ! command -v firebase &> /dev/null; then
    echo "Firebase CLI is not installed. Installing..."
    npm install -g firebase-tools
fi

# Login to Firebase (if not already logged in)
echo "Logging in to Firebase..."
firebase login

# Initialize Firebase project (if not already initialized)
if [ ! -f "firebase.json" ]; then
    echo "Initializing Firebase project..."
    firebase init

    # Select Firestore and other services as needed
    # This will be interactive, so the user will need to select options
fi

# Deploy Firestore rules
echo "Deploying Firestore rules..."
firebase deploy --only firestore:rules

# Check if service account file exists
if [ ! -f "scripts/service-account.json" ]; then
    echo "Service account file not found at: scripts/service-account.json"
    echo ""
    echo "Please follow these steps to get your service account key:"
    echo "1. Go to the Firebase Console (https://console.firebase.google.com/)"
    echo "2. Select your project"
    echo "3. Click on the gear icon (⚙️) next to 'Project Overview' to go to Project settings"
    echo "4. Go to the 'Service accounts' tab"
    echo "5. Click 'Generate new private key' button"
    echo "6. Save the downloaded JSON file as 'service-account.json' in the 'scripts' directory"
    echo ""
    echo "After downloading the service account key, run this script again."
    exit 1
fi

# Initialize Firestore collections using Admin SDK
echo "Initializing Firestore collections using Admin SDK..."
npm run init-firestore-admin

echo "Firebase initialization completed!"
