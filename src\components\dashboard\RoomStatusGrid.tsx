import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Chip,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  Avatar,
  Menu,
  MenuItem
} from '@mui/material';
import {
  Bed as RoomIcon,
  Person as GuestIcon,
  CleaningServices as CleaningIcon,
  Build as MaintenanceIcon,
  CheckCircle as AvailableIcon,
  Cancel as OccupiedIcon,
  Warning as WarningIcon,
  MoreVert as MoreIcon,
  Visibility as ViewIcon,
  Edit as EditIcon,
  Assignment as TaskIcon
} from '@mui/icons-material';

interface RoomData {
  id: string;
  roomNumber: string;
  type: string;
  status: 'available' | 'occupied' | 'cleaning' | 'maintenance' | 'out_of_order';
  guestName?: string;
  checkInTime?: Date;
  checkOutTime?: Date;
  housekeepingStatus?: 'clean' | 'dirty' | 'in_progress';
  maintenanceIssues?: string[];
  lastCleaned?: Date;
  nextCheckIn?: Date;
  floor: number;
}

interface RoomStatusGridProps {
  rooms: RoomData[];
  onRoomClick?: (room: RoomData) => void;
  onStatusChange?: (roomId: string, newStatus: string) => void;
}

const RoomStatusGrid: React.FC<RoomStatusGridProps> = ({
  rooms,
  onRoomClick,
  onStatusChange
}) => {
  const [selectedRoom, setSelectedRoom] = useState<RoomData | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [menuRoom, setMenuRoom] = useState<RoomData | null>(null);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'available':
        return '#4caf50';
      case 'occupied':
        return '#f44336';
      case 'cleaning':
        return '#ff9800';
      case 'maintenance':
        return '#9e9e9e';
      case 'out_of_order':
        return '#d32f2f';
      default:
        return '#757575';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'available':
        return <AvailableIcon sx={{ color: 'white', fontSize: 16 }} />;
      case 'occupied':
        return <OccupiedIcon sx={{ color: 'white', fontSize: 16 }} />;
      case 'cleaning':
        return <CleaningIcon sx={{ color: 'white', fontSize: 16 }} />;
      case 'maintenance':
        return <MaintenanceIcon sx={{ color: 'white', fontSize: 16 }} />;
      case 'out_of_order':
        return <WarningIcon sx={{ color: 'white', fontSize: 16 }} />;
      default:
        return <RoomIcon sx={{ color: 'white', fontSize: 16 }} />;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'available':
        return 'Available';
      case 'occupied':
        return 'Occupied';
      case 'cleaning':
        return 'Cleaning';
      case 'maintenance':
        return 'Maintenance';
      case 'out_of_order':
        return 'Out of Order';
      default:
        return 'Unknown';
    }
  };

  const handleRoomClick = (room: RoomData) => {
    setSelectedRoom(room);
    setDialogOpen(true);
    if (onRoomClick) {
      onRoomClick(room);
    }
  };

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, room: RoomData) => {
    event.stopPropagation();
    setAnchorEl(event.currentTarget);
    setMenuRoom(room);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setMenuRoom(null);
  };

  const handleStatusChange = (newStatus: string) => {
    if (menuRoom && onStatusChange) {
      onStatusChange(menuRoom.id, newStatus);
    }
    handleMenuClose();
  };

  const formatTime = (date: Date | undefined) => {
    if (!date) return 'N/A';
    return date.toLocaleTimeString('en-US', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: true 
    });
  };

  const formatDate = (date: Date | undefined) => {
    if (!date) return 'N/A';
    return date.toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric' 
    });
  };

  // Group rooms by floor
  const roomsByFloor = rooms.reduce((acc, room) => {
    if (!acc[room.floor]) {
      acc[room.floor] = [];
    }
    acc[room.floor].push(room);
    return acc;
  }, {} as Record<number, RoomData[]>);

  const sortedFloors = Object.keys(roomsByFloor)
    .map(Number)
    .sort((a, b) => b - a); // Highest floor first

  return (
    <Box>
      {sortedFloors.map((floor) => (
        <Box key={floor} mb={3}>
          <Typography variant="h6" gutterBottom>
            Floor {floor}
          </Typography>
          <Grid container spacing={2}>
            {roomsByFloor[floor]
              .sort((a, b) => a.roomNumber.localeCompare(b.roomNumber))
              .map((room) => (
                <Grid item xs={6} sm={4} md={3} lg={2} key={room.id}>
                  <Card
                    sx={{
                      cursor: 'pointer',
                      transition: 'transform 0.2s, box-shadow 0.2s',
                      '&:hover': {
                        transform: 'translateY(-2px)',
                        boxShadow: 3
                      },
                      border: `2px solid ${getStatusColor(room.status)}`,
                      position: 'relative'
                    }}
                    onClick={() => handleRoomClick(room)}
                  >
                    <CardContent sx={{ p: 2, '&:last-child': { pb: 2 } }}>
                      <Box display="flex" justifyContent="space-between" alignItems="flex-start">
                        <Box>
                          <Typography variant="h6" component="div">
                            {room.roomNumber}
                          </Typography>
                          <Typography variant="body2" color="textSecondary">
                            {room.type}
                          </Typography>
                        </Box>
                        <IconButton
                          size="small"
                          onClick={(e) => handleMenuOpen(e, room)}
                          sx={{ p: 0.5 }}
                        >
                          <MoreIcon fontSize="small" />
                        </IconButton>
                      </Box>

                      <Box mt={1}>
                        <Chip
                          icon={getStatusIcon(room.status)}
                          label={getStatusText(room.status)}
                          size="small"
                          sx={{
                            backgroundColor: getStatusColor(room.status),
                            color: 'white',
                            fontSize: '0.7rem',
                            height: '24px',
                            '& .MuiChip-icon': {
                              color: 'white'
                            }
                          }}
                        />
                      </Box>

                      {room.guestName && (
                        <Box mt={1} display="flex" alignItems="center">
                          <GuestIcon sx={{ fontSize: 14, mr: 0.5, color: 'text.secondary' }} />
                          <Typography variant="caption" color="textSecondary">
                            {room.guestName}
                          </Typography>
                        </Box>
                      )}

                      {room.checkOutTime && (
                        <Typography variant="caption" color="textSecondary" display="block">
                          Out: {formatTime(room.checkOutTime)}
                        </Typography>
                      )}

                      {room.nextCheckIn && (
                        <Typography variant="caption" color="primary" display="block">
                          Next: {formatTime(room.nextCheckIn)}
                        </Typography>
                      )}
                    </CardContent>
                  </Card>
                </Grid>
              ))}
          </Grid>
        </Box>
      ))}

      {/* Room Details Dialog */}
      <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)} maxWidth="sm" fullWidth>
        <DialogTitle>
          Room {selectedRoom?.roomNumber} Details
        </DialogTitle>
        <DialogContent>
          {selectedRoom && (
            <Box>
              <List>
                <ListItem>
                  <ListItemIcon>
                    <RoomIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary="Room Type"
                    secondary={selectedRoom.type}
                  />
                </ListItem>
                
                <ListItem>
                  <ListItemIcon>
                    {getStatusIcon(selectedRoom.status)}
                  </ListItemIcon>
                  <ListItemText
                    primary="Status"
                    secondary={getStatusText(selectedRoom.status)}
                  />
                </ListItem>

                {selectedRoom.guestName && (
                  <ListItem>
                    <ListItemIcon>
                      <GuestIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Current Guest"
                      secondary={selectedRoom.guestName}
                    />
                  </ListItem>
                )}

                {selectedRoom.checkInTime && (
                  <ListItem>
                    <ListItemText
                      primary="Check-in Time"
                      secondary={`${formatDate(selectedRoom.checkInTime)} ${formatTime(selectedRoom.checkInTime)}`}
                    />
                  </ListItem>
                )}

                {selectedRoom.checkOutTime && (
                  <ListItem>
                    <ListItemText
                      primary="Check-out Time"
                      secondary={`${formatDate(selectedRoom.checkOutTime)} ${formatTime(selectedRoom.checkOutTime)}`}
                    />
                  </ListItem>
                )}

                {selectedRoom.lastCleaned && (
                  <ListItem>
                    <ListItemIcon>
                      <CleaningIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Last Cleaned"
                      secondary={`${formatDate(selectedRoom.lastCleaned)} ${formatTime(selectedRoom.lastCleaned)}`}
                    />
                  </ListItem>
                )}

                {selectedRoom.maintenanceIssues && selectedRoom.maintenanceIssues.length > 0 && (
                  <ListItem>
                    <ListItemIcon>
                      <MaintenanceIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Maintenance Issues"
                      secondary={selectedRoom.maintenanceIssues.join(', ')}
                    />
                  </ListItem>
                )}
              </List>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDialogOpen(false)}>Close</Button>
          <Button variant="contained" startIcon={<EditIcon />}>
            Update Status
          </Button>
        </DialogActions>
      </Dialog>

      {/* Context Menu */}
      <Menu
        anchorEl={anchorEl}
        open={Boolean(anchorEl)}
        onClose={handleMenuClose}
      >
        <MenuItem onClick={() => handleStatusChange('available')}>
          <ListItemIcon>
            <AvailableIcon fontSize="small" sx={{ color: '#4caf50' }} />
          </ListItemIcon>
          Mark Available
        </MenuItem>
        <MenuItem onClick={() => handleStatusChange('cleaning')}>
          <ListItemIcon>
            <CleaningIcon fontSize="small" sx={{ color: '#ff9800' }} />
          </ListItemIcon>
          Start Cleaning
        </MenuItem>
        <MenuItem onClick={() => handleStatusChange('maintenance')}>
          <ListItemIcon>
            <MaintenanceIcon fontSize="small" sx={{ color: '#9e9e9e' }} />
          </ListItemIcon>
          Maintenance Required
        </MenuItem>
        <MenuItem onClick={() => handleStatusChange('out_of_order')}>
          <ListItemIcon>
            <WarningIcon fontSize="small" sx={{ color: '#d32f2f' }} />
          </ListItemIcon>
          Out of Order
        </MenuItem>
      </Menu>
    </Box>
  );
};

export default RoomStatusGrid;
