import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Grid,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Tabs,
  Tab
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Login as CheckInIcon,
  Logout as CheckOutIcon,
  Cancel as CancelIcon,
  Visibility as ViewIcon
} from '@mui/icons-material';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { useAuth } from '../hooks/useAuth';
import { Booking, BookingStatus } from '../types';
import { 
  getBookingsForHotel, 
  createBooking, 
  updateBooking, 
  checkInGuest, 
  checkOutGuest, 
  cancelBooking 
} from '../services/bookingService';
import { getHotelsByVendor } from '../services/hotelService';
import { format } from 'date-fns';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`booking-tabpanel-${index}`}
      aria-labelledby={`booking-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const BookingsPage: React.FC = () => {
  const { user } = useAuth();
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [hotels, setHotels] = useState<any[]>([]);
  const [selectedHotel, setSelectedHotel] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [tabValue, setTabValue] = useState(0);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingBooking, setEditingBooking] = useState<Booking | null>(null);

  useEffect(() => {
    fetchHotels();
  }, [user]);

  useEffect(() => {
    if (selectedHotel) {
      fetchBookings();
    }
  }, [selectedHotel]);

  const fetchHotels = async () => {
    if (!user) return;

    try {
      let hotelData: any[] = [];
      
      if (user.role === 'super_admin') {
        // For super admin, would need to fetch all hotels
        hotelData = [];
      } else if ((user.role === 'admin' || user.role === 'vendor') && user.vendorId) {
        hotelData = await getHotelsByVendor(user.vendorId);
      } else if (user.hotelId) {
        // For staff, just show their hotel
        hotelData = [{ id: user.hotelId, name: 'Your Hotel' }];
      }

      setHotels(hotelData);
      if (hotelData.length > 0) {
        setSelectedHotel(hotelData[0].id);
      }
    } catch (err: any) {
      console.error('Error fetching hotels:', err);
      setError(err.message || 'Failed to load hotels');
    }
  };

  const fetchBookings = async () => {
    if (!selectedHotel) return;

    try {
      setLoading(true);
      setError(null);

      const bookingData = await getBookingsForHotel(selectedHotel);
      setBookings(bookingData);
    } catch (err: any) {
      console.error('Error fetching bookings:', err);
      setError(err.message || 'Failed to load bookings');
    } finally {
      setLoading(false);
    }
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleCheckIn = async (bookingId: string) => {
    try {
      await checkInGuest(bookingId);
      fetchBookings();
    } catch (err: any) {
      console.error('Error checking in guest:', err);
      setError(err.message || 'Failed to check in guest');
    }
  };

  const handleCheckOut = async (bookingId: string) => {
    try {
      await checkOutGuest(bookingId);
      fetchBookings();
    } catch (err: any) {
      console.error('Error checking out guest:', err);
      setError(err.message || 'Failed to check out guest');
    }
  };

  const handleCancel = async (bookingId: string) => {
    if (!window.confirm('Are you sure you want to cancel this booking?')) return;

    try {
      await cancelBooking(bookingId);
      fetchBookings();
    } catch (err: any) {
      console.error('Error cancelling booking:', err);
      setError(err.message || 'Failed to cancel booking');
    }
  };

  const getStatusColor = (status: BookingStatus) => {
    const colors = {
      pending: 'warning',
      confirmed: 'info',
      checked_in: 'success',
      checked_out: 'default',
      cancelled: 'error'
    };
    return colors[status] || 'default';
  };

  const filterBookingsByStatus = (status?: BookingStatus) => {
    if (!status) return bookings;
    return bookings.filter(booking => booking.status === status);
  };

  const renderBookingsTable = (filteredBookings: Booking[]) => (
    <TableContainer component={Paper}>
      <Table>
        <TableHead>
          <TableRow>
            <TableCell>Guest</TableCell>
            <TableCell>Room</TableCell>
            <TableCell>Check-in</TableCell>
            <TableCell>Check-out</TableCell>
            <TableCell>Amount</TableCell>
            <TableCell>Status</TableCell>
            <TableCell>Actions</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {loading ? (
            <TableRow>
              <TableCell colSpan={7} align="center">
                <CircularProgress />
              </TableCell>
            </TableRow>
          ) : filteredBookings.length === 0 ? (
            <TableRow>
              <TableCell colSpan={7} align="center">
                No bookings found
              </TableCell>
            </TableRow>
          ) : (
            filteredBookings.map((booking) => (
              <TableRow key={booking.id}>
                <TableCell>
                  <Box>
                    <Typography variant="body1" fontWeight="medium">
                      {booking.guestName}
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      {booking.guestEmail}
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      {booking.guestPhone}
                    </Typography>
                  </Box>
                </TableCell>
                <TableCell>
                  <Typography variant="body1">
                    Room {booking.roomNumber}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    {booking.adults} Adults, {booking.children} Children
                  </Typography>
                </TableCell>
                <TableCell>
                  {format(booking.checkInDate.toDate(), 'MMM dd, yyyy')}
                </TableCell>
                <TableCell>
                  {format(booking.checkOutDate.toDate(), 'MMM dd, yyyy')}
                </TableCell>
                <TableCell>
                  <Typography variant="body1" fontWeight="medium">
                    ${booking.totalAmount}
                  </Typography>
                  <Chip
                    label={booking.paymentStatus}
                    size="small"
                    color={booking.paymentStatus === 'paid' ? 'success' : 'warning'}
                  />
                </TableCell>
                <TableCell>
                  <Chip
                    label={booking.status.replace('_', ' ')}
                    color={getStatusColor(booking.status) as any}
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  <Box display="flex" gap={1}>
                    {booking.status === 'confirmed' && (
                      <IconButton
                        size="small"
                        onClick={() => handleCheckIn(booking.id)}
                        color="success"
                        title="Check In"
                      >
                        <CheckInIcon />
                      </IconButton>
                    )}
                    {booking.status === 'checked_in' && (
                      <IconButton
                        size="small"
                        onClick={() => handleCheckOut(booking.id)}
                        color="primary"
                        title="Check Out"
                      >
                        <CheckOutIcon />
                      </IconButton>
                    )}
                    {(booking.status === 'pending' || booking.status === 'confirmed') && (
                      <IconButton
                        size="small"
                        onClick={() => handleCancel(booking.id)}
                        color="error"
                        title="Cancel"
                      >
                        <CancelIcon />
                      </IconButton>
                    )}
                    <IconButton
                      size="small"
                      color="primary"
                      title="View Details"
                    >
                      <ViewIcon />
                    </IconButton>
                  </Box>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </TableContainer>
  );

  if (loading && !selectedHotel) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Box>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
          <Typography variant="h4" component="h1">
            Bookings Management
          </Typography>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            disabled={!selectedHotel}
          >
            New Booking
          </Button>
        </Box>

        {hotels.length > 1 && (
          <Box mb={3}>
            <FormControl sx={{ minWidth: 200 }}>
              <InputLabel>Select Hotel</InputLabel>
              <Select
                value={selectedHotel}
                label="Select Hotel"
                onChange={(e) => setSelectedHotel(e.target.value)}
              >
                {hotels.map((hotel) => (
                  <MenuItem key={hotel.id} value={hotel.id}>
                    {hotel.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          </Box>
        )}

        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {error}
          </Alert>
        )}

        {selectedHotel && (
          <Box>
            <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
              <Tabs value={tabValue} onChange={handleTabChange}>
                <Tab label="All Bookings" />
                <Tab label="Pending" />
                <Tab label="Confirmed" />
                <Tab label="Checked In" />
                <Tab label="Checked Out" />
              </Tabs>
            </Box>

            <TabPanel value={tabValue} index={0}>
              {renderBookingsTable(bookings)}
            </TabPanel>
            <TabPanel value={tabValue} index={1}>
              {renderBookingsTable(filterBookingsByStatus('pending'))}
            </TabPanel>
            <TabPanel value={tabValue} index={2}>
              {renderBookingsTable(filterBookingsByStatus('confirmed'))}
            </TabPanel>
            <TabPanel value={tabValue} index={3}>
              {renderBookingsTable(filterBookingsByStatus('checked_in'))}
            </TabPanel>
            <TabPanel value={tabValue} index={4}>
              {renderBookingsTable(filterBookingsByStatus('checked_out'))}
            </TabPanel>
          </Box>
        )}
      </Box>
    </LocalizationProvider>
  );
};

export default BookingsPage;
