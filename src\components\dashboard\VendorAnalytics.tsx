import React from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typo<PERSON>,
  Grid,
  LinearProgress,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  AttachMoney as RevenueIcon,
  Hotel as OccupancyIcon,
  Star as RatingIcon,
  People as GuestsIcon,
  Schedule as TimeIcon,
  CheckCircle as CheckIcon
} from '@mui/icons-material';

interface VendorAnalyticsProps {
  hotelData?: {
    name: string;
    totalRooms: number;
    occupiedRooms: number;
    revenue: {
      today: number;
      thisWeek: number;
      thisMonth: number;
      trend: number;
    };
    bookings: {
      total: number;
      pending: number;
      confirmed: number;
      cancelled: number;
    };
    guestSatisfaction: {
      rating: number;
      reviews: number;
      trend: number;
    };
    performance: {
      checkInTime: number; // average in minutes
      checkOutTime: number; // average in minutes
      cleaningTime: number; // average in minutes
    };
  };
}

const VendorAnalytics: React.FC<VendorAnalyticsProps> = ({ hotelData }) => {
  // Mock data if not provided
  const data = hotelData || {
    name: "Your Hotel",
    totalRooms: 50,
    occupiedRooms: 38,
    revenue: {
      today: 2850,
      thisWeek: 18500,
      thisMonth: 75000,
      trend: 12.5
    },
    bookings: {
      total: 156,
      pending: 8,
      confirmed: 142,
      cancelled: 6
    },
    guestSatisfaction: {
      rating: 4.6,
      reviews: 89,
      trend: 8.2
    },
    performance: {
      checkInTime: 8,
      checkOutTime: 12,
      cleaningTime: 35
    }
  };

  const occupancyRate = Math.round((data.occupiedRooms / data.totalRooms) * 100);
  const availableRooms = data.totalRooms - data.occupiedRooms;

  return (
    <Box>
      {/* Revenue Overview */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
                <Typography variant="h6" color="primary">
                  Today's Revenue
                </Typography>
                <RevenueIcon sx={{ color: 'primary.main' }} />
              </Box>
              <Typography variant="h4" gutterBottom>
                ${data.revenue.today.toLocaleString()}
              </Typography>
              <Box display="flex" alignItems="center">
                {data.revenue.trend > 0 ? (
                  <TrendingUpIcon sx={{ color: 'success.main', fontSize: 16, mr: 0.5 }} />
                ) : (
                  <TrendingDownIcon sx={{ color: 'error.main', fontSize: 16, mr: 0.5 }} />
                )}
                <Typography 
                  variant="body2" 
                  sx={{ color: data.revenue.trend > 0 ? 'success.main' : 'error.main' }}
                >
                  {Math.abs(data.revenue.trend)}% vs yesterday
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
                <Typography variant="h6" color="success.main">
                  This Week
                </Typography>
                <RevenueIcon sx={{ color: 'success.main' }} />
              </Box>
              <Typography variant="h4" gutterBottom>
                ${data.revenue.thisWeek.toLocaleString()}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                Average: ${Math.round(data.revenue.thisWeek / 7).toLocaleString()}/day
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={4}>
          <Card>
            <CardContent>
              <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
                <Typography variant="h6" color="warning.main">
                  This Month
                </Typography>
                <RevenueIcon sx={{ color: 'warning.main' }} />
              </Box>
              <Typography variant="h4" gutterBottom>
                ${data.revenue.thisMonth.toLocaleString()}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                Target: $80,000 (94% achieved)
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Occupancy and Bookings */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Room Occupancy
              </Typography>
              <Box display="flex" alignItems="center" justifyContent="space-between" mb={2}>
                <Typography variant="h3" color="primary">
                  {occupancyRate}%
                </Typography>
                <OccupancyIcon sx={{ fontSize: 40, color: 'primary.main' }} />
              </Box>
              <LinearProgress
                variant="determinate"
                value={occupancyRate}
                sx={{
                  height: 8,
                  borderRadius: 4,
                  mb: 2,
                  backgroundColor: '#e0e0e0',
                  '& .MuiLinearProgress-bar': {
                    borderRadius: 4,
                    backgroundColor: occupancyRate > 80 ? '#4caf50' : occupancyRate > 60 ? '#ff9800' : '#f44336'
                  }
                }}
              />
              <Box display="flex" justifyContent="space-between">
                <Typography variant="body2" color="textSecondary">
                  Occupied: {data.occupiedRooms} rooms
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  Available: {availableRooms} rooms
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Booking Status
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Box textAlign="center" p={1}>
                    <Typography variant="h4" color="success.main">
                      {data.bookings.confirmed}
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      Confirmed
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={6}>
                  <Box textAlign="center" p={1}>
                    <Typography variant="h4" color="warning.main">
                      {data.bookings.pending}
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      Pending
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={6}>
                  <Box textAlign="center" p={1}>
                    <Typography variant="h4" color="info.main">
                      {data.bookings.total}
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      Total
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={6}>
                  <Box textAlign="center" p={1}>
                    <Typography variant="h4" color="error.main">
                      {data.bookings.cancelled}
                    </Typography>
                    <Typography variant="body2" color="textSecondary">
                      Cancelled
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Guest Satisfaction and Performance */}
      <Grid container spacing={3}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Guest Satisfaction
              </Typography>
              <Box display="flex" alignItems="center" mb={2}>
                <RatingIcon sx={{ color: '#ffc107', fontSize: 32, mr: 1 }} />
                <Typography variant="h3" color="warning.main">
                  {data.guestSatisfaction.rating}
                </Typography>
                <Typography variant="h6" color="textSecondary" sx={{ ml: 0.5 }}>
                  /5.0
                </Typography>
              </Box>
              <Box display="flex" alignItems="center" mb={2}>
                {data.guestSatisfaction.trend > 0 ? (
                  <TrendingUpIcon sx={{ color: 'success.main', fontSize: 16, mr: 0.5 }} />
                ) : (
                  <TrendingDownIcon sx={{ color: 'error.main', fontSize: 16, mr: 0.5 }} />
                )}
                <Typography 
                  variant="body2" 
                  sx={{ color: data.guestSatisfaction.trend > 0 ? 'success.main' : 'error.main' }}
                >
                  {Math.abs(data.guestSatisfaction.trend)}% vs last month
                </Typography>
              </Box>
              <Typography variant="body2" color="textSecondary">
                Based on {data.guestSatisfaction.reviews} reviews
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Performance Metrics
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemIcon>
                    <CheckIcon sx={{ color: 'success.main' }} />
                  </ListItemIcon>
                  <ListItemText
                    primary="Average Check-in Time"
                    secondary={`${data.performance.checkInTime} minutes`}
                  />
                  <Chip
                    label="Excellent"
                    size="small"
                    color="success"
                  />
                </ListItem>
                <Divider />
                <ListItem>
                  <ListItemIcon>
                    <TimeIcon sx={{ color: 'warning.main' }} />
                  </ListItemIcon>
                  <ListItemText
                    primary="Average Check-out Time"
                    secondary={`${data.performance.checkOutTime} minutes`}
                  />
                  <Chip
                    label="Good"
                    size="small"
                    color="warning"
                  />
                </ListItem>
                <Divider />
                <ListItem>
                  <ListItemIcon>
                    <TimeIcon sx={{ color: 'info.main' }} />
                  </ListItemIcon>
                  <ListItemText
                    primary="Average Room Cleaning"
                    secondary={`${data.performance.cleaningTime} minutes`}
                  />
                  <Chip
                    label="Standard"
                    size="small"
                    color="info"
                  />
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default VendorAnalytics;
