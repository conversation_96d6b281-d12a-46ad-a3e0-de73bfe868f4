import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Checkbox,
  Chip,
  Button,
  Divider,
  Avatar,
  LinearProgress,
  Tabs,
  Tab,
  Badge
} from '@mui/material';
import {
  CheckCircle as CheckIcon,
  Schedule as ScheduleIcon,
  CleaningServices as CleaningIcon,
  Build as MaintenanceIcon,
  Person as PersonIcon,
  Assignment as TaskIcon,
  Add as AddIcon,
  MoreVert as MoreIcon,
  PlayArrow as StartIcon,
  Pause as PauseIcon,
  Stop as StopIcon
} from '@mui/icons-material';
import { format } from 'date-fns';

interface Task {
  id: string;
  title: string;
  description: string;
  type: 'checkin' | 'checkout' | 'cleaning' | 'maintenance' | 'meeting' | 'other';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  status: 'pending' | 'in_progress' | 'completed' | 'overdue';
  assignedTo?: string;
  assignedToAvatar?: string;
  dueTime: Date;
  estimatedDuration?: number; // in minutes
  roomNumber?: string;
  guestName?: string;
  completedAt?: Date;
  startedAt?: Date;
}

interface TodaysTasksProps {
  tasks: Task[];
  onTaskComplete?: (taskId: string) => void;
  onTaskStart?: (taskId: string) => void;
  onTaskPause?: (taskId: string) => void;
  onAddTask?: () => void;
}

const TodaysTasks: React.FC<TodaysTasksProps> = ({
  tasks,
  onTaskComplete,
  onTaskStart,
  onTaskPause,
  onAddTask
}) => {
  const [tabValue, setTabValue] = useState(0);

  const getTaskIcon = (type: string) => {
    switch (type) {
      case 'checkin':
        return <CheckIcon sx={{ color: '#4caf50' }} />;
      case 'checkout':
        return <CheckIcon sx={{ color: '#ff9800' }} />;
      case 'cleaning':
        return <CleaningIcon sx={{ color: '#2196f3' }} />;
      case 'maintenance':
        return <MaintenanceIcon sx={{ color: '#f44336' }} />;
      case 'meeting':
        return <PersonIcon sx={{ color: '#9c27b0' }} />;
      default:
        return <TaskIcon sx={{ color: '#757575' }} />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return '#f44336';
      case 'high':
        return '#ff9800';
      case 'medium':
        return '#2196f3';
      default:
        return '#4caf50';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed':
        return '#4caf50';
      case 'in_progress':
        return '#2196f3';
      case 'overdue':
        return '#f44336';
      default:
        return '#757575';
    }
  };

  const formatTime = (date: Date) => {
    return format(date, 'HH:mm');
  };

  const isOverdue = (task: Task) => {
    return task.status !== 'completed' && new Date() > task.dueTime;
  };

  const filterTasks = (status?: string) => {
    if (status) {
      return tasks.filter(task => task.status === status);
    }
    return tasks;
  };

  const getTabTasks = () => {
    switch (tabValue) {
      case 0:
        return tasks; // All tasks
      case 1:
        return tasks.filter(task => task.status === 'pending');
      case 2:
        return tasks.filter(task => task.status === 'in_progress');
      case 3:
        return tasks.filter(task => task.status === 'completed');
      default:
        return tasks;
    }
  };

  const pendingCount = tasks.filter(task => task.status === 'pending').length;
  const inProgressCount = tasks.filter(task => task.status === 'in_progress').length;
  const completedCount = tasks.filter(task => task.status === 'completed').length;
  const overdueCount = tasks.filter(task => isOverdue(task)).length;

  const completionRate = tasks.length > 0 ? (completedCount / tasks.length) * 100 : 0;

  return (
    <Card>
      <CardContent>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Typography variant="h6">
            Today's Tasks
          </Typography>
          <Button
            size="small"
            startIcon={<AddIcon />}
            onClick={onAddTask}
            variant="outlined"
          >
            Add Task
          </Button>
        </Box>

        {/* Progress Overview */}
        <Box mb={3}>
          <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
            <Typography variant="body2" color="textSecondary">
              Completion Rate
            </Typography>
            <Typography variant="body2" color="textSecondary">
              {completedCount}/{tasks.length} tasks
            </Typography>
          </Box>
          <LinearProgress
            variant="determinate"
            value={completionRate}
            sx={{
              height: 8,
              borderRadius: 4,
              backgroundColor: '#e0e0e0',
              '& .MuiLinearProgress-bar': {
                borderRadius: 4,
                backgroundColor: completionRate === 100 ? '#4caf50' : '#2196f3'
              }
            }}
          />
          <Typography variant="caption" color="textSecondary">
            {Math.round(completionRate)}% complete
          </Typography>
        </Box>

        {/* Task Tabs */}
        <Tabs
          value={tabValue}
          onChange={(_, newValue) => setTabValue(newValue)}
          variant="scrollable"
          scrollButtons="auto"
          sx={{ mb: 2 }}
        >
          <Tab
            label={
              <Badge badgeContent={overdueCount} color="error">
                All ({tasks.length})
              </Badge>
            }
          />
          <Tab
            label={
              <Badge badgeContent={pendingCount} color="warning">
                Pending
              </Badge>
            }
          />
          <Tab
            label={
              <Badge badgeContent={inProgressCount} color="info">
                In Progress
              </Badge>
            }
          />
          <Tab
            label={
              <Badge badgeContent={completedCount} color="success">
                Completed
              </Badge>
            }
          />
        </Tabs>

        {/* Tasks List */}
        <List sx={{ maxHeight: '400px', overflow: 'auto' }}>
          {getTabTasks().map((task, index) => (
            <React.Fragment key={task.id}>
              <ListItem
                sx={{
                  backgroundColor: task.status === 'completed' ? '#f5f5f5' : 'transparent',
                  borderRadius: 1,
                  mb: 1,
                  border: isOverdue(task) ? '1px solid #f44336' : 'none'
                }}
              >
                <ListItemIcon>
                  <Checkbox
                    checked={task.status === 'completed'}
                    onChange={() => onTaskComplete && onTaskComplete(task.id)}
                    disabled={task.status === 'completed'}
                  />
                </ListItemIcon>

                <Box sx={{ mr: 1 }}>
                  {getTaskIcon(task.type)}
                </Box>

                <ListItemText
                  primary={
                    <Box display="flex" alignItems="center" gap={1}>
                      <Typography
                        variant="subtitle2"
                        sx={{
                          textDecoration: task.status === 'completed' ? 'line-through' : 'none',
                          color: task.status === 'completed' ? 'text.secondary' : 'text.primary'
                        }}
                      >
                        {task.title}
                      </Typography>
                      <Chip
                        label={task.priority}
                        size="small"
                        sx={{
                          backgroundColor: getPriorityColor(task.priority),
                          color: 'white',
                          fontSize: '0.7rem',
                          height: '20px'
                        }}
                      />
                      {task.roomNumber && (
                        <Chip
                          label={`Room ${task.roomNumber}`}
                          size="small"
                          variant="outlined"
                          sx={{ fontSize: '0.7rem', height: '20px' }}
                        />
                      )}
                    </Box>
                  }
                  secondary={
                    <Box>
                      <Typography variant="body2" color="textSecondary">
                        {task.description}
                      </Typography>
                      <Box display="flex" alignItems="center" gap={1} mt={0.5}>
                        <ScheduleIcon sx={{ fontSize: 14 }} />
                        <Typography variant="caption" color="textSecondary">
                          Due: {formatTime(task.dueTime)}
                        </Typography>
                        {task.estimatedDuration && (
                          <Typography variant="caption" color="textSecondary">
                            • {task.estimatedDuration}min
                          </Typography>
                        )}
                        {task.assignedTo && (
                          <Box display="flex" alignItems="center" ml={1}>
                            <Avatar
                              sx={{ width: 16, height: 16, fontSize: '0.7rem', mr: 0.5 }}
                              src={task.assignedToAvatar}
                            >
                              {task.assignedTo.charAt(0)}
                            </Avatar>
                            <Typography variant="caption" color="textSecondary">
                              {task.assignedTo}
                            </Typography>
                          </Box>
                        )}
                      </Box>
                      {isOverdue(task) && (
                        <Typography variant="caption" sx={{ color: '#f44336', fontWeight: 'bold' }}>
                          OVERDUE
                        </Typography>
                      )}
                    </Box>
                  }
                />

                <ListItemSecondaryAction>
                  <Box display="flex" alignItems="center">
                    {task.status === 'pending' && (
                      <IconButton
                        size="small"
                        onClick={() => onTaskStart && onTaskStart(task.id)}
                        color="primary"
                      >
                        <StartIcon />
                      </IconButton>
                    )}
                    {task.status === 'in_progress' && (
                      <IconButton
                        size="small"
                        onClick={() => onTaskPause && onTaskPause(task.id)}
                        color="warning"
                      >
                        <PauseIcon />
                      </IconButton>
                    )}
                    <IconButton size="small">
                      <MoreIcon />
                    </IconButton>
                  </Box>
                </ListItemSecondaryAction>
              </ListItem>
              {index < getTabTasks().length - 1 && <Divider />}
            </React.Fragment>
          ))}
        </List>

        {getTabTasks().length === 0 && (
          <Box textAlign="center" py={4}>
            <TaskIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
            <Typography variant="body2" color="textSecondary">
              No tasks in this category
            </Typography>
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

// Sample tasks for demo
export const sampleTasks: Task[] = [
  {
    id: '1',
    title: 'Guest Check-out',
    description: 'Process checkout for Mr. Johnson',
    type: 'checkout',
    priority: 'high',
    status: 'pending',
    assignedTo: 'Sarah Wilson',
    dueTime: new Date(Date.now() + 30 * 60 * 1000), // 30 minutes from now
    estimatedDuration: 15,
    roomNumber: '205',
    guestName: 'Mr. Johnson'
  },
  {
    id: '2',
    title: 'Room Cleaning',
    description: 'Deep clean room after checkout',
    type: 'cleaning',
    priority: 'medium',
    status: 'in_progress',
    assignedTo: 'Maria Garcia',
    dueTime: new Date(Date.now() + 60 * 60 * 1000), // 1 hour from now
    estimatedDuration: 45,
    roomNumber: '101',
    startedAt: new Date(Date.now() - 15 * 60 * 1000) // Started 15 minutes ago
  },
  {
    id: '3',
    title: 'AC Maintenance',
    description: 'Fix air conditioning unit',
    type: 'maintenance',
    priority: 'urgent',
    status: 'pending',
    assignedTo: 'John Smith',
    dueTime: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago (overdue)
    estimatedDuration: 90,
    roomNumber: '301'
  },
  {
    id: '4',
    title: 'Guest Check-in',
    description: 'Welcome new guest and provide room keys',
    type: 'checkin',
    priority: 'high',
    status: 'completed',
    assignedTo: 'Sarah Wilson',
    dueTime: new Date(Date.now() - 60 * 60 * 1000), // 1 hour ago
    estimatedDuration: 10,
    roomNumber: '150',
    guestName: 'Ms. Brown',
    completedAt: new Date(Date.now() - 45 * 60 * 1000)
  },
  {
    id: '5',
    title: 'Staff Meeting',
    description: 'Weekly team meeting in conference room',
    type: 'meeting',
    priority: 'medium',
    status: 'pending',
    assignedTo: 'All Staff',
    dueTime: new Date(Date.now() + 2 * 60 * 60 * 1000), // 2 hours from now
    estimatedDuration: 60
  }
];

export default TodaysTasks;
