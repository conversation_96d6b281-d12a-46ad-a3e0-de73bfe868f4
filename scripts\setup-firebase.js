// Simple Firebase setup script for web client
// This script helps you configure your Firebase project

console.log('🔥 Firebase Setup Helper');
console.log('========================');
console.log('');

console.log('📋 Follow these steps to set up your Firebase project:');
console.log('');

console.log('1. 🌐 Go to Firebase Console:');
console.log('   👉 https://console.firebase.google.com/');
console.log('');

console.log('2. 🆕 Create a new project:');
console.log('   - Click "Create a project"');
console.log('   - Project name: "linkinblink-hotel-dashboard"');
console.log('   - Accept terms and click "Continue"');
console.log('   - Disable Google Analytics and click "Create project"');
console.log('');

console.log('3. 🗄️ Set up Firestore Database:');
console.log('   - Click "Firestore Database" in left sidebar');
console.log('   - Click "Create database"');
console.log('   - Choose "Start in test mode"');
console.log('   - Select your preferred location');
console.log('   - Click "Done"');
console.log('');

console.log('4. 🌐 Add Web App:');
console.log('   - Click the web icon </> in project overview');
console.log('   - App nickname: "hotel-dashboard"');
console.log('   - Click "Register app"');
console.log('');

console.log('5. 📝 Copy your Firebase config:');
console.log('   - Copy the firebaseConfig object');
console.log('   - Update the .env file with your actual values');
console.log('');

console.log('6. 🔐 Enable Authentication:');
console.log('   - Go to "Authentication" in left sidebar');
console.log('   - Click "Get started"');
console.log('   - Go to "Sign-in method" tab');
console.log('   - Enable "Email/Password"');
console.log('');

console.log('7. 🔒 Update Firestore Rules:');
console.log('   - Go to "Firestore Database"');
console.log('   - Click "Rules" tab');
console.log('   - The rules are already configured in firestore.rules');
console.log('');

console.log('8. ✅ Test the setup:');
console.log('   - Update .env with your config');
console.log('   - Run: npm start');
console.log('');

console.log('📧 Need help? Check the README.md file for detailed instructions.');
console.log('');

// Check if .env file has been updated
const fs = require('fs');
const path = require('path');

try {
  const envPath = path.join(__dirname, '..', '.env');
  const envContent = fs.readFileSync(envPath, 'utf8');
  
  if (envContent.includes('YOUR_API_KEY_HERE')) {
    console.log('⚠️  IMPORTANT: Update your .env file with actual Firebase config values!');
  } else {
    console.log('✅ .env file appears to be configured');
  }
} catch (error) {
  console.log('⚠️  Could not check .env file');
}

console.log('');
console.log('🚀 Once configured, your hotel dashboard will be ready to use!');
