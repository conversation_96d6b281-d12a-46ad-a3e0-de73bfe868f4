import { Timestamp } from 'firebase/firestore';

// Detailed room status for hotel operations
export type RoomOperationalStatus = 
  | 'available_clean'      // Ready for guest
  | 'available_dirty'      // Needs cleaning
  | 'occupied_clean'       // Guest checked in, room clean
  | 'occupied_dirty'       // Guest checked in, needs service
  | 'checkout_dirty'       // Guest checked out, needs cleaning
  | 'out_of_order'         // Maintenance required
  | 'maintenance'          // Under maintenance
  | 'blocked'              // Blocked by management
  | 'inspecting'           // Being inspected
  | 'cleaning_in_progress' // Currently being cleaned
  | 'reserved'             // Reserved for incoming guest
  | 'no_show';             // Guest didn't show up

// Housekeeping status
export type HousekeepingStatus = 
  | 'clean'
  | 'dirty'
  | 'cleaning_in_progress'
  | 'inspected'
  | 'maintenance_required'
  | 'deep_clean_required';

// Maintenance status
export type MaintenanceStatus = 
  | 'operational'
  | 'minor_issue'
  | 'major_issue'
  | 'out_of_service'
  | 'scheduled_maintenance';

// Room condition assessment
export interface RoomCondition {
  overall: 'excellent' | 'good' | 'fair' | 'poor';
  cleanliness: 'excellent' | 'good' | 'fair' | 'poor';
  maintenance: 'excellent' | 'good' | 'fair' | 'poor';
  amenities: 'all_working' | 'minor_issues' | 'major_issues';
  
  // Specific issues
  issues?: {
    category: 'plumbing' | 'electrical' | 'hvac' | 'furniture' | 'amenities' | 'cleanliness' | 'other';
    description: string;
    severity: 'low' | 'medium' | 'high' | 'critical';
    reportedBy: string;
    reportedAt: Timestamp;
    resolved?: boolean;
    resolvedBy?: string;
    resolvedAt?: Timestamp;
  }[];
  
  lastInspectedBy?: string;
  lastInspectedAt?: Timestamp;
  inspectionNotes?: string;
}

// Real-time room status
export interface RoomStatus {
  id: string;
  roomId: string;
  roomNumber: string;
  hotelId: string;
  
  // Current Status
  operationalStatus: RoomOperationalStatus;
  housekeepingStatus: HousekeepingStatus;
  maintenanceStatus: MaintenanceStatus;
  
  // Current Occupancy
  isOccupied: boolean;
  currentGuestStayId?: string;
  guestCount?: number;
  checkInTime?: Timestamp;
  expectedCheckOutTime?: Timestamp;
  
  // Next Booking
  nextBookingId?: string;
  nextCheckInTime?: Timestamp;
  
  // Room Condition
  condition: RoomCondition;
  
  // Housekeeping Information
  lastCleanedBy?: string;
  lastCleanedAt?: Timestamp;
  cleaningDuration?: number; // minutes
  cleaningNotes?: string;
  
  // Maintenance Information
  lastMaintenanceBy?: string;
  lastMaintenanceAt?: Timestamp;
  maintenanceNotes?: string;
  nextScheduledMaintenance?: Timestamp;
  
  // Status History (last 10 changes)
  statusHistory: {
    previousStatus: RoomOperationalStatus;
    newStatus: RoomOperationalStatus;
    changedBy: string;
    changedAt: Timestamp;
    reason?: string;
    notes?: string;
  }[];
  
  // Blocking Information
  blockedBy?: string;
  blockedAt?: Timestamp;
  blockReason?: string;
  blockUntil?: Timestamp;
  
  // System Information
  lastUpdatedBy: string;
  lastUpdatedAt: Timestamp;
  createdAt: Timestamp;
}

// Housekeeping task
export interface HousekeepingTask {
  id: string;
  roomId: string;
  roomNumber: string;
  hotelId: string;
  
  // Task Details
  type: 'checkout_cleaning' | 'maintenance_cleaning' | 'deep_cleaning' | 'inspection' | 'turnover';
  priority: 'low' | 'normal' | 'high' | 'urgent';
  estimatedDuration: number; // minutes
  
  // Assignment
  assignedTo?: string;
  assignedBy: string;
  assignedAt: Timestamp;
  
  // Status
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled' | 'on_hold';
  
  // Execution
  startedAt?: Timestamp;
  completedAt?: Timestamp;
  actualDuration?: number; // minutes
  
  // Quality Check
  inspectedBy?: string;
  inspectedAt?: Timestamp;
  inspectionPassed?: boolean;
  inspectionNotes?: string;
  
  // Issues Found
  issuesFound?: {
    category: string;
    description: string;
    severity: 'low' | 'medium' | 'high';
    requiresMaintenance: boolean;
  }[];
  
  // Completion Details
  completionNotes?: string;
  suppliesUsed?: {
    item: string;
    quantity: number;
    unit: string;
  }[];
  
  // Guest Information (if applicable)
  guestStayId?: string;
  guestCheckoutTime?: Timestamp;
  nextGuestCheckinTime?: Timestamp;
  
  // System Information
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

// Maintenance request
export interface MaintenanceRequest {
  id: string;
  roomId: string;
  roomNumber: string;
  hotelId: string;
  
  // Request Details
  category: 'plumbing' | 'electrical' | 'hvac' | 'furniture' | 'appliances' | 'structural' | 'other';
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high' | 'emergency';
  
  // Impact
  affectsGuestStay: boolean;
  roomOutOfService: boolean;
  estimatedDowntime?: number; // hours
  
  // Reporting
  reportedBy: string;
  reportedAt: Timestamp;
  reportSource: 'guest_complaint' | 'housekeeping' | 'maintenance_inspection' | 'front_desk' | 'management';
  
  // Assignment
  assignedTo?: string;
  assignedBy?: string;
  assignedAt?: Timestamp;
  
  // Status
  status: 'open' | 'assigned' | 'in_progress' | 'completed' | 'cancelled' | 'on_hold';
  
  // Work Details
  workStartedAt?: Timestamp;
  workCompletedAt?: Timestamp;
  actualDuration?: number; // hours
  
  // Resolution
  resolutionDescription?: string;
  partsUsed?: {
    part: string;
    quantity: number;
    cost: number;
  }[];
  laborCost?: number;
  totalCost?: number;
  
  // Quality Check
  verifiedBy?: string;
  verifiedAt?: Timestamp;
  verificationPassed?: boolean;
  verificationNotes?: string;
  
  // Follow-up
  followUpRequired?: boolean;
  followUpDate?: Timestamp;
  followUpNotes?: string;
  
  // System Information
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

// Room availability for booking system
export interface RoomAvailability {
  id: string;
  roomId: string;
  hotelId: string;
  date: string; // YYYY-MM-DD format
  
  // Availability
  isAvailable: boolean;
  isBlocked: boolean;
  blockReason?: string;
  
  // Pricing
  basePrice: number;
  dynamicPrice?: number;
  currency: string;
  
  // Restrictions
  minimumStay?: number;
  maximumStay?: number;
  checkInRestriction?: boolean;
  checkOutRestriction?: boolean;
  
  // Booking Information
  bookingId?: string;
  guestStayId?: string;
  
  // System Information
  lastUpdatedBy: string;
  lastUpdatedAt: Timestamp;
  createdAt: Timestamp;
}
