import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Grid,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Tabs,
  Tab,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  Badge,
  Tooltip,
  Switch,
  FormControlLabel
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  Person as PersonIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  Business as BusinessIcon,
  AdminPanelSettings as AdminIcon,
  SupervisorAccount as VendorIcon,
  Group as StaffIcon,
  CheckCircle as ActiveIcon,
  Cancel as InactiveIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
  Security as LockIcon,
  SecurityUpdate as UnlockIcon
} from '@mui/icons-material';
import { useAuth } from '../hooks/useAuth';
import { User } from '../types';
import { getAllUsers, getUsersForHotel, createUser, updateUser, deleteUser, toggleUserStatus } from '../services/userService';
import { getAllHotels } from '../services/hotelService';
import { collection, query, where, onSnapshot, orderBy } from 'firebase/firestore';
import { db } from '../firebase/config';
import { format } from 'date-fns';

// Utility function to convert Timestamp to Date
const toDate = (date: any): Date => {
  if (!date) return new Date();
  if (date instanceof Date) return date;
  if (date.toDate && typeof date.toDate === 'function') return date.toDate();
  return new Date(date);
};

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`users-tabpanel-${index}`}
      aria-labelledby={`users-tab-${index}`}
      {...other}
    >
      {value === index && <Box>{children}</Box>}
    </div>
  );
}

const UserManagement: React.FC = () => {
  const { user } = useAuth();
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [viewDialogOpen, setViewDialogOpen] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [viewingUser, setViewingUser] = useState<User | null>(null);
  const [tabValue, setTabValue] = useState(0);
  const [selectedHotel, setSelectedHotel] = useState<string>('all');

  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    role: 'staff' as any,
    hotelId: '',
    vendorId: '',
    isActive: true,
    password: '',
    confirmPassword: ''
  });

  // Mock user data for demonstration
  const [mockUsers] = useState<any[]>([
    {
      id: '1',
      name: 'John Smith',
      email: '<EMAIL>',
      phone: '+****************',
      role: 'super_admin',
      hotelId: null,
      hotelName: null,
      vendorId: null,
      isActive: true,
      lastLogin: new Date(Date.now() - 2 * 60 * 60 * 1000),
      createdAt: new Date('2023-01-01'),
      permissions: ['all']
    },
    {
      id: '2',
      name: 'Sarah Johnson',
      email: '<EMAIL>',
      phone: '+****************',
      role: 'vendor',
      hotelId: '1',
      hotelName: 'Grand Plaza Hotel',
      vendorId: 'vendor1',
      isActive: true,
      lastLogin: new Date(Date.now() - 30 * 60 * 1000),
      createdAt: new Date('2023-02-15'),
      permissions: ['hotel_management', 'staff_management', 'booking_management']
    },
    {
      id: '3',
      name: 'Michael Brown',
      email: '<EMAIL>',
      phone: '+****************',
      role: 'staff',
      hotelId: '1',
      hotelName: 'Grand Plaza Hotel',
      vendorId: 'vendor1',
      isActive: true,
      lastLogin: new Date(Date.now() - 4 * 60 * 60 * 1000),
      createdAt: new Date('2023-03-20'),
      permissions: ['front_desk', 'housekeeping']
    },
    {
      id: '4',
      name: 'Emily Davis',
      email: '<EMAIL>',
      phone: '+****************',
      role: 'vendor',
      hotelId: '2',
      hotelName: 'Seaside Resort',
      vendorId: 'vendor2',
      isActive: true,
      lastLogin: new Date(Date.now() - 1 * 60 * 60 * 1000),
      createdAt: new Date('2023-04-10'),
      permissions: ['hotel_management', 'staff_management', 'booking_management']
    },
    {
      id: '5',
      name: 'David Wilson',
      email: '<EMAIL>',
      phone: '+****************',
      role: 'staff',
      hotelId: '2',
      hotelName: 'Seaside Resort',
      vendorId: 'vendor2',
      isActive: false,
      lastLogin: new Date(Date.now() - 48 * 60 * 60 * 1000),
      createdAt: new Date('2023-05-05'),
      permissions: ['maintenance']
    },
    {
      id: '6',
      name: 'Anna Martinez',
      email: '<EMAIL>',
      phone: '+****************',
      role: 'staff',
      hotelId: '1',
      hotelName: 'Grand Plaza Hotel',
      vendorId: 'vendor1',
      isActive: true,
      lastLogin: new Date(Date.now() - 6 * 60 * 60 * 1000),
      createdAt: new Date('2023-06-01'),
      permissions: ['front_desk', 'concierge']
    }
  ]);

  const hotels = [
    { id: '1', name: 'Grand Plaza Hotel' },
    { id: '2', name: 'Seaside Resort' },
    { id: '3', name: 'Mountain Lodge' }
  ];

  useEffect(() => {
    if (!user) return;

    let unsubscribe: (() => void) | undefined;

    // Set up real-time listener for users
    if (user.role === 'super_admin') {
      if (selectedHotel === 'all') {
        // Listen to all users
        const usersQuery = query(
          collection(db, 'users'),
          orderBy('name', 'asc')
        );

        unsubscribe = onSnapshot(usersQuery, (snapshot) => {
          const userList: User[] = [];
          snapshot.forEach((doc) => {
            userList.push({
              id: doc.id,
              ...doc.data() as Omit<User, 'id'>
            });
          });
          setUsers(userList);
          setLoading(false);
        }, (error) => {
          console.error('Error listening to all users:', error);
          setError(error.message);
          fetchUsers();
        });
      } else {
        // Listen to users for specific hotel
        const hotelUsersQuery = query(
          collection(db, 'users'),
          where('hotelId', '==', selectedHotel),
          orderBy('name', 'asc')
        );

        unsubscribe = onSnapshot(hotelUsersQuery, (snapshot) => {
          const userList: User[] = [];
          snapshot.forEach((doc) => {
            userList.push({
              id: doc.id,
              ...doc.data() as Omit<User, 'id'>
            });
          });
          setUsers(userList);
          setLoading(false);
        }, (error) => {
          console.error('Error listening to hotel users:', error);
          setError(error.message);
          fetchUsers();
        });
      }
    } else if (user.role === 'vendor' && user.hotelId) {
      // Vendor sees only their hotel's users
      const vendorUsersQuery = query(
        collection(db, 'users'),
        where('hotelId', '==', user.hotelId),
        orderBy('name', 'asc')
      );

      unsubscribe = onSnapshot(vendorUsersQuery, (snapshot) => {
        const userList: User[] = [];
        snapshot.forEach((doc) => {
          userList.push({
            id: doc.id,
            ...doc.data() as Omit<User, 'id'>
          });
        });
        setUsers(userList);
        setLoading(false);
      }, (error) => {
        console.error('Error listening to vendor users:', error);
        setError(error.message);
        fetchUsers();
      });
    }

    // Cleanup listener on unmount
    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
    };
  }, [user, selectedHotel]);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      let allUsers: User[] = [];

      if (user?.role === 'super_admin') {
        // Super admin sees all users
        if (selectedHotel === 'all') {
          allUsers = await getAllUsers();
        } else {
          allUsers = await getUsersForHotel(selectedHotel);
        }
      } else if (user?.role === 'vendor' && user?.hotelId) {
        // Vendor sees only their hotel's users
        allUsers = await getUsersForHotel(user.hotelId);
      }

      setUsers(allUsers);
    } catch (err: any) {
      console.error('Error fetching users:', err);
      setError(err.message);
      // Fallback to mock data if Firebase fails
      setUsers(mockUsers);
    } finally {
      setLoading(false);
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'super_admin':
        return '#f44336';
      case 'vendor':
        return '#2196f3';
      case 'staff':
        return '#4caf50';
      default:
        return '#757575';
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'super_admin':
        return <AdminIcon />;
      case 'vendor':
        return <VendorIcon />;
      case 'staff':
        return <StaffIcon />;
      default:
        return <PersonIcon />;
    }
  };

  const handleOpenDialog = (user?: any) => {
    if (user) {
      setEditingUser(user);
      setFormData({
        name: user.name,
        email: user.email,
        phone: user.phone,
        role: user.role,
        hotelId: user.hotelId || '',
        vendorId: user.vendorId || '',
        isActive: user.isActive,
        password: '',
        confirmPassword: ''
      });
    } else {
      setEditingUser(null);
      setFormData({
        name: '',
        email: '',
        phone: '',
        role: 'staff',
        hotelId: '',
        vendorId: '',
        isActive: true,
        password: '',
        confirmPassword: ''
      });
    }
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setEditingUser(null);
  };

  const handleViewUser = (user: any) => {
    setViewingUser(user);
    setViewDialogOpen(true);
  };

  const handleFormChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSaveUser = async () => {
    try {
      if (formData.password !== formData.confirmPassword) {
        setError('Passwords do not match');
        return;
      }

      if (editingUser) {
        // Update existing user
        const updateData = {
          name: formData.name,
          email: formData.email,
          phone: formData.phone,
          role: formData.role,
          hotelId: formData.hotelId,
          isActive: formData.isActive
        };
        await updateUser(editingUser.id, updateData);
      } else {
        // Create new user
        await createUser({
          email: formData.email,
          password: formData.password,
          name: formData.name,
          role: formData.role,
          phone: formData.phone,
          hotelId: formData.hotelId,
          vendorId: user?.role === 'vendor' ? user.id : undefined,
          isActive: formData.isActive
        });
      }

      handleCloseDialog();
      await fetchUsers();
    } catch (err: any) {
      console.error('Error saving user:', err);
      setError(err.message);
    }
  };

  const handleDeleteUser = async (userId: string) => {
    if (window.confirm('Are you sure you want to delete this user?')) {
      try {
        await deleteUser(userId);
        await fetchUsers();
      } catch (err: any) {
        console.error('Error deleting user:', err);
        setError(err.message);
      }
    }
  };

  const handleToggleUserStatus = async (userId: string, currentStatus: boolean) => {
    try {
      await toggleUserStatus(userId, !currentStatus);
      await fetchUsers();
    } catch (err: any) {
      console.error('Error updating user status:', err);
      setError(err.message);
    }
  };

  const superAdmins = users.filter(u => u.role === 'super_admin');
  const vendors = users.filter(u => u.role === 'vendor');
  const staff = users.filter(u => u.role === 'staff');
  const activeUsers = users.filter(u => u.isActive);
  const inactiveUsers = users.filter(u => !u.isActive);

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 3 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1">
          User Management
        </Typography>
        <Box display="flex" gap={2} alignItems="center">
          {user?.role === 'super_admin' && (
            <FormControl size="small" sx={{ minWidth: 200 }}>
              <InputLabel>Filter by Hotel</InputLabel>
              <Select
                value={selectedHotel}
                label="Filter by Hotel"
                onChange={(e) => setSelectedHotel(e.target.value)}
              >
                <MenuItem value="all">All Hotels</MenuItem>
                {hotels.map((hotel) => (
                  <MenuItem key={hotel.id} value={hotel.id}>
                    {hotel.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          )}
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => handleOpenDialog()}
          >
            Add New User
          </Button>
        </Box>
      </Box>

      {/* User Statistics */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Avatar sx={{ bgcolor: '#4caf50', mx: 'auto', mb: 1 }}>
                <PersonIcon />
              </Avatar>
              <Typography variant="h4" color="primary">
                {users.length}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                Total Users
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Avatar sx={{ bgcolor: '#2196f3', mx: 'auto', mb: 1 }}>
                <VendorIcon />
              </Avatar>
              <Typography variant="h4" color="primary">
                {vendors.length}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                Vendors
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Avatar sx={{ bgcolor: '#4caf50', mx: 'auto', mb: 1 }}>
                <StaffIcon />
              </Avatar>
              <Typography variant="h4" color="success.main">
                {staff.length}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                Staff Members
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Avatar sx={{ bgcolor: '#ff9800', mx: 'auto', mb: 1 }}>
                <ActiveIcon />
              </Avatar>
              <Typography variant="h4" color="warning.main">
                {activeUsers.length}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                Active Users
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Users Table */}
      <Card>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={(_, newValue) => setTabValue(newValue)}>
            <Tab label={`All Users (${users.length})`} />
            <Tab label={`Vendors (${vendors.length})`} />
            <Tab label={`Staff (${staff.length})`} />
            <Tab label={`Inactive (${inactiveUsers.length})`} />
          </Tabs>
        </Box>

        <TabPanel value={tabValue} index={0}>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>User</TableCell>
                  <TableCell>Role</TableCell>
                  <TableCell>Hotel</TableCell>
                  <TableCell>Contact</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Last Login</TableCell>
                  <TableCell align="center">Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {users.map((user) => (
                  <TableRow key={user.id}>
                    <TableCell>
                      <Box display="flex" alignItems="center">
                        <Avatar
                          sx={{
                            bgcolor: getRoleColor(user.role),
                            mr: 2,
                            width: 40,
                            height: 40
                          }}
                        >
                          {getRoleIcon(user.role)}
                        </Avatar>
                        <Box>
                          <Typography variant="body2" fontWeight="bold">
                            {user.name}
                          </Typography>
                          <Typography variant="caption" color="textSecondary">
                            {user.email}
                          </Typography>
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip
                        icon={getRoleIcon(user.role)}
                        label={user.role.replace('_', ' ').toUpperCase()}
                        size="small"
                        sx={{
                          backgroundColor: getRoleColor(user.role),
                          color: 'white',
                          '& .MuiChip-icon': {
                            color: 'white'
                          }
                        }}
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {user.hotelName || 'All Hotels'}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {user.phone}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Box display="flex" alignItems="center" gap={1}>
                        <Chip
                          icon={user.isActive ? <ActiveIcon /> : <InactiveIcon />}
                          label={user.isActive ? 'Active' : 'Inactive'}
                          size="small"
                          color={user.isActive ? 'success' : 'error'}
                        />
                        <Switch
                          checked={user.isActive}
                          onChange={() => handleToggleUserStatus(user.id, user.isActive)}
                          size="small"
                        />
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {user.lastLogin ? format(toDate(user.lastLogin), 'MMM dd, HH:mm') : 'Never'}
                      </Typography>
                    </TableCell>
                    <TableCell align="center">
                      <Tooltip title="View Details">
                        <IconButton size="small" color="primary" onClick={() => handleViewUser(user)}>
                          <ViewIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Edit User">
                        <IconButton size="small" color="secondary" onClick={() => handleOpenDialog(user)}>
                          <EditIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Delete User">
                        <IconButton size="small" color="error" onClick={() => handleDeleteUser(user.id)}>
                          <DeleteIcon />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          <Box p={3}>
            <Typography variant="h6" gutterBottom>
              Vendor Users ({vendors.length})
            </Typography>
            <Grid container spacing={3}>
              {vendors.map((vendor) => (
                <Grid item xs={12} md={6} lg={4} key={vendor.id}>
                  <Card>
                    <CardContent>
                      <Box display="flex" alignItems="center" mb={2}>
                        <Avatar
                          sx={{
                            bgcolor: getRoleColor(vendor.role),
                            mr: 2,
                            width: 56,
                            height: 56
                          }}
                        >
                          {getRoleIcon(vendor.role)}
                        </Avatar>
                        <Box>
                          <Typography variant="h6">
                            {vendor.name}
                          </Typography>
                          <Typography variant="body2" color="textSecondary">
                            {vendor.hotelName}
                          </Typography>
                        </Box>
                      </Box>
                      <List dense>
                        <ListItem>
                          <ListItemIcon>
                            <EmailIcon sx={{ fontSize: 16 }} />
                          </ListItemIcon>
                          <ListItemText
                            primary={vendor.email}
                            primaryTypographyProps={{ variant: 'body2' }}
                          />
                        </ListItem>
                        <ListItem>
                          <ListItemIcon>
                            <PhoneIcon sx={{ fontSize: 16 }} />
                          </ListItemIcon>
                          <ListItemText
                            primary={vendor.phone}
                            primaryTypographyProps={{ variant: 'body2' }}
                          />
                        </ListItem>
                      </List>
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </Box>
        </TabPanel>

        <TabPanel value={tabValue} index={2}>
          <Box p={3}>
            <Typography variant="h6" gutterBottom>
              Staff Members ({staff.length})
            </Typography>
            {/* Similar grid layout for staff */}
          </Box>
        </TabPanel>

        <TabPanel value={tabValue} index={3}>
          <Box p={3}>
            <Typography variant="h6" gutterBottom>
              Inactive Users ({inactiveUsers.length})
            </Typography>
            {inactiveUsers.length === 0 ? (
              <Box textAlign="center" py={4}>
                <ActiveIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                <Typography variant="h6" gutterBottom>
                  No inactive users
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  All users are currently active.
                </Typography>
              </Box>
            ) : (
              <Alert severity="info" sx={{ mb: 2 }}>
                You have {inactiveUsers.length} inactive users.
              </Alert>
            )}
          </Box>
        </TabPanel>
      </Card>

      {/* Add/Edit User Dialog */}
      <Dialog open={dialogOpen} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingUser ? 'Edit User' : 'Add New User'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Full Name"
                value={formData.name}
                onChange={(e) => handleFormChange('name', e.target.value)}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Email"
                type="email"
                value={formData.email}
                onChange={(e) => handleFormChange('email', e.target.value)}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Phone"
                value={formData.phone}
                onChange={(e) => handleFormChange('phone', e.target.value)}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth required>
                <InputLabel>Role</InputLabel>
                <Select
                  value={formData.role}
                  label="Role"
                  onChange={(e) => handleFormChange('role', e.target.value)}
                >
                  {user?.role === 'super_admin' && (
                    <MenuItem value="super_admin">Super Admin</MenuItem>
                  )}
                  <MenuItem value="vendor">Vendor</MenuItem>
                  <MenuItem value="staff">Staff</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            {(formData.role === 'vendor' || formData.role === 'staff') && (
              <Grid item xs={12}>
                <FormControl fullWidth required>
                  <InputLabel>Hotel</InputLabel>
                  <Select
                    value={formData.hotelId}
                    label="Hotel"
                    onChange={(e) => handleFormChange('hotelId', e.target.value)}
                  >
                    {hotels.map((hotel) => (
                      <MenuItem key={hotel.id} value={hotel.id}>
                        {hotel.name}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </Grid>
            )}
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Password"
                type="password"
                value={formData.password}
                onChange={(e) => handleFormChange('password', e.target.value)}
                required={!editingUser}
                helperText={editingUser ? "Leave blank to keep current password" : ""}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Confirm Password"
                type="password"
                value={formData.confirmPassword}
                onChange={(e) => handleFormChange('confirmPassword', e.target.value)}
                required={!editingUser}
              />
            </Grid>
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.isActive}
                    onChange={(e) => handleFormChange('isActive', e.target.checked)}
                  />
                }
                label="User is Active"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog} startIcon={<CancelIcon />}>
            Cancel
          </Button>
          <Button onClick={handleSaveUser} variant="contained" startIcon={<SaveIcon />}>
            {editingUser ? 'Update User' : 'Create User'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* View User Dialog */}
      <Dialog open={viewDialogOpen} onClose={() => setViewDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          User Details: {viewingUser?.name}
        </DialogTitle>
        <DialogContent>
          {viewingUser && (
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Typography variant="h6" gutterBottom>
                  Basic Information
                </Typography>
                <List>
                  <ListItem>
                    <ListItemIcon>
                      <PersonIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Full Name"
                      secondary={viewingUser.name}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      <EmailIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Email"
                      secondary={viewingUser.email}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      <PhoneIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Phone"
                      secondary={viewingUser.phone}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      {getRoleIcon(viewingUser.role)}
                    </ListItemIcon>
                    <ListItemText
                      primary="Role"
                      secondary={viewingUser.role.replace('_', ' ').toUpperCase()}
                    />
                  </ListItem>
                </List>
              </Grid>

              <Grid item xs={12} md={6}>
                <Typography variant="h6" gutterBottom>
                  Account Information
                </Typography>
                <List>
                  <ListItem>
                    <ListItemIcon>
                      <BusinessIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Hotel"
                      secondary={viewingUser.hotelName || 'All Hotels'}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      {viewingUser.isActive ? <ActiveIcon /> : <InactiveIcon />}
                    </ListItemIcon>
                    <ListItemText
                      primary="Status"
                      secondary={viewingUser.isActive ? 'Active' : 'Inactive'}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      <PersonIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Last Login"
                      secondary={viewingUser.lastLogin ? format(toDate(viewingUser.lastLogin), 'MMM dd, yyyy HH:mm') : 'Never'}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      <PersonIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Created"
                      secondary={format(toDate(viewingUser.createdAt), 'MMM dd, yyyy')}
                    />
                  </ListItem>
                </List>
              </Grid>

              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom>
                  Permissions
                </Typography>
                <Box display="flex" flexWrap="wrap" gap={1}>
                  {viewingUser.permissions?.map((permission: string, index: number) => (
                    <Chip key={index} label={permission.replace('_', ' ').toUpperCase()} variant="outlined" />
                  ))}
                </Box>
              </Grid>
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setViewDialogOpen(false)}>
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default UserManagement;
