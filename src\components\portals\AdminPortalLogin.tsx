import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Alert,
  CircularProgress,
  Container,
  Avatar,
  IconButton
} from '@mui/material';
import {
  AdminPanelSettings as AdminIcon,
  ArrowBack as BackIcon
} from '@mui/icons-material';
import { useAuth } from '../../hooks/useAuth';

const AdminPortalLogin: React.FC = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const { signIn } = useAuth();
  const navigate = useNavigate();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    try {
      const user = await signIn(email, password);
      
      // Check if user has super admin role
      if (user.role === 'super_admin') {
        navigate('/admin-dashboard');
      } else {
        setError('Access denied. This portal is for system administrators only.');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to sign in');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #d32f2f 0%, #c62828 100%)',
        display: 'flex',
        alignItems: 'center',
        py: 4
      }}
    >
      <Container component="main" maxWidth="sm">
        <Box sx={{ position: 'relative' }}>
          <IconButton
            onClick={() => navigate('/')}
            sx={{
              position: 'absolute',
              top: -60,
              left: 0,
              color: 'white',
              bgcolor: 'rgba(255,255,255,0.1)',
              '&:hover': { bgcolor: 'rgba(255,255,255,0.2)' }
            }}
          >
            <BackIcon />
          </IconButton>

          <Card sx={{ boxShadow: 6 }}>
            <CardContent sx={{ p: 6 }}>
              <Box textAlign="center" mb={4}>
                <Avatar
                  sx={{
                    bgcolor: '#d32f2f',
                    width: 80,
                    height: 80,
                    mx: 'auto',
                    mb: 2
                  }}
                >
                  <AdminIcon sx={{ fontSize: 40 }} />
                </Avatar>
                <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
                  Super Admin Panel
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  System administration and platform management
                </Typography>
              </Box>

              <Box component="form" onSubmit={handleSubmit}>
                {error && (
                  <Alert severity="error" sx={{ mb: 3 }}>
                    {error}
                  </Alert>
                )}

                <TextField
                  margin="normal"
                  required
                  fullWidth
                  id="email"
                  label="Admin Email"
                  name="email"
                  autoComplete="email"
                  autoFocus
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  disabled={loading}
                  sx={{ mb: 2 }}
                />

                <TextField
                  margin="normal"
                  required
                  fullWidth
                  name="password"
                  label="Admin Password"
                  type="password"
                  id="password"
                  autoComplete="current-password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  disabled={loading}
                  sx={{ mb: 3 }}
                />

                <Button
                  type="submit"
                  fullWidth
                  variant="contained"
                  size="large"
                  disabled={loading}
                  sx={{ 
                    mb: 3, 
                    py: 1.5,
                    bgcolor: '#d32f2f',
                    '&:hover': { bgcolor: '#c62828' }
                  }}
                >
                  {loading ? <CircularProgress size={24} /> : 'Access Admin Panel'}
                </Button>

                <Box textAlign="center">
                  <Typography variant="body2" color="text.secondary">
                    Secure administrative access only
                  </Typography>
                </Box>
              </Box>

              <Box sx={{ mt: 4, p: 3, bgcolor: 'grey.50', borderRadius: 1 }}>
                <Typography variant="h6" gutterBottom color="#d32f2f">
                  Demo Credentials:
                </Typography>
                <Typography variant="body2" sx={{ mb: 1 }}>
                  <strong>Super Admin:</strong><br />
                  Email: <EMAIL><br />
                  Password: admin123456
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Box>
      </Container>
    </Box>
  );
};

export default AdminPortalLogin;
