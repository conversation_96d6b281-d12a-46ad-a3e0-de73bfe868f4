# Automated Scheduling

The hotel booking system includes an automated scheduling feature that helps managers create efficient staff schedules with minimal effort. This document explains how to use the automated scheduling feature to create schedules that balance business needs with staff preferences.

## What Is Automated Scheduling?

Automated scheduling is a feature that automatically generates staff schedules based on predefined rules, staff availability, and business requirements. It helps managers save time and create more efficient schedules by:

1. **Respecting Staff Availability**: Scheduling staff during their preferred or available times
2. **Balancing Workload**: Distributing shifts evenly among staff members
3. **Preventing Conflicts**: Avoiding scheduling conflicts like overlapping shifts
4. **Ensuring Coverage**: Making sure all required positions are filled

## How Automated Scheduling Works

The automated scheduling system works by:

1. **Analyzing Requirements**: Determining how many staff members of each role are needed for each shift
2. **Checking Availability**: Considering staff availability preferences
3. **Applying Rules**: Following rules like maximum shifts per week and shift balance
4. **Detecting Conflicts**: Identifying and avoiding scheduling conflicts
5. **Generating Schedule**: Creating a complete schedule based on all these factors

## Using the Auto Scheduler

### Generating a Schedule

To generate an automated schedule:

1. Navigate to the Staff Scheduling page
2. Click on the "Auto Scheduler" tab
3. Set the week starting date
4. Configure the scheduling options:
   - **Respect Staff Availability**: Whether to consider staff availability preferences
   - **Maximum Shifts Per Week**: Limit on shifts per staff member
   - **Balance Shifts**: Whether to distribute shifts evenly
5. Click "Generate Schedule"

### Reviewing the Generated Schedule

After generating a schedule, you'll see:

1. **Schedule Preview**: A day-by-day view of the generated shifts
2. **Conflicts**: Any scheduling conflicts that were detected and avoided
3. **Unfilled Positions**: Any positions that couldn't be filled

### Applying the Schedule

To apply the generated schedule:

1. Review the schedule to ensure it meets your needs
2. Click "Apply Schedule"
3. Confirm the action in the dialog
4. The shifts will be created in the system

## Scheduling Options

### Respect Staff Availability

When enabled, the system will:
- Schedule staff during their preferred times when possible
- Only schedule staff during their available times
- Never schedule staff during their unavailable times

When disabled, the system will:
- Schedule staff based solely on role requirements and shift balance
- Ignore staff availability preferences

### Maximum Shifts Per Week

This setting limits the number of shifts that can be assigned to each staff member per week. It helps prevent overworking staff and ensures compliance with labor regulations.

### Balance Shifts

When enabled, the system will:
- Distribute shifts evenly among staff members
- Prioritize staff with fewer assigned shifts
- Ensure fair workload distribution

When disabled, the system will:
- Not consider the number of shifts already assigned
- Potentially assign more shifts to some staff than others

## Role Requirements

The system uses predefined role requirements to determine how many staff members of each role are needed for each shift. The default requirements are:

### Housekeeping
- Morning: 2 staff
- Afternoon: 1 staff
- Night: 0 staff

### Front Desk
- Morning: 1 staff
- Afternoon: 1 staff
- Night: 1 staff

### Maintenance
- Morning: 1 staff
- Afternoon: 1 staff
- Night: 0 staff

### Food Service
- Morning: 1 staff
- Afternoon: 1 staff
- Night: 0 staff

### Manager
- Morning: 1 staff
- Afternoon: 0 staff
- Night: 0 staff

## Handling Conflicts and Unfilled Positions

### Conflicts

The system automatically detects and avoids scheduling conflicts, such as:
- Shift overlaps
- Time off conflicts
- Consecutive shifts with insufficient rest
- Maximum hours exceeded

If conflicts are detected, they will be displayed in the schedule preview. The system will avoid creating shifts that would cause these conflicts, which may result in unfilled positions.

### Unfilled Positions

Positions may remain unfilled for several reasons:
- No staff members with the required role
- Not enough staff members with the required role
- Staff members are unavailable during the required times
- Staff members have reached their maximum shifts

When positions are unfilled, you can:
1. Adjust the scheduling options (e.g., disable "Respect Staff Availability")
2. Add more staff members with the required role
3. Update staff availability
4. Manually create shifts for the unfilled positions

## Benefits of Automated Scheduling

Using the automated scheduling feature provides several benefits:

1. **Time Savings**: Reduces the time spent creating schedules
2. **Efficiency**: Creates optimized schedules that meet business needs
3. **Fairness**: Distributes shifts evenly among staff
4. **Staff Satisfaction**: Respects staff availability preferences
5. **Compliance**: Helps ensure compliance with labor regulations
6. **Conflict Prevention**: Automatically avoids scheduling conflicts

## Best Practices

1. **Keep Staff Availability Updated**: Encourage staff to keep their availability preferences up to date
2. **Review Generated Schedules**: Always review schedules before applying them
3. **Adjust Options as Needed**: Experiment with different options to find what works best
4. **Plan Ahead**: Generate schedules well in advance
5. **Handle Unfilled Positions**: Address unfilled positions by adjusting options or manually creating shifts

## Technical Details

The automated scheduling system uses an algorithm that:
1. Determines the required staff for each shift based on role requirements
2. Sorts staff by preference and shift count
3. Assigns shifts while respecting constraints
4. Checks for conflicts
5. Tracks unfilled positions

The system integrates with:
- Staff availability preferences
- Schedule conflict detection
- Staff management
- Staff scheduling

By using the automated scheduling feature effectively, you can create efficient schedules that balance business needs with staff preferences, leading to better operational efficiency and higher staff satisfaction.
