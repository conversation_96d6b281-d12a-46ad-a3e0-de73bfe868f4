{"__collections__": {"shifts": {"shift-1": {"hotelId": "hotel-1", "staffId": "staff-1", "staffName": "John <PERSON>", "staffEmail": "<EMAIL>", "department": "Management", "position": "General Manager", "shiftDate": {"__datatype__": "timestamp", "value": "2025-07-15T00:00:00Z"}, "startTime": "08:00", "endTime": "17:00", "duration": 9, "shiftType": "regular", "status": "scheduled", "notes": "Regular management shift", "createdAt": {"__datatype__": "timestamp", "value": "2025-07-12T12:00:00Z"}, "updatedAt": {"__datatype__": "timestamp", "value": "2025-07-12T12:00:00Z"}}, "shift-2": {"hotelId": "hotel-1", "staffId": "staff-2", "staffName": "<PERSON>", "staffEmail": "<EMAIL>", "department": "Front Desk", "position": "Front Desk Agent", "shiftDate": {"__datatype__": "timestamp", "value": "2025-07-15T00:00:00Z"}, "startTime": "06:00", "endTime": "14:00", "duration": 8, "shiftType": "morning", "status": "scheduled", "notes": "Morning front desk coverage", "createdAt": {"__datatype__": "timestamp", "value": "2025-07-12T12:00:00Z"}, "updatedAt": {"__datatype__": "timestamp", "value": "2025-07-12T12:00:00Z"}}, "shift-3": {"hotelId": "hotel-1", "staffId": "staff-3", "staffName": "<PERSON>", "staffEmail": "<EMAIL>", "department": "Housekeeping", "position": "Housekeeping Supervisor", "shiftDate": {"__datatype__": "timestamp", "value": "2025-07-15T00:00:00Z"}, "startTime": "07:00", "endTime": "15:00", "duration": 8, "shiftType": "day", "status": "scheduled", "notes": "Housekeeping supervision and room cleaning", "createdAt": {"__datatype__": "timestamp", "value": "2025-07-12T12:00:00Z"}, "updatedAt": {"__datatype__": "timestamp", "value": "2025-07-12T12:00:00Z"}}, "shift-4": {"hotelId": "hotel-1", "staffId": "staff-1", "staffName": "John <PERSON>", "staffEmail": "<EMAIL>", "department": "Management", "position": "General Manager", "shiftDate": {"__datatype__": "timestamp", "value": "2025-07-16T00:00:00Z"}, "startTime": "08:00", "endTime": "17:00", "duration": 9, "shiftType": "regular", "status": "scheduled", "notes": "Regular management shift", "createdAt": {"__datatype__": "timestamp", "value": "2025-07-12T12:00:00Z"}, "updatedAt": {"__datatype__": "timestamp", "value": "2025-07-12T12:00:00Z"}}, "shift-5": {"hotelId": "hotel-1", "staffId": "staff-2", "staffName": "<PERSON>", "staffEmail": "<EMAIL>", "department": "Front Desk", "position": "Front Desk Agent", "shiftDate": {"__datatype__": "timestamp", "value": "2025-07-16T00:00:00Z"}, "startTime": "14:00", "endTime": "22:00", "duration": 8, "shiftType": "evening", "status": "scheduled", "notes": "Evening front desk coverage", "createdAt": {"__datatype__": "timestamp", "value": "2025-07-12T12:00:00Z"}, "updatedAt": {"__datatype__": "timestamp", "value": "2025-07-12T12:00:00Z"}}}}}