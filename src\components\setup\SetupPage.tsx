import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  Card<PERSON>ontent,
  <PERSON><PERSON>,
  CircularProgress,
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>l,
  StepContent,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider
} from '@mui/material';
import {
  CheckCircle as CheckIcon,
  Error as ErrorIcon,
  Info as InfoIcon
} from '@mui/icons-material';
import { completeInitialization } from '../../utils/initializeData';
import { auth, db } from '../../firebase/config';

const steps = [
  'Firebase Project Setup',
  'Database Initialization', 
  'User Account Creation',
  'Sample Data Loading'
];

const SetupPage: React.FC = () => {
  const [activeStep, setActiveStep] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [logs, setLogs] = useState<string[]>([]);

  const addLog = (message: string) => {
    setLogs(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const handleInitialize = async () => {
    setLoading(true);
    setError(null);
    setLogs([]);
    
    try {
      addLog('Starting initialization...');
      setActiveStep(1);
      
      addLog('Initializing database...');
      setActiveStep(2);
      
      addLog('Creating user accounts...');
      setActiveStep(3);
      
      addLog('Loading sample data...');
      await completeInitialization();
      
      setActiveStep(4);
      addLog('✅ Initialization completed successfully!');
      setSuccess(true);
      
    } catch (err: any) {
      console.error('Initialization error:', err);

      if (err.message.includes('Missing or insufficient permissions')) {
        setError('Firebase permissions error. Please ensure:\n1. Authentication is enabled in Firebase Console\n2. Firestore Database is created in "test mode"\n3. Firestore rules allow read/write access');
        addLog('❌ Permission denied - Check Firebase setup');
      } else if (err.message.includes('auth/email-already-in-use')) {
        setError('Users already exist. You can proceed to login!');
        addLog('⚠️ Users already created - Setup may be complete');
        setSuccess(true);
      } else {
        setError(err.message || 'Failed to initialize the application');
        addLog(`❌ Error: ${err.message}`);
      }
    } finally {
      setLoading(false);
    }
  };

  if (success) {
    return (
      <Container maxWidth="md" sx={{ mt: 4 }}>
        <Card>
          <CardContent sx={{ textAlign: 'center', p: 4 }}>
            <CheckIcon sx={{ fontSize: 64, color: 'success.main', mb: 2 }} />
            <Typography variant="h4" gutterBottom>
              🎉 Setup Complete!
            </Typography>
            <Typography variant="body1" paragraph>
              Your hotel dashboard has been successfully initialized with sample data.
            </Typography>
            
            <Box sx={{ mt: 3, p: 2, bgcolor: 'grey.100', borderRadius: 1 }}>
              <Typography variant="h6" gutterBottom>
                👤 Login Credentials:
              </Typography>
              <Typography variant="body2" sx={{ mb: 1 }}>
                <strong>Admin Account:</strong><br />
                Email: <EMAIL><br />
                Password: admin123456
              </Typography>
              <Typography variant="body2">
                <strong>Vendor Account:</strong><br />
                Email: <EMAIL><br />
                Password: vendor123456
              </Typography>
            </Box>
            
            <Button 
              variant="contained" 
              size="large" 
              sx={{ mt: 3 }}
              onClick={() => window.location.href = '/login'}
            >
              Go to Login
            </Button>
          </CardContent>
        </Card>
      </Container>
    );
  }

  return (
    <Container maxWidth="md" sx={{ mt: 4 }}>
      <Typography variant="h3" component="h1" gutterBottom align="center">
        🏨 Hotel Dashboard Setup
      </Typography>
      
      <Typography variant="body1" paragraph align="center" color="textSecondary">
        Welcome! Let's set up your hotel management dashboard.
      </Typography>

      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            📋 Prerequisites Checklist:
          </Typography>
          <List dense>
            <ListItem>
              <ListItemIcon>
                <InfoIcon color="primary" />
              </ListItemIcon>
              <ListItemText 
                primary="Firebase project created"
                secondary="Create a new Firebase project at console.firebase.google.com"
              />
            </ListItem>
            <ListItem>
              <ListItemIcon>
                <InfoIcon color="primary" />
              </ListItemIcon>
              <ListItemText 
                primary="Firestore database enabled"
                secondary="Enable Firestore in 'test mode' from Firebase console"
              />
            </ListItem>
            <ListItem>
              <ListItemIcon>
                <InfoIcon color="primary" />
              </ListItemIcon>
              <ListItemText 
                primary="Authentication enabled"
                secondary="Enable Email/Password authentication in Firebase console"
              />
            </ListItem>
            <ListItem>
              <ListItemIcon>
                <InfoIcon color="primary" />
              </ListItemIcon>
              <ListItemText 
                primary="Firebase config updated"
                secondary="Update .env file with your Firebase project configuration"
              />
            </ListItem>
          </List>
        </CardContent>
      </Card>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          <Typography variant="body2">
            {error}
          </Typography>
          <Typography variant="caption" display="block" sx={{ mt: 1 }}>
            Make sure you've completed all prerequisites above.
          </Typography>
        </Alert>
      )}

      <Card>
        <CardContent>
          <Stepper activeStep={activeStep} orientation="vertical">
            {steps.map((label, index) => (
              <Step key={label}>
                <StepLabel>{label}</StepLabel>
                <StepContent>
                  <Typography variant="body2" color="textSecondary">
                    {index === 0 && "Verifying Firebase connection..."}
                    {index === 1 && "Setting up database collections..."}
                    {index === 2 && "Creating admin and vendor accounts..."}
                    {index === 3 && "Loading sample hotels, rooms, and staff data..."}
                  </Typography>
                </StepContent>
              </Step>
            ))}
          </Stepper>

          <Box sx={{ mt: 3 }}>
            <Button
              variant="contained"
              size="large"
              fullWidth
              onClick={handleInitialize}
              disabled={loading}
              startIcon={loading ? <CircularProgress size={20} /> : null}
            >
              {loading ? 'Initializing...' : 'Initialize Hotel Dashboard'}
            </Button>
          </Box>

          {logs.length > 0 && (
            <Box sx={{ mt: 3 }}>
              <Divider sx={{ mb: 2 }} />
              <Typography variant="h6" gutterBottom>
                Setup Logs:
              </Typography>
              <Box 
                sx={{ 
                  bgcolor: 'grey.900', 
                  color: 'grey.100', 
                  p: 2, 
                  borderRadius: 1,
                  fontFamily: 'monospace',
                  fontSize: '0.875rem',
                  maxHeight: 200,
                  overflow: 'auto'
                }}
              >
                {logs.map((log, index) => (
                  <div key={index}>{log}</div>
                ))}
              </Box>
            </Box>
          )}
        </CardContent>
      </Card>
    </Container>
  );
};

export default SetupPage;
