import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Tabs,
  Tab,
  Button,
  IconButton,
  Chip,
  Alert
} from '@mui/material';
import {
  Dashboard as DashboardIcon,
  Notifications as NotificationIcon,
  Room as RoomIcon,
  Assignment as TaskIcon,
  Refresh as RefreshIcon,
  Fullscreen as FullscreenIcon,
  Settings as SettingsIcon
} from '@mui/icons-material';
import { useAuth } from '../hooks/useAuth';
import Dashboard from '../components/dashboard/Dashboard';
import NotificationsPanel, { sampleNotifications } from '../components/dashboard/NotificationsPanel';
import RoomStatusGrid from '../components/dashboard/RoomStatusGrid';
import TodaysTasks, { sampleTasks } from '../components/dashboard/TodaysTasks';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`dashboard-tabpanel-${index}`}
      aria-labelledby={`dashboard-tab-${index}`}
      {...other}
    >
      {value === index && <Box>{children}</Box>}
    </div>
  );
}

const CompleteDashboard: React.FC = () => {
  const { user } = useAuth();
  const [tabValue, setTabValue] = useState(0);
  const [notifications, setNotifications] = useState(sampleNotifications);
  const [tasks, setTasks] = useState(sampleTasks);
  const [lastRefresh, setLastRefresh] = useState(new Date());

  // Sample room data
  const [rooms] = useState([
    {
      id: '1',
      roomNumber: '101',
      type: 'Standard',
      status: 'available' as const,
      floor: 1,
      lastCleaned: new Date(Date.now() - 2 * 60 * 60 * 1000)
    },
    {
      id: '2',
      roomNumber: '102',
      type: 'Standard',
      status: 'occupied' as const,
      guestName: 'John Doe',
      checkInTime: new Date(Date.now() - 24 * 60 * 60 * 1000),
      checkOutTime: new Date(Date.now() + 2 * 60 * 60 * 1000),
      floor: 1
    },
    {
      id: '3',
      roomNumber: '103',
      type: 'Standard',
      status: 'cleaning' as const,
      floor: 1,
      lastCleaned: new Date(Date.now() - 30 * 60 * 1000)
    },
    {
      id: '4',
      roomNumber: '201',
      type: 'Deluxe',
      status: 'maintenance' as const,
      maintenanceIssues: ['AC not working', 'Leaky faucet'],
      floor: 2
    },
    {
      id: '5',
      roomNumber: '202',
      type: 'Deluxe',
      status: 'available' as const,
      floor: 2,
      nextCheckIn: new Date(Date.now() + 4 * 60 * 60 * 1000)
    },
    {
      id: '6',
      roomNumber: '203',
      type: 'Deluxe',
      status: 'occupied' as const,
      guestName: 'Sarah Wilson',
      checkInTime: new Date(Date.now() - 12 * 60 * 60 * 1000),
      checkOutTime: new Date(Date.now() + 12 * 60 * 60 * 1000),
      floor: 2
    },
    {
      id: '7',
      roomNumber: '301',
      type: 'Suite',
      status: 'out_of_order' as const,
      maintenanceIssues: ['Plumbing issues'],
      floor: 3
    },
    {
      id: '8',
      roomNumber: '302',
      type: 'Suite',
      status: 'available' as const,
      floor: 3
    }
  ]);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleRefresh = () => {
    setLastRefresh(new Date());
    // In a real app, this would trigger data refresh
  };

  const handleNotificationMarkAsRead = (id: string) => {
    setNotifications(prev => 
      prev.map(notif => 
        notif.id === id ? { ...notif, isRead: true } : notif
      )
    );
  };

  const handleNotificationDelete = (id: string) => {
    setNotifications(prev => prev.filter(notif => notif.id !== id));
  };

  const handleNotificationsClearAll = () => {
    setNotifications([]);
  };

  const handleTaskComplete = (taskId: string) => {
    setTasks(prev =>
      prev.map(task =>
        task.id === taskId
          ? { ...task, status: 'completed' as const, completedAt: new Date() }
          : task
      )
    );
  };

  const handleTaskStart = (taskId: string) => {
    setTasks(prev =>
      prev.map(task =>
        task.id === taskId
          ? { ...task, status: 'in_progress' as const, startedAt: new Date() }
          : task
      )
    );
  };

  const handleTaskPause = (taskId: string) => {
    setTasks(prev =>
      prev.map(task =>
        task.id === taskId
          ? { ...task, status: 'pending' as const }
          : task
      )
    );
  };

  const handleRoomStatusChange = (roomId: string, newStatus: string) => {
    // In a real app, this would update the room status in the backend
    console.log(`Room ${roomId} status changed to ${newStatus}`);
  };

  const unreadNotifications = notifications.filter(n => !n.isRead).length;
  const pendingTasks = tasks.filter(t => t.status === 'pending').length;
  const inProgressTasks = tasks.filter(t => t.status === 'in_progress').length;

  return (
    <Box>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1">
          Hotel Management Dashboard
        </Typography>
        <Box display="flex" alignItems="center" gap={1}>
          <Chip
            label={`Last updated: ${lastRefresh.toLocaleTimeString()}`}
            size="small"
            variant="outlined"
          />
          <IconButton onClick={handleRefresh} color="primary">
            <RefreshIcon />
          </IconButton>
          <IconButton color="default">
            <SettingsIcon />
          </IconButton>
        </Box>
      </Box>

      {/* Alert for urgent items */}
      {(unreadNotifications > 0 || pendingTasks > 0) && (
        <Alert severity="warning" sx={{ mb: 3 }}>
          <Box display="flex" alignItems="center" gap={2}>
            <Typography variant="body2">
              You have {unreadNotifications} unread notifications and {pendingTasks + inProgressTasks} pending tasks.
            </Typography>
            <Button size="small" onClick={() => setTabValue(1)}>
              View Tasks
            </Button>
          </Box>
        </Alert>
      )}

      {/* Dashboard Tabs */}
      <Card sx={{ mb: 3 }}>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={handleTabChange} variant="scrollable" scrollButtons="auto">
            <Tab
              icon={<DashboardIcon />}
              label="Overview"
              iconPosition="start"
            />
            <Tab
              icon={<TaskIcon />}
              label={`Tasks (${pendingTasks + inProgressTasks})`}
              iconPosition="start"
            />
            <Tab
              icon={<RoomIcon />}
              label="Room Status"
              iconPosition="start"
            />
            <Tab
              icon={<NotificationIcon />}
              label={`Notifications (${unreadNotifications})`}
              iconPosition="start"
            />
          </Tabs>
        </Box>

        {/* Tab Panels */}
        <TabPanel value={tabValue} index={0}>
          <Box p={3}>
            <Dashboard />
          </Box>
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          <Box p={3}>
            <TodaysTasks
              tasks={tasks}
              onTaskComplete={handleTaskComplete}
              onTaskStart={handleTaskStart}
              onTaskPause={handleTaskPause}
              onAddTask={() => console.log('Add new task')}
            />
          </Box>
        </TabPanel>

        <TabPanel value={tabValue} index={2}>
          <Box p={3}>
            <Typography variant="h6" gutterBottom>
              Real-time Room Status
            </Typography>
            <Typography variant="body2" color="textSecondary" paragraph>
              Click on any room to view details or right-click to change status.
            </Typography>
            <RoomStatusGrid
              rooms={rooms}
              onRoomClick={(room) => console.log('Room clicked:', room)}
              onStatusChange={handleRoomStatusChange}
            />
          </Box>
        </TabPanel>

        <TabPanel value={tabValue} index={3}>
          <Box p={3}>
            <NotificationsPanel
              notifications={notifications}
              onMarkAsRead={handleNotificationMarkAsRead}
              onDelete={handleNotificationDelete}
              onClearAll={handleNotificationsClearAll}
            />
          </Box>
        </TabPanel>
      </Card>

      {/* Quick Stats Footer */}
      <Grid container spacing={2}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="primary">
                {rooms.filter(r => r.status === 'available').length}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                Available Rooms
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="error">
                {pendingTasks}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                Pending Tasks
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="warning.main">
                {unreadNotifications}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                Unread Notifications
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="success.main">
                {Math.round((rooms.filter(r => r.status === 'occupied').length / rooms.length) * 100)}%
              </Typography>
              <Typography variant="body2" color="textSecondary">
                Occupancy Rate
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default CompleteDashboard;
