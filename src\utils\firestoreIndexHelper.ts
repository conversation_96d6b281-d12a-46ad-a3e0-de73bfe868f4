import { FirebaseError } from 'firebase/app';

/**
 * Check if an error is related to missing Firestore indexes
 */
export const isIndexError = (error: any): boolean => {
  return error instanceof FirebaseError && 
         error.code === 'failed-precondition' && 
         error.message.includes('requires an index');
};

/**
 * Extract the index creation URL from a Firestore index error
 */
export const extractIndexUrl = (error: any): string | null => {
  if (!isIndexError(error)) return null;
  
  const match = error.message.match(/https:\/\/console\.firebase\.google\.com[^\s]+/);
  return match ? match[0] : null;
};

/**
 * Handle Firestore index errors with user-friendly messages
 */
export const handleIndexError = (error: any, context: string = ''): {
  message: string;
  indexUrl?: string;
  canRetry: boolean;
} => {
  if (isIndexError(error)) {
    const indexUrl = extractIndexUrl(error);
    return {
      message: `Database index required for ${context}. This is a one-time setup needed for optimal performance.`,
      indexUrl,
      canRetry: true
    };
  }
  
  return {
    message: error.message || 'An unexpected error occurred',
    canRetry: false
  };
};

/**
 * Wrapper for Firestore queries that handles index errors gracefully
 */
export const executeQueryWithFallback = async <T>(
  primaryQuery: () => Promise<T>,
  fallbackQuery?: () => Promise<T>,
  context: string = 'query'
): Promise<T> => {
  try {
    return await primaryQuery();
  } catch (error) {
    if (isIndexError(error)) {
      console.warn(`Index required for ${context}:`, extractIndexUrl(error));
      
      if (fallbackQuery) {
        console.log(`Falling back to alternative query for ${context}`);
        return await fallbackQuery();
      }
      
      // If no fallback, throw a more user-friendly error
      throw new Error(`Database optimization needed for ${context}. Please contact support.`);
    }
    
    // Re-throw non-index errors
    throw error;
  }
};

/**
 * Common Firestore index requirements for the hotel management system
 */
export const REQUIRED_INDEXES = {
  staff: [
    {
      collection: 'staff',
      fields: [
        { field: 'hotelId', order: 'ASCENDING' },
        { field: 'name', order: 'ASCENDING' }
      ]
    },
    {
      collection: 'staff',
      fields: [
        { field: 'hotelId', order: 'ASCENDING' },
        { field: 'role', order: 'ASCENDING' },
        { field: 'name', order: 'ASCENDING' }
      ]
    },
    {
      collection: 'staff',
      fields: [
        { field: 'hotelId', order: 'ASCENDING' },
        { field: 'status', order: 'ASCENDING' },
        { field: 'name', order: 'ASCENDING' }
      ]
    }
  ],
  bookings: [
    {
      collection: 'bookings',
      fields: [
        { field: 'hotelId', order: 'ASCENDING' },
        { field: 'createdAt', order: 'DESCENDING' }
      ]
    },
    {
      collection: 'bookings',
      fields: [
        { field: 'hotelId', order: 'ASCENDING' },
        { field: 'status', order: 'ASCENDING' },
        { field: 'createdAt', order: 'DESCENDING' }
      ]
    }
  ],
  users: [
    {
      collection: 'users',
      fields: [
        { field: 'hotelId', order: 'ASCENDING' },
        { field: 'name', order: 'ASCENDING' }
      ]
    },
    {
      collection: 'users',
      fields: [
        { field: 'role', order: 'ASCENDING' },
        { field: 'isActive', order: 'ASCENDING' },
        { field: 'name', order: 'ASCENDING' }
      ]
    }
  ],
  rooms: [
    {
      collection: 'rooms',
      fields: [
        { field: 'hotelId', order: 'ASCENDING' },
        { field: 'isActive', order: 'ASCENDING' },
        { field: 'roomNumber', order: 'ASCENDING' }
      ]
    }
  ],
  hotels: [
    {
      collection: 'hotels',
      fields: [
        { field: 'vendorId', order: 'ASCENDING' },
        { field: 'isActive', order: 'ASCENDING' }
      ]
    },
    {
      collection: 'hotels',
      fields: [
        { field: 'isActive', order: 'ASCENDING' },
        { field: 'name', order: 'ASCENDING' }
      ]
    }
  ]
};

/**
 * Generate Firestore index creation instructions
 */
export const generateIndexInstructions = (): string => {
  const instructions = [
    '🔥 FIRESTORE INDEX SETUP INSTRUCTIONS',
    '=====================================',
    '',
    'Your LinkinBlink Hotel Management System requires some database indexes for optimal performance.',
    'This is a one-time setup that takes just a few minutes.',
    '',
    '📋 STEPS TO CREATE INDEXES:',
    '',
    '1. Go to Firebase Console: https://console.firebase.google.com',
    '2. Select your project: linkinblink-f544a',
    '3. Navigate to Firestore Database → Indexes',
    '4. Click "Create Index" and add the following indexes:',
    '',
    '📊 REQUIRED INDEXES:',
    ''
  ];
  
  Object.entries(REQUIRED_INDEXES).forEach(([category, indexes]) => {
    instructions.push(`${category.toUpperCase()} Collection:`);
    indexes.forEach((index, i) => {
      instructions.push(`  ${i + 1}. Collection: ${index.collection}`);
      instructions.push(`     Fields:`);
      index.fields.forEach(field => {
        instructions.push(`     - ${field.field} (${field.order})`);
      });
      instructions.push('');
    });
  });
  
  instructions.push(
    '⚡ QUICK SETUP:',
    'Alternatively, when you see index error messages in the console,',
    'click the provided links to automatically create the required indexes.',
    '',
    '✅ Once indexes are created, all features will work at full speed!',
    ''
  );
  
  return instructions.join('\n');
};

/**
 * Log index setup instructions to console
 */
export const logIndexInstructions = (): void => {
  console.log(generateIndexInstructions());
};

export default {
  isIndexError,
  extractIndexUrl,
  handleIndexError,
  executeQueryWithFallback,
  generateIndexInstructions,
  logIndexInstructions,
  REQUIRED_INDEXES
};
