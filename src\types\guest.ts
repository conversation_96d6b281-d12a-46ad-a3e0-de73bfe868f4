import { Timestamp } from 'firebase/firestore';

// Guest status for hotel operations
export type GuestStatus = 
  | 'checked_in'
  | 'checked_out'
  | 'no_show'
  | 'early_departure'
  | 'extended_stay';

// Guest preferences
export interface GuestPreferences {
  roomType?: string;
  bedType?: 'single' | 'double' | 'king' | 'queen' | 'twin';
  smokingPreference?: 'smoking' | 'non_smoking';
  floorPreference?: 'low' | 'high' | 'middle';
  specialRequests?: string[];
  dietaryRestrictions?: string[];
  accessibilityNeeds?: string[];
}

// Guest contact information
export interface GuestContact {
  email: string;
  phone: string;
  alternatePhone?: string;
  address: {
    street: string;
    city: string;
    state: string;
    country: string;
    zipCode: string;
  };
  emergencyContact?: {
    name: string;
    relationship: string;
    phone: string;
  };
}

// Guest identification
export interface GuestIdentification {
  type: 'passport' | 'drivers_license' | 'national_id' | 'other';
  number: string;
  issuingCountry: string;
  expiryDate?: Date;
  verified: boolean;
  verifiedBy?: string;
  verifiedAt?: Timestamp;
}

// Guest profile for hotel CRM
export interface Guest {
  id: string;
  
  // Basic Information
  firstName: string;
  lastName: string;
  fullName: string;
  dateOfBirth?: Date;
  gender?: 'male' | 'female' | 'other' | 'prefer_not_to_say';
  nationality?: string;
  
  // Contact Information
  contact: GuestContact;
  
  // Identification
  identification?: GuestIdentification;
  
  // Preferences
  preferences: GuestPreferences;
  
  // Guest History
  totalStays: number;
  totalSpent: number;
  averageRating: number;
  lastStayDate?: Timestamp;
  firstStayDate?: Timestamp;
  
  // Loyalty Program
  loyaltyTier?: 'bronze' | 'silver' | 'gold' | 'platinum';
  loyaltyPoints: number;
  
  // Status
  isVip: boolean;
  isBlacklisted: boolean;
  blacklistReason?: string;
  
  // Notes and Special Information
  notes?: string;
  allergies?: string[];
  specialNeeds?: string[];
  
  // System Information
  createdAt: Timestamp;
  updatedAt: Timestamp;
  createdBy: string;
  lastUpdatedBy: string;
}

// Guest stay record (for current and historical stays)
export interface GuestStay {
  id: string;
  guestId: string;
  bookingId: string;
  hotelId: string;
  roomId: string;
  roomNumber: string;
  
  // Stay Details
  checkInDate: Timestamp;
  checkOutDate: Timestamp;
  actualCheckInTime?: Timestamp;
  actualCheckOutTime?: Timestamp;
  
  // Status
  status: GuestStatus;
  
  // Guest Information (snapshot at time of stay)
  guestSnapshot: {
    name: string;
    email: string;
    phone: string;
    identification?: GuestIdentification;
  };
  
  // Stay Details
  numberOfGuests: number;
  additionalGuests?: {
    name: string;
    age?: number;
    relationship?: string;
  }[];
  
  // Services and Charges
  roomCharges: number;
  serviceCharges: number;
  taxes: number;
  totalAmount: number;
  amountPaid: number;
  outstandingBalance: number;
  
  // Special Requests and Notes
  specialRequests?: string[];
  housekeepingNotes?: string[];
  frontDeskNotes?: string[];
  
  // Ratings and Feedback
  guestRating?: number;
  guestFeedback?: string;
  hotelRating?: number;
  hotelNotes?: string;
  
  // System Information
  checkedInBy?: string;
  checkedOutBy?: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

// Guest service request
export interface GuestServiceRequest {
  id: string;
  guestStayId: string;
  guestId: string;
  hotelId: string;
  roomNumber: string;
  
  // Request Details
  type: 'housekeeping' | 'maintenance' | 'room_service' | 'concierge' | 'other';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  title: string;
  description: string;
  
  // Status
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled';
  
  // Assignment
  assignedTo?: string;
  assignedBy?: string;
  assignedAt?: Timestamp;
  
  // Completion
  completedAt?: Timestamp;
  completedBy?: string;
  completionNotes?: string;
  
  // Guest Satisfaction
  guestRating?: number;
  guestFeedback?: string;
  
  // System Information
  requestedAt: Timestamp;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

// Guest check-in/out data
export interface CheckInOutData {
  guestStayId: string;
  timestamp: Timestamp;
  processedBy: string;
  
  // Check-in specific
  keyCardsIssued?: number;
  depositAmount?: number;
  depositMethod?: 'cash' | 'card' | 'digital';
  
  // Check-out specific
  finalBill?: number;
  paymentMethod?: 'cash' | 'card' | 'digital' | 'comp';
  depositReturned?: number;
  
  // Room condition
  roomCondition?: 'excellent' | 'good' | 'fair' | 'damaged';
  damageNotes?: string;
  
  // Guest feedback
  guestSatisfaction?: number;
  guestComments?: string;
  
  // Additional charges
  additionalCharges?: {
    description: string;
    amount: number;
    category: string;
  }[];
  
  notes?: string;
}
