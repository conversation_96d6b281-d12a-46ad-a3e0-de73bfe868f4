Stack trace:
Frame         Function      Args
0007FFFFA350  00021005FE8E (000210285F68, 00021026AB6E, 000000000000, 0007FFFF9250) msys-2.0.dll+0x1FE8E
0007FFFFA350  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA628) msys-2.0.dll+0x67F9
0007FFFFA350  000210046832 (000210286019, 0007FFFFA208, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFA350  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFA350  000210068E24 (0007FFFFA360, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFA630  00021006A225 (0007FFFFA360, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFDDC680000 ntdll.dll
7FFDDB4D0000 KERNEL32.DLL
7FFDD9880000 KERNELBASE.dll
7FFDDC470000 USER32.dll
7FFDD9F70000 win32u.dll
7FFDDAD50000 GDI32.dll
7FFDDA1B0000 gdi32full.dll
7FFDD97D0000 msvcp_win.dll
7FFDD9FA0000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFDDC3B0000 advapi32.dll
7FFDDC1E0000 msvcrt.dll
7FFDDB7E0000 sechost.dll
7FFDDA640000 RPCRT4.dll
7FFDD8DB0000 CRYPTBASE.DLL
7FFDDA2F0000 bcryptPrimitives.dll
7FFDDA600000 IMM32.DLL
