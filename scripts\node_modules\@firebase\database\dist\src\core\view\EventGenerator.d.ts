/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { Index } from '../snap/indexes/Index';
import { Node } from '../snap/Node';
import { Change } from './Change';
import { Event } from './Event';
import { EventRegistration, QueryContext } from './EventRegistration';
/**
 * An EventGenerator is used to convert "raw" changes (Change) as computed by the
 * <PERSON><PERSON><PERSON>iffer into actual events (Event) that can be raised.  See generateEventsForChanges()
 * for details.
 *
 */
export declare class EventGenerator {
    query_: QueryContext;
    index_: Index;
    constructor(query_: QueryContext);
}
/**
 * Given a set of raw changes (no moved events and prevName not specified yet), and a set of
 * EventRegistrations that should be notified of these changes, generate the actual events to be raised.
 *
 * Notes:
 *  - child_moved events will be synthesized at this time for any child_changed events that affect
 *    our index.
 *  - prevName will be calculated based on the index ordering.
 */
export declare function eventGeneratorGenerateEventsForChanges(eventGenerator: EventGenerator, changes: Change[], eventCache: Node, eventRegistrations: EventRegistration[]): Event[];
