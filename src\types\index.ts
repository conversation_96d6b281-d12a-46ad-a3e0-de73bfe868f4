import { Timestamp } from 'firebase/firestore';

// User roles
export type UserRole = 'super_admin' | 'admin' | 'vendor' | 'staff' | 'guest';

// User interface
export interface User {
  id: string;
  email: string;
  name: string;
  role: UserRole;
  hotelId?: string;
  vendorId?: string;
  phone?: string;
  avatar?: string;
  isActive: boolean;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

// Hotel interface
export interface Hotel {
  id: string;
  name: string;
  description: string;
  address: string;
  city: string;
  state: string;
  country: string;
  zipCode: string;
  phone: string;
  email: string;
  website?: string;
  vendorId: string;
  vendorName: string;
  rating: number;
  amenities: string[];
  images: string[];
  isActive: boolean;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

// Room types
export type RoomType = 'single' | 'double' | 'suite' | 'deluxe' | 'presidential';
export type RoomStatus = 'available' | 'occupied' | 'maintenance' | 'cleaning';

// Room interface
export interface Room {
  id: string;
  hotelId: string;
  roomNumber: string;
  type: RoomType;
  status: RoomStatus;
  price: number;
  capacity: number;
  amenities: string[];
  images: string[];
  description?: string;
  floor: number;
  isActive: boolean;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

// Booking status
export type BookingStatus = 'pending' | 'confirmed' | 'checked_in' | 'checked_out' | 'cancelled';

// Booking interface
export interface Booking {
  id: string;
  hotelId: string;
  hotelName: string;
  roomId: string;
  roomNumber: string;
  userId: string;
  guestName: string;
  guestEmail: string;
  guestPhone: string;
  checkInDate: Timestamp;
  checkOutDate: Timestamp;
  adults: number;
  children: number;
  totalAmount: number;
  status: BookingStatus;
  paymentStatus: 'pending' | 'paid' | 'refunded';
  specialRequests?: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

// Staff roles
export type StaffRole = 'manager' | 'receptionist' | 'housekeeping' | 'maintenance' | 'security' | 'chef' | 'waiter';

// Staff interface
export interface StaffMember {
  id: string;
  hotelId: string;
  vendorId: string;
  name: string;
  email: string;
  phone: string;
  role: StaffRole;
  department: string;
  salary: number;
  hireDate: Timestamp;
  status: 'active' | 'inactive' | 'terminated';
  avatar?: string;
  address?: string;
  emergencyContact?: {
    name: string;
    phone: string;
    relationship: string;
  };
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

// Shift types
export type ShiftType = 'morning' | 'afternoon' | 'night' | 'custom';

// Shift interface
export interface Shift {
  id: string;
  hotelId: string;
  vendorId: string;
  staffId: string;
  staffName: string;
  date: Timestamp;
  role: StaffRole;
  shiftType: ShiftType;
  startTime: Timestamp;
  endTime: Timestamp;
  notes?: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

// Time off request interface
export interface TimeOffRequest {
  id: string;
  hotelId: string;
  staffId: string;
  staffName: string;
  type: 'vacation' | 'sick' | 'personal' | 'emergency';
  startDate: Timestamp;
  endDate: Timestamp;
  reason: string;
  status: 'pending' | 'approved' | 'rejected';
  approvedBy?: string;
  approvedAt?: Timestamp;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

// Room availability interface
export interface RoomAvailability {
  id: string;
  hotelId: string;
  roomId: string;
  date: Timestamp;
  isAvailable: boolean;
  price: number;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

// Notification interface
export interface Notification {
  id: string;
  userId: string;
  title: string;
  message: string;
  type: 'info' | 'warning' | 'error' | 'success';
  isRead: boolean;
  createdAt: Timestamp;
}

// Vendor request interface
export interface VendorRequest {
  id: string;
  userId: string;
  companyName: string;
  contactPerson: string;
  email: string;
  phone: string;
  address: string;
  businessLicense: string;
  description: string;
  status: 'pending' | 'approved' | 'rejected';
  reviewedBy?: string;
  reviewedAt?: Timestamp;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

// Permission interface
export interface Permission {
  id: string;
  name: string;
  description: string;
  resource: string;
  action: string;
  createdAt: Timestamp;
}

// API Response types
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

// Query options
export interface QueryOptions {
  limit?: number;
  orderBy?: string;
  orderDirection?: 'asc' | 'desc';
  startAfter?: any;
}

// Filter options for different entities
export interface StaffFilterOptions extends QueryOptions {
  role?: StaffRole;
  status?: 'active' | 'inactive' | 'terminated';
  department?: string;
}

export interface BookingFilterOptions extends QueryOptions {
  status?: BookingStatus;
  dateRange?: {
    start: Date;
    end: Date;
  };
}

export interface RoomFilterOptions extends QueryOptions {
  type?: RoomType;
  status?: RoomStatus;
  priceRange?: {
    min: number;
    max: number;
  };
}
