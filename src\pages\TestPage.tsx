import React, { useState } from 'react';
import {
  <PERSON>,
  Container,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  Grid,
  Alert,
  CircularProgress,
  Chip,
  Divider
} from '@mui/material';
import {
  PlayArrow as TestIcon,
  CheckCircle as SuccessIcon,
  Error as ErrorIcon,
  Info as InfoIcon
} from '@mui/icons-material';
import { runComprehensiveTests, testFirebaseConfig, testAllFirebaseServices, testCollections } from '../utils/testFirebaseConnection';
import { initializeIfEmpty, isDatabaseInitialized } from '../utils/initializeData';
import { generateIndexInstructions } from '../utils/firestoreIndexHelper';

interface TestResult {
  name: string;
  status: 'pending' | 'running' | 'success' | 'error';
  message?: string;
  details?: any;
}

const TestPage: React.FC = () => {
  const [tests, setTests] = useState<TestResult[]>([
    { name: 'Firebase Configuration', status: 'pending' },
    { name: 'Authentication Service', status: 'pending' },
    { name: 'Firestore Database', status: 'pending' },
    { name: 'Collections Access', status: 'pending' },
    { name: 'Database Initialization', status: 'pending' }
  ]);
  const [isRunning, setIsRunning] = useState(false);
  const [overallStatus, setOverallStatus] = useState<'pending' | 'success' | 'error'>('pending');

  const updateTestStatus = (testName: string, status: TestResult['status'], message?: string, details?: any) => {
    setTests(prev => prev.map(test => 
      test.name === testName 
        ? { ...test, status, message, details }
        : test
    ));
  };

  const runAllTests = async () => {
    setIsRunning(true);
    setOverallStatus('pending');
    
    try {
      // Reset all tests
      setTests(prev => prev.map(test => ({ ...test, status: 'pending' })));

      // Test 1: Firebase Configuration
      updateTestStatus('Firebase Configuration', 'running');
      const configResult = testFirebaseConfig();
      updateTestStatus('Firebase Configuration', configResult ? 'success' : 'error', 
        configResult ? 'All environment variables configured' : 'Missing environment variables');

      if (!configResult) {
        setOverallStatus('error');
        setIsRunning(false);
        return;
      }

      // Test 2: Firebase Services
      updateTestStatus('Authentication Service', 'running');
      updateTestStatus('Firestore Database', 'running');
      
      const serviceResults = await testAllFirebaseServices();
      
      updateTestStatus('Authentication Service', serviceResults.auth ? 'success' : 'error',
        serviceResults.auth ? 'Authentication working' : 'Authentication failed');
      
      updateTestStatus('Firestore Database', serviceResults.firestore ? 'success' : 'error',
        serviceResults.firestore ? 'Firestore working' : 'Firestore failed');

      if (!serviceResults.overall) {
        setOverallStatus('error');
        setIsRunning(false);
        return;
      }

      // Test 3: Collections Access
      updateTestStatus('Collections Access', 'running');
      const collectionResults = await testCollections();
      const collectionsWorking = Object.values(collectionResults).every(Boolean);
      
      updateTestStatus('Collections Access', collectionsWorking ? 'success' : 'error',
        collectionsWorking ? 'All collections accessible' : 'Some collections failed',
        collectionResults);

      // Test 4: Database Initialization
      updateTestStatus('Database Initialization', 'running');
      const isInitialized = await isDatabaseInitialized();
      
      if (!isInitialized) {
        await initializeIfEmpty();
        updateTestStatus('Database Initialization', 'success', 'Database initialized with sample data');
      } else {
        updateTestStatus('Database Initialization', 'success', 'Database already contains data');
      }

      // Overall result
      const allPassed = configResult && serviceResults.overall && collectionsWorking;
      setOverallStatus(allPassed ? 'success' : 'error');

    } catch (error) {
      console.error('Test execution failed:', error);
      setOverallStatus('error');
    } finally {
      setIsRunning(false);
    }
  };

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return <SuccessIcon color="success" />;
      case 'error':
        return <ErrorIcon color="error" />;
      case 'running':
        return <CircularProgress size={24} />;
      default:
        return <InfoIcon color="disabled" />;
    }
  };

  const getStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return 'success';
      case 'error':
        return 'error';
      case 'running':
        return 'warning';
      default:
        return 'default';
    }
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Box textAlign="center" mb={4}>
        <Typography variant="h3" component="h1" gutterBottom>
          🧪 Firebase Connection Test
        </Typography>
        <Typography variant="h6" color="text.secondary" gutterBottom>
          Verify that all Firebase services are properly connected and working
        </Typography>
        
        <Button
          variant="contained"
          size="large"
          startIcon={<TestIcon />}
          onClick={runAllTests}
          disabled={isRunning}
          sx={{ mt: 2 }}
        >
          {isRunning ? 'Running Tests...' : 'Run All Tests'}
        </Button>
      </Box>

      {overallStatus !== 'pending' && (
        <Alert 
          severity={overallStatus === 'success' ? 'success' : 'error'} 
          sx={{ mb: 3 }}
        >
          {overallStatus === 'success' 
            ? '🎉 All tests passed! Firebase is fully connected and working.'
            : '⚠️ Some tests failed. Please check the configuration and try again.'
          }
        </Alert>
      )}

      <Grid container spacing={3}>
        {tests.map((test, index) => (
          <Grid item xs={12} md={6} key={index}>
            <Card>
              <CardContent>
                <Box display="flex" alignItems="center" gap={2} mb={2}>
                  {getStatusIcon(test.status)}
                  <Typography variant="h6" component="h2">
                    {test.name}
                  </Typography>
                  <Chip 
                    label={test.status.toUpperCase()} 
                    color={getStatusColor(test.status) as any}
                    size="small"
                  />
                </Box>
                
                {test.message && (
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    {test.message}
                  </Typography>
                )}
                
                {test.details && (
                  <Box mt={2}>
                    <Divider sx={{ mb: 1 }} />
                    <Typography variant="caption" component="div">
                      Details:
                    </Typography>
                    <Box component="pre" sx={{ 
                      fontSize: '0.75rem', 
                      backgroundColor: 'grey.100', 
                      p: 1, 
                      borderRadius: 1,
                      overflow: 'auto',
                      maxHeight: 200
                    }}>
                      {JSON.stringify(test.details, null, 2)}
                    </Box>
                  </Box>
                )}
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      <Box mt={4}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              🔥 Firestore Index Setup Instructions
            </Typography>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              If you see index-related errors, follow these instructions to set up database indexes:
            </Typography>
            <Box component="pre" sx={{
              fontSize: '0.75rem',
              backgroundColor: 'grey.100',
              p: 2,
              borderRadius: 1,
              overflow: 'auto',
              maxHeight: 400,
              whiteSpace: 'pre-wrap'
            }}>
              {generateIndexInstructions()}
            </Box>
          </CardContent>
        </Card>
      </Box>

      <Box mt={4} textAlign="center">
        <Typography variant="body2" color="text.secondary">
          This test page verifies that your LinkinBlink Hotel Management System is properly connected to Firebase.
          <br />
          All services including Authentication, Firestore Database, and sample data initialization are tested.
        </Typography>
      </Box>
    </Container>
  );
};

export default TestPage;
