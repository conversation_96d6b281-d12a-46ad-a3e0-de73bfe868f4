import {
  collection,
  doc,
  getDoc,
  getDocs,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit as firestoreLimit,
  Timestamp,
  serverTimestamp
} from 'firebase/firestore';
import { db } from '../firebase/config';
import { Booking, BookingFilterOptions, BookingStatus } from '../types';

// Utility function to convert Timestamp to Date
const toDate = (date: any): Date => {
  if (!date) return new Date();
  if (date instanceof Date) return date;
  if (date.toDate && typeof date.toDate === 'function') return date.toDate();
  return new Date(date);
};

// Collection name
const BOOKINGS_COLLECTION = 'bookings';

/**
 * Get all bookings for a hotel
 */
export const getBookingsForHotel = async (
  hotelId: string,
  options?: BookingFilterOptions
): Promise<Booking[]> => {
  try {
    let bookingsQuery = query(
      collection(db, BOOKINGS_COLLECTION),
      where('hotelId', '==', hotelId),
      orderBy('createdAt', 'desc')
    );
    
    // Apply status filter if provided
    if (options?.status) {
      bookingsQuery = query(
        bookingsQuery,
        where('status', '==', options.status)
      );
    }
    
    // Apply limit if provided
    if (options?.limit) {
      bookingsQuery = query(
        bookingsQuery,
        firestoreLimit(options.limit)
      );
    }
    
    const querySnapshot = await getDocs(bookingsQuery);
    const bookings: Booking[] = [];
    
    querySnapshot.forEach((doc) => {
      bookings.push({
        id: doc.id,
        ...doc.data() as Omit<Booking, 'id'>
      });
    });
    
    // Apply date range filter if provided (client-side filtering)
    if (options?.dateRange) {
      const { start, end } = options.dateRange;
      return bookings.filter(booking => {
        const checkInDate = toDate(booking.checkInDate);
        return checkInDate >= start && checkInDate <= end;
      });
    }
    
    return bookings;
  } catch (error) {
    console.error('Error getting bookings for hotel:', error);
    throw error;
  }
};

/**
 * Get a booking by ID
 */
export const getBookingById = async (bookingId: string): Promise<Booking | null> => {
  try {
    const docRef = doc(db, BOOKINGS_COLLECTION, bookingId);
    const docSnap = await getDoc(docRef);
    
    if (docSnap.exists()) {
      return {
        id: docSnap.id,
        ...docSnap.data() as Omit<Booking, 'id'>
      };
    } else {
      return null;
    }
  } catch (error) {
    console.error('Error getting booking by ID:', error);
    throw error;
  }
};

/**
 * Create a new booking with optional Aadhaar verification
 */
export const createBooking = async (
  booking: Omit<Booking, 'id' | 'createdAt' | 'updatedAt'>
): Promise<Booking> => {
  try {
    const now = Timestamp.now();

    // Generate booking number if not provided
    const bookingNumber = booking.bookingNumber || `BK${String(Date.now()).slice(-6)}`;

    const newBooking: Omit<Booking, 'id'> = {
      ...booking,
      bookingNumber,
      status: booking.status || 'pending',
      paymentStatus: booking.paymentStatus || 'pending',
      createdAt: now,
      updatedAt: now,
      // Ensure Aadhaar verification data is properly stored
      ...(booking.aadhaarVerification && {
        aadhaarVerification: {
          ...booking.aadhaarVerification,
          verificationDate: booking.aadhaarVerification.verificationDate || new Date()
        }
      })
    };

    const docRef = await addDoc(collection(db, BOOKINGS_COLLECTION), newBooking);

    console.log('✅ Booking created successfully:', {
      id: docRef.id,
      bookingNumber,
      guestName: booking.guestName,
      aadhaarRequired: booking.aadhaarVerification?.required || false,
      aadhaarVerified: booking.aadhaarVerification?.verified || false
    });

    return {
      ...newBooking,
      id: docRef.id
    };
  } catch (error) {
    console.error('❌ Error creating booking:', error);
    throw error;
  }
};

/**
 * Update a booking
 */
export const updateBooking = async (
  bookingId: string,
  updates: Partial<Omit<Booking, 'id' | 'createdAt' | 'updatedAt'>>
): Promise<void> => {
  try {
    const updateData = {
      ...updates,
      updatedAt: serverTimestamp()
    };
    
    await updateDoc(doc(db, BOOKINGS_COLLECTION, bookingId), updateData);
  } catch (error) {
    console.error('Error updating booking:', error);
    throw error;
  }
};

/**
 * Cancel a booking
 */
export const cancelBooking = async (bookingId: string): Promise<void> => {
  try {
    await updateDoc(doc(db, BOOKINGS_COLLECTION, bookingId), {
      status: 'cancelled',
      updatedAt: serverTimestamp()
    });
  } catch (error) {
    console.error('Error cancelling booking:', error);
    throw error;
  }
};

/**
 * Check in a guest
 */
export const checkInGuest = async (bookingId: string): Promise<void> => {
  try {
    await updateDoc(doc(db, BOOKINGS_COLLECTION, bookingId), {
      status: 'checked_in',
      updatedAt: serverTimestamp()
    });
  } catch (error) {
    console.error('Error checking in guest:', error);
    throw error;
  }
};

/**
 * Check out a guest
 */
export const checkOutGuest = async (bookingId: string): Promise<void> => {
  try {
    await updateDoc(doc(db, BOOKINGS_COLLECTION, bookingId), {
      status: 'checked_out',
      updatedAt: serverTimestamp()
    });
  } catch (error) {
    console.error('Error checking out guest:', error);
    throw error;
  }
};

/**
 * Get bookings by user
 */
export const getBookingsByUser = async (
  userId: string,
  options?: BookingFilterOptions
): Promise<Booking[]> => {
  try {
    let bookingsQuery = query(
      collection(db, BOOKINGS_COLLECTION),
      where('userId', '==', userId),
      orderBy('createdAt', 'desc')
    );
    
    if (options?.limit) {
      bookingsQuery = query(bookingsQuery, firestoreLimit(options.limit));
    }
    
    const querySnapshot = await getDocs(bookingsQuery);
    const bookings: Booking[] = [];
    
    querySnapshot.forEach((doc) => {
      bookings.push({
        id: doc.id,
        ...doc.data() as Omit<Booking, 'id'>
      });
    });
    
    return bookings;
  } catch (error) {
    console.error('Error getting bookings by user:', error);
    throw error;
  }
};

/**
 * Get bookings by date range
 */
export const getBookingsByDateRange = async (
  hotelId: string,
  startDate: Date,
  endDate: Date
): Promise<Booking[]> => {
  try {
    const startTimestamp = Timestamp.fromDate(startDate);
    const endTimestamp = Timestamp.fromDate(endDate);
    
    const bookingsQuery = query(
      collection(db, BOOKINGS_COLLECTION),
      where('hotelId', '==', hotelId),
      where('checkInDate', '>=', startTimestamp),
      where('checkInDate', '<=', endTimestamp),
      orderBy('checkInDate', 'asc')
    );
    
    const querySnapshot = await getDocs(bookingsQuery);
    const bookings: Booking[] = [];
    
    querySnapshot.forEach((doc) => {
      bookings.push({
        id: doc.id,
        ...doc.data() as Omit<Booking, 'id'>
      });
    });
    
    return bookings;
  } catch (error) {
    console.error('Error getting bookings by date range:', error);
    throw error;
  }
};

/**
 * Get booking statistics for a hotel
 */
export const getBookingStats = async (hotelId: string): Promise<{
  totalBookings: number;
  pendingBookings: number;
  confirmedBookings: number;
  checkedInBookings: number;
  checkedOutBookings: number;
  cancelledBookings: number;
  totalRevenue: number;
  averageBookingValue: number;
}> => {
  try {
    const bookings = await getBookingsForHotel(hotelId);
    
    const stats = {
      totalBookings: bookings.length,
      pendingBookings: bookings.filter(b => b.status === 'pending').length,
      confirmedBookings: bookings.filter(b => b.status === 'confirmed').length,
      checkedInBookings: bookings.filter(b => b.status === 'checked_in').length,
      checkedOutBookings: bookings.filter(b => b.status === 'checked_out').length,
      cancelledBookings: bookings.filter(b => b.status === 'cancelled').length,
      totalRevenue: bookings.reduce((sum, b) => sum + b.totalAmount, 0),
      averageBookingValue: 0
    };
    
    stats.averageBookingValue = stats.totalBookings > 0 
      ? stats.totalRevenue / stats.totalBookings 
      : 0;
    
    return stats;
  } catch (error) {
    console.error('Error getting booking stats:', error);
    throw error;
  }
};
