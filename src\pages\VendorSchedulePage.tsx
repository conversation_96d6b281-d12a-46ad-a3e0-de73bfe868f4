import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Grid,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Tabs,
  Tab,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  Badge,
  Tooltip
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Schedule as ScheduleIcon,
  Person as PersonIcon,
  AccessTime as TimeIcon,
  Work as WorkIcon,
  Event as EventIcon,
  Today as TodayIcon,
  DateRange as DateRangeIcon,
  CheckCircle as CheckIcon,
  Warning as WarningIcon,
  Refresh as RefreshIcon
} from '@mui/icons-material';
import { useAuth } from '../hooks/useAuth';
import { getHotelsByVendor } from '../services/hotelService';
import { format, addDays, startOfWeek, endOfWeek, eachDayOfInterval, isSameDay, isToday } from 'date-fns';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`schedule-tabpanel-${index}`}
      aria-labelledby={`schedule-tab-${index}`}
      {...other}
    >
      {value === index && <Box>{children}</Box>}
    </div>
  );
}

const VendorSchedulePage: React.FC = () => {
  const { user } = useAuth();
  const [hotels, setHotels] = useState<any[]>([]);
  const [selectedHotel, setSelectedHotel] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [tabValue, setTabValue] = useState(0);
  const [selectedDate, setSelectedDate] = useState(new Date());

  // Mock schedule data for demonstration
  const [mockSchedules] = useState<any[]>([
    {
      id: '1',
      staffName: 'Sarah Wilson',
      staffId: '1',
      role: 'Front Desk',
      date: new Date(),
      shift: 'morning',
      startTime: '08:00',
      endTime: '16:00',
      status: 'scheduled',
      tasks: ['Check-in guests', 'Handle reservations', 'Answer phone calls'],
      notes: 'Training new staff member'
    },
    {
      id: '2',
      staffName: 'Michael Rodriguez',
      staffId: '2',
      role: 'Housekeeping',
      date: new Date(),
      shift: 'morning',
      startTime: '09:00',
      endTime: '17:00',
      status: 'in_progress',
      tasks: ['Clean rooms 101-110', 'Laundry service', 'Inventory check'],
      notes: ''
    },
    {
      id: '3',
      staffName: 'Emily Chen',
      staffId: '3',
      role: 'Manager',
      date: new Date(),
      shift: 'full_day',
      startTime: '07:00',
      endTime: '19:00',
      status: 'scheduled',
      tasks: ['Staff meeting', 'Review reports', 'Guest relations'],
      notes: 'Monthly performance review'
    },
    {
      id: '4',
      staffName: 'James Thompson',
      staffId: '4',
      role: 'Maintenance',
      date: addDays(new Date(), 1),
      shift: 'evening',
      startTime: '14:00',
      endTime: '22:00',
      status: 'scheduled',
      tasks: ['AC maintenance Room 301', 'Plumbing check', 'Safety inspection'],
      notes: 'Emergency contact available'
    },
    {
      id: '5',
      staffName: 'Anna Kowalski',
      staffId: '5',
      role: 'Front Desk',
      date: addDays(new Date(), 1),
      shift: 'evening',
      startTime: '16:00',
      endTime: '00:00',
      status: 'scheduled',
      tasks: ['Evening check-ins', 'Night audit prep', 'Security rounds'],
      notes: ''
    },
    {
      id: '6',
      staffName: 'Sarah Wilson',
      staffId: '1',
      role: 'Front Desk',
      date: addDays(new Date(), 2),
      shift: 'morning',
      startTime: '08:00',
      endTime: '16:00',
      status: 'requested_off',
      tasks: [],
      notes: 'Personal day requested'
    }
  ]);

  useEffect(() => {
    const fetchHotels = async () => {
      if (!user?.vendorId) return;
      
      try {
        setLoading(true);
        const vendorHotels = await getHotelsByVendor(user.vendorId);
        setHotels(vendorHotels);
        if (vendorHotels.length > 0) {
          setSelectedHotel(vendorHotels[0].id);
        }
      } catch (err: any) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchHotels();
  }, [user]);

  const getShiftColor = (shift: string) => {
    switch (shift) {
      case 'morning':
        return '#4caf50';
      case 'evening':
        return '#ff9800';
      case 'night':
        return '#3f51b5';
      case 'full_day':
        return '#9c27b0';
      default:
        return '#757575';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'scheduled':
        return '#2196f3';
      case 'in_progress':
        return '#4caf50';
      case 'completed':
        return '#9e9e9e';
      case 'requested_off':
        return '#f44336';
      case 'sick_leave':
        return '#ff9800';
      default:
        return '#757575';
    }
  };

  const todaySchedules = mockSchedules.filter(s => isSameDay(s.date, new Date()));
  const weekStart = startOfWeek(selectedDate);
  const weekEnd = endOfWeek(selectedDate);
  const weekDays = eachDayOfInterval({ start: weekStart, end: weekEnd });

  const getSchedulesForDate = (date: Date) => {
    return mockSchedules.filter(s => isSameDay(s.date, date));
  };

  const totalHoursToday = todaySchedules.reduce((sum, schedule) => {
    const start = new Date(`2000-01-01 ${schedule.startTime}`);
    const end = new Date(`2000-01-01 ${schedule.endTime}`);
    return sum + (end.getTime() - start.getTime()) / (1000 * 60 * 60);
  }, 0);

  const activeStaff = todaySchedules.filter(s => s.status === 'in_progress').length;
  const scheduledStaff = todaySchedules.filter(s => s.status === 'scheduled').length;

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 3 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1">
          Staff Schedule Management
        </Typography>
        <Box display="flex" gap={2}>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={() => window.location.reload()}
          >
            Refresh
          </Button>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => setDialogOpen(true)}
          >
            Add Schedule
          </Button>
        </Box>
      </Box>

      {/* Today's Overview */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Avatar sx={{ bgcolor: '#4caf50', mx: 'auto', mb: 1 }}>
                <CheckIcon />
              </Avatar>
              <Typography variant="h4" color="success.main">
                {activeStaff}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                Currently Working
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Avatar sx={{ bgcolor: '#2196f3', mx: 'auto', mb: 1 }}>
                <ScheduleIcon />
              </Avatar>
              <Typography variant="h4" color="primary.main">
                {scheduledStaff}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                Scheduled Today
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Avatar sx={{ bgcolor: '#ff9800', mx: 'auto', mb: 1 }}>
                <TimeIcon />
              </Avatar>
              <Typography variant="h4" color="warning.main">
                {totalHoursToday.toFixed(0)}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                Total Hours Today
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Avatar sx={{ bgcolor: '#9c27b0', mx: 'auto', mb: 1 }}>
                <TodayIcon />
              </Avatar>
              <Typography variant="h4" color="secondary.main">
                {todaySchedules.length}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                Total Shifts Today
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Schedule Tabs */}
      <Card>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={(_, newValue) => setTabValue(newValue)}>
            <Tab label="Today's Schedule" />
            <Tab label="Weekly View" />
            <Tab label="Staff Assignments" />
            <Tab label="Time Off Requests" />
          </Tabs>
        </Box>

        <TabPanel value={tabValue} index={0}>
          <Box p={3}>
            <Typography variant="h6" gutterBottom>
              Today's Schedule - {format(new Date(), 'EEEE, MMMM do, yyyy')}
            </Typography>
            
            {todaySchedules.length === 0 ? (
              <Box textAlign="center" py={4}>
                <ScheduleIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                <Typography variant="h6" gutterBottom>
                  No schedules for today
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  Add staff schedules to manage daily operations.
                </Typography>
              </Box>
            ) : (
              <Grid container spacing={3}>
                {todaySchedules.map((schedule) => (
                  <Grid item xs={12} md={6} lg={4} key={schedule.id}>
                    <Card
                      sx={{
                        border: `2px solid ${getStatusColor(schedule.status)}`,
                        '&:hover': {
                          boxShadow: 3,
                          transform: 'translateY(-2px)',
                          transition: 'all 0.2s'
                        }
                      }}
                    >
                      <CardContent>
                        <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
                          <Box>
                            <Typography variant="h6">
                              {schedule.staffName}
                            </Typography>
                            <Typography variant="body2" color="textSecondary">
                              {schedule.role}
                            </Typography>
                          </Box>
                          <Chip
                            label={schedule.status.replace('_', ' ').toUpperCase()}
                            size="small"
                            sx={{
                              backgroundColor: getStatusColor(schedule.status),
                              color: 'white'
                            }}
                          />
                        </Box>

                        <Box display="flex" alignItems="center" gap={1} mb={2}>
                          <TimeIcon sx={{ fontSize: 16, color: 'text.secondary' }} />
                          <Typography variant="body2">
                            {schedule.startTime} - {schedule.endTime}
                          </Typography>
                          <Chip
                            label={schedule.shift.replace('_', ' ').toUpperCase()}
                            size="small"
                            variant="outlined"
                            sx={{
                              borderColor: getShiftColor(schedule.shift),
                              color: getShiftColor(schedule.shift),
                              fontSize: '0.7rem'
                            }}
                          />
                        </Box>

                        {schedule.tasks.length > 0 && (
                          <Box mb={2}>
                            <Typography variant="subtitle2" gutterBottom>
                              Tasks:
                            </Typography>
                            <List dense>
                              {schedule.tasks.map((task: string, index: number) => (
                                <ListItem key={index} sx={{ py: 0.5, px: 0 }}>
                                  <ListItemIcon sx={{ minWidth: 20 }}>
                                    <WorkIcon sx={{ fontSize: 14 }} />
                                  </ListItemIcon>
                                  <ListItemText
                                    primary={task}
                                    primaryTypographyProps={{ variant: 'body2' }}
                                  />
                                </ListItem>
                              ))}
                            </List>
                          </Box>
                        )}

                        {schedule.notes && (
                          <Alert severity="info" sx={{ mb: 2, fontSize: '0.8rem' }}>
                            {schedule.notes}
                          </Alert>
                        )}

                        <Box display="flex" justifyContent="space-between" alignItems="center">
                          <Typography variant="caption" color="textSecondary">
                            {format(schedule.date, 'MMM dd, yyyy')}
                          </Typography>
                          <Box>
                            <Tooltip title="Edit Schedule">
                              <IconButton size="small" color="primary">
                                <EditIcon />
                              </IconButton>
                            </Tooltip>
                            <Tooltip title="Delete Schedule">
                              <IconButton size="small" color="error">
                                <DeleteIcon />
                              </IconButton>
                            </Tooltip>
                          </Box>
                        </Box>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            )}
          </Box>
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          <Box p={3}>
            <Typography variant="h6" gutterBottom>
              Weekly Schedule - {format(weekStart, 'MMM dd')} to {format(weekEnd, 'MMM dd, yyyy')}
            </Typography>
            
            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Staff</TableCell>
                    {weekDays.map((day) => (
                      <TableCell key={day.toISOString()} align="center">
                        <Box>
                          <Typography variant="body2" fontWeight="bold">
                            {format(day, 'EEE')}
                          </Typography>
                          <Typography variant="caption" color={isToday(day) ? 'primary' : 'textSecondary'}>
                            {format(day, 'MMM dd')}
                          </Typography>
                        </Box>
                      </TableCell>
                    ))}
                  </TableRow>
                </TableHead>
                <TableBody>
                  {['Sarah Wilson', 'Michael Rodriguez', 'Emily Chen', 'James Thompson', 'Anna Kowalski'].map((staffName) => (
                    <TableRow key={staffName}>
                      <TableCell>
                        <Box display="flex" alignItems="center">
                          <Avatar sx={{ mr: 2, width: 32, height: 32 }}>
                            {staffName.split(' ').map((n: string) => n[0]).join('')}
                          </Avatar>
                          <Typography variant="body2">
                            {staffName}
                          </Typography>
                        </Box>
                      </TableCell>
                      {weekDays.map((day) => {
                        const daySchedules = getSchedulesForDate(day).filter(s => s.staffName === staffName);
                        return (
                          <TableCell key={day.toISOString()} align="center">
                            {daySchedules.map((schedule) => (
                              <Chip
                                key={schedule.id}
                                label={`${schedule.startTime}-${schedule.endTime}`}
                                size="small"
                                sx={{
                                  backgroundColor: getShiftColor(schedule.shift),
                                  color: 'white',
                                  fontSize: '0.7rem',
                                  mb: 0.5
                                }}
                              />
                            ))}
                          </TableCell>
                        );
                      })}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Box>
        </TabPanel>

        <TabPanel value={tabValue} index={2}>
          <Box p={3}>
            <Typography variant="h6" gutterBottom>
              Staff Assignments
            </Typography>
            <Typography variant="body2" color="textSecondary" paragraph>
              Manage staff roles and responsibilities for different shifts.
            </Typography>
            
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Morning Shift (8:00 AM - 4:00 PM)
                    </Typography>
                    <List>
                      <ListItem>
                        <ListItemIcon>
                          <PersonIcon />
                        </ListItemIcon>
                        <ListItemText
                          primary="Sarah Wilson"
                          secondary="Front Desk Manager"
                        />
                      </ListItem>
                      <ListItem>
                        <ListItemIcon>
                          <PersonIcon />
                        </ListItemIcon>
                        <ListItemText
                          primary="Michael Rodriguez"
                          secondary="Housekeeping Supervisor"
                        />
                      </ListItem>
                    </List>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} md={6}>
                <Card>
                  <CardContent>
                    <Typography variant="h6" gutterBottom>
                      Evening Shift (4:00 PM - 12:00 AM)
                    </Typography>
                    <List>
                      <ListItem>
                        <ListItemIcon>
                          <PersonIcon />
                        </ListItemIcon>
                        <ListItemText
                          primary="Anna Kowalski"
                          secondary="Front Desk Associate"
                        />
                      </ListItem>
                      <ListItem>
                        <ListItemIcon>
                          <PersonIcon />
                        </ListItemIcon>
                        <ListItemText
                          primary="James Thompson"
                          secondary="Maintenance Technician"
                        />
                      </ListItem>
                    </List>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          </Box>
        </TabPanel>

        <TabPanel value={tabValue} index={3}>
          <Box p={3}>
            <Typography variant="h6" gutterBottom>
              Time Off Requests
            </Typography>
            
            <Alert severity="info" sx={{ mb: 3 }}>
              You have 1 pending time off request that requires approval.
            </Alert>

            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Staff Member</TableCell>
                    <TableCell>Request Date</TableCell>
                    <TableCell>Type</TableCell>
                    <TableCell>Reason</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell align="center">Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  <TableRow>
                    <TableCell>Sarah Wilson</TableCell>
                    <TableCell>{format(addDays(new Date(), 2), 'MMM dd, yyyy')}</TableCell>
                    <TableCell>Personal Day</TableCell>
                    <TableCell>Family appointment</TableCell>
                    <TableCell>
                      <Chip
                        label="Pending"
                        size="small"
                        color="warning"
                      />
                    </TableCell>
                    <TableCell align="center">
                      <Button size="small" color="success" sx={{ mr: 1 }}>
                        Approve
                      </Button>
                      <Button size="small" color="error">
                        Deny
                      </Button>
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </TableContainer>
          </Box>
        </TabPanel>
      </Card>
    </Box>
  );
};

export default VendorSchedulePage;
