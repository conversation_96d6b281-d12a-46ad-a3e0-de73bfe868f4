import { logError, showErrorMessage } from '../utils/errorHandler';

// Aadhaar verification response interface
export interface AadhaarVerificationResponse {
  success: boolean;
  verified: boolean;
  data?: {
    aadhaarNumber: string;
    name: string;
    dateOfBirth: string;
    gender: string;
    address: {
      street: string;
      city: string;
      state: string;
      pincode: string;
    };
    photo?: string;
  };
  message: string;
  transactionId?: string;
}

// Aadhaar OTP request interface
export interface AadhaarOTPRequest {
  aadhaarNumber: string;
  captcha?: string;
}

// Aadhaar OTP verification interface
export interface AadhaarOTPVerification {
  aadhaarNumber: string;
  otp: string;
  transactionId: string;
}

// Sandbox Aadhaar API configuration
const AADHAAR_API_CONFIG = {
  baseUrl: process.env.REACT_APP_AADHAAR_API_URL || 'https://sandbox-aadhaar-api.example.com',
  apiKey: process.env.REACT_APP_AADHAAR_API_KEY || 'sandbox_key_123',
  timeout: 30000, // 30 seconds
};

/**
 * Validate Aadhaar number format
 */
export const validateAadhaarNumber = (aadhaarNumber: string): boolean => {
  // Remove spaces and hyphens
  const cleanNumber = aadhaarNumber.replace(/[\s-]/g, '');
  
  // Check if it's 12 digits
  if (!/^\d{12}$/.test(cleanNumber)) {
    return false;
  }
  
  // Aadhaar number validation using Verhoeff algorithm (simplified)
  // In production, use the complete Verhoeff algorithm
  return true;
};

/**
 * Format Aadhaar number for display (XXXX XXXX XXXX)
 */
export const formatAadhaarNumber = (aadhaarNumber: string): string => {
  const cleanNumber = aadhaarNumber.replace(/[\s-]/g, '');
  return cleanNumber.replace(/(\d{4})(\d{4})(\d{4})/, '$1 $2 $3');
};

/**
 * Mask Aadhaar number for security (XXXX XXXX 1234)
 */
export const maskAadhaarNumber = (aadhaarNumber: string): string => {
  const cleanNumber = aadhaarNumber.replace(/[\s-]/g, '');
  if (cleanNumber.length !== 12) return aadhaarNumber;
  
  return `XXXX XXXX ${cleanNumber.slice(-4)}`;
};

/**
 * Send OTP to Aadhaar number (Step 1)
 */
export const sendAadhaarOTP = async (request: AadhaarOTPRequest): Promise<{
  success: boolean;
  transactionId?: string;
  message: string;
}> => {
  try {
    // Validate Aadhaar number
    if (!validateAadhaarNumber(request.aadhaarNumber)) {
      throw new Error('Invalid Aadhaar number format');
    }

    // Sandbox API call
    const response = await fetch(`${AADHAAR_API_CONFIG.baseUrl}/api/v1/send-otp`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${AADHAAR_API_CONFIG.apiKey}`,
        'X-API-Version': '1.0'
      },
      body: JSON.stringify({
        aadhaar_number: request.aadhaarNumber.replace(/[\s-]/g, ''),
        captcha: request.captcha
      }),
      signal: AbortSignal.timeout(AADHAAR_API_CONFIG.timeout)
    });

    if (!response.ok) {
      throw new Error(`API request failed: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();

    return {
      success: data.success || false,
      transactionId: data.transaction_id,
      message: data.message || 'OTP sent successfully'
    };

  } catch (error: any) {
    logError(error, 'Aadhaar OTP sending');
    
    // Handle specific error cases
    if (error.name === 'AbortError') {
      return {
        success: false,
        message: 'Request timeout. Please try again.'
      };
    }

    // For sandbox/demo purposes, simulate success
    if (process.env.NODE_ENV === 'development') {
      console.log('🔧 Development mode: Simulating Aadhaar OTP send');
      return {
        success: true,
        transactionId: `txn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        message: 'OTP sent successfully (Demo mode)'
      };
    }

    return {
      success: false,
      message: showErrorMessage(error)
    };
  }
};

/**
 * Verify OTP and get Aadhaar details (Step 2)
 */
export const verifyAadhaarOTP = async (verification: AadhaarOTPVerification): Promise<AadhaarVerificationResponse> => {
  try {
    // Validate inputs
    if (!validateAadhaarNumber(verification.aadhaarNumber)) {
      throw new Error('Invalid Aadhaar number format');
    }

    if (!verification.otp || verification.otp.length !== 6) {
      throw new Error('Invalid OTP format');
    }

    if (!verification.transactionId) {
      throw new Error('Transaction ID is required');
    }

    // Sandbox API call
    const response = await fetch(`${AADHAAR_API_CONFIG.baseUrl}/api/v1/verify-otp`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${AADHAAR_API_CONFIG.apiKey}`,
        'X-API-Version': '1.0'
      },
      body: JSON.stringify({
        aadhaar_number: verification.aadhaarNumber.replace(/[\s-]/g, ''),
        otp: verification.otp,
        transaction_id: verification.transactionId
      }),
      signal: AbortSignal.timeout(AADHAAR_API_CONFIG.timeout)
    });

    if (!response.ok) {
      throw new Error(`API request failed: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();

    return {
      success: data.success || false,
      verified: data.verified || false,
      data: data.aadhaar_data ? {
        aadhaarNumber: data.aadhaar_data.aadhaar_number,
        name: data.aadhaar_data.name,
        dateOfBirth: data.aadhaar_data.date_of_birth,
        gender: data.aadhaar_data.gender,
        address: {
          street: data.aadhaar_data.address?.street || '',
          city: data.aadhaar_data.address?.city || '',
          state: data.aadhaar_data.address?.state || '',
          pincode: data.aadhaar_data.address?.pincode || ''
        },
        photo: data.aadhaar_data.photo
      } : undefined,
      message: data.message || 'Verification completed',
      transactionId: verification.transactionId
    };

  } catch (error: any) {
    logError(error, 'Aadhaar OTP verification');

    // Handle specific error cases
    if (error.name === 'AbortError') {
      return {
        success: false,
        verified: false,
        message: 'Request timeout. Please try again.'
      };
    }

    // For sandbox/demo purposes, simulate success with mock data
    if (process.env.NODE_ENV === 'development') {
      console.log('🔧 Development mode: Simulating Aadhaar verification');
      
      // Simulate different responses based on OTP
      if (verification.otp === '123456') {
        return {
          success: true,
          verified: true,
          data: {
            aadhaarNumber: verification.aadhaarNumber,
            name: 'John Doe',
            dateOfBirth: '1990-01-15',
            gender: 'Male',
            address: {
              street: '123 Main Street',
              city: 'Mumbai',
              state: 'Maharashtra',
              pincode: '400001'
            }
          },
          message: 'Aadhaar verified successfully (Demo mode)',
          transactionId: verification.transactionId
        };
      } else {
        return {
          success: false,
          verified: false,
          message: 'Invalid OTP. Use 123456 for demo.'
        };
      }
    }

    return {
      success: false,
      verified: false,
      message: showErrorMessage(error)
    };
  }
};

/**
 * Check if Aadhaar verification is required for booking
 */
export const isAadhaarVerificationRequired = (bookingData: any): boolean => {
  // Aadhaar verification is required for:
  // 1. Indian guests (based on phone number or nationality)
  // 2. Bookings above certain amount
  // 3. Extended stays
  
  const phoneNumber = bookingData.guestPhone || '';
  const isIndianPhone = phoneNumber.startsWith('+91') || phoneNumber.startsWith('91') || phoneNumber.length === 10;
  
  return isIndianPhone; // Simplified logic
};

/**
 * Get Aadhaar verification status for a booking
 */
export const getAadhaarVerificationStatus = (bookingData: any): {
  required: boolean;
  completed: boolean;
  verificationData?: any;
} => {
  return {
    required: isAadhaarVerificationRequired(bookingData),
    completed: !!bookingData.aadhaarVerification?.verified,
    verificationData: bookingData.aadhaarVerification
  };
};

export default {
  validateAadhaarNumber,
  formatAadhaarNumber,
  maskAadhaarNumber,
  sendAadhaarOTP,
  verifyAadhaarOTP,
  isAadhaarVerificationRequired,
  getAadhaarVerificationStatus
};
