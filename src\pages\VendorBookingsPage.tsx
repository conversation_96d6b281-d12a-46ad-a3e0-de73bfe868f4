import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Grid,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Tabs,
  Tab,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  Badge,
  Tooltip
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  CheckCircle as ConfirmedIcon,
  Schedule as PendingIcon,
  Cancel as CancelledIcon,
  Payment as PaymentIcon,
  Person as GuestIcon,
  Hotel as RoomIcon,
  DateRange as DateIcon,
  AttachMoney as MoneyIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  Print as PrintIcon,
  Send as SendIcon
} from '@mui/icons-material';
import { useAuth } from '../hooks/useAuth';
import { Booking } from '../types';
import { getBookingsForHotel, createBooking, updateBooking, cancelBooking } from '../services/bookingService';
import { getHotelsByVendor } from '../services/hotelService';
import { format, addDays, differenceInDays } from 'date-fns';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`bookings-tabpanel-${index}`}
      aria-labelledby={`bookings-tab-${index}`}
      {...other}
    >
      {value === index && <Box>{children}</Box>}
    </div>
  );
}

const VendorBookingsPage: React.FC = () => {
  const { user } = useAuth();
  const [hotels, setHotels] = useState<any[]>([]);
  const [selectedHotel, setSelectedHotel] = useState<string>('');
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [viewDialogOpen, setViewDialogOpen] = useState(false);
  const [editingBooking, setEditingBooking] = useState<Booking | null>(null);
  const [viewingBooking, setViewingBooking] = useState<any | null>(null);
  const [tabValue, setTabValue] = useState(0);

  // Mock booking data for demonstration
  const [mockBookings] = useState<any[]>([
    {
      id: '1',
      bookingNumber: 'BK001',
      guestName: 'John Smith',
      guestEmail: '<EMAIL>',
      guestPhone: '+****************',
      roomNumber: '101',
      roomType: 'Standard',
      checkInDate: new Date(),
      checkOutDate: addDays(new Date(), 3),
      numberOfGuests: 2,
      totalAmount: 360,
      paidAmount: 360,
      status: 'confirmed',
      paymentStatus: 'paid',
      bookingDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
      specialRequests: 'Late check-in requested',
      source: 'website',
      notes: 'VIP guest - provide welcome amenities'
    },
    {
      id: '2',
      bookingNumber: 'BK002',
      guestName: 'Sarah Johnson',
      guestEmail: '<EMAIL>',
      guestPhone: '+****************',
      roomNumber: '205',
      roomType: 'Deluxe',
      checkInDate: addDays(new Date(), 1),
      checkOutDate: addDays(new Date(), 4),
      numberOfGuests: 1,
      totalAmount: 540,
      paidAmount: 0,
      status: 'pending',
      paymentStatus: 'pending',
      bookingDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
      specialRequests: 'Non-smoking room',
      source: 'phone',
      notes: 'Business traveler'
    },
    {
      id: '3',
      bookingNumber: 'BK003',
      guestName: 'Michael Brown',
      guestEmail: '<EMAIL>',
      guestPhone: '+****************',
      roomNumber: '301',
      roomType: 'Suite',
      checkInDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
      checkOutDate: addDays(new Date(), 2),
      numberOfGuests: 4,
      totalAmount: 900,
      paidAmount: 450,
      status: 'confirmed',
      paymentStatus: 'partial',
      bookingDate: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
      specialRequests: 'Extra bed required',
      source: 'booking.com',
      notes: 'Family vacation'
    },
    {
      id: '4',
      bookingNumber: 'BK004',
      guestName: 'Emily Davis',
      guestEmail: '<EMAIL>',
      guestPhone: '+****************',
      roomNumber: '102',
      roomType: 'Standard',
      checkInDate: addDays(new Date(), 7),
      checkOutDate: addDays(new Date(), 10),
      numberOfGuests: 2,
      totalAmount: 360,
      paidAmount: 0,
      status: 'cancelled',
      paymentStatus: 'refunded',
      bookingDate: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
      specialRequests: '',
      source: 'website',
      notes: 'Cancelled due to emergency'
    },
    {
      id: '5',
      bookingNumber: 'BK005',
      guestName: 'David Wilson',
      guestEmail: '<EMAIL>',
      guestPhone: '+****************',
      roomNumber: '203',
      roomType: 'Deluxe',
      checkInDate: addDays(new Date(), 14),
      checkOutDate: addDays(new Date(), 17),
      numberOfGuests: 2,
      totalAmount: 540,
      paidAmount: 540,
      status: 'confirmed',
      paymentStatus: 'paid',
      bookingDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
      specialRequests: 'Anniversary celebration',
      source: 'website',
      notes: 'Honeymoon package requested'
    }
  ]);

  useEffect(() => {
    const fetchHotels = async () => {
      if (!user?.vendorId) return;
      
      try {
        setLoading(true);
        const vendorHotels = await getHotelsByVendor(user.vendorId);
        setHotels(vendorHotels);
        if (vendorHotels.length > 0) {
          setSelectedHotel(vendorHotels[0].id);
        }
      } catch (err: any) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchHotels();
  }, [user]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed':
        return '#4caf50';
      case 'pending':
        return '#ff9800';
      case 'cancelled':
        return '#f44336';
      case 'checked_in':
        return '#2196f3';
      case 'checked_out':
        return '#9e9e9e';
      default:
        return '#757575';
    }
  };

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'paid':
        return '#4caf50';
      case 'partial':
        return '#ff9800';
      case 'pending':
        return '#f44336';
      case 'refunded':
        return '#9e9e9e';
      default:
        return '#757575';
    }
  };

  // Handler functions for booking actions
  const handleViewBooking = (booking: any) => {
    setViewingBooking(booking);
    setViewDialogOpen(true);
  };

  const handleEditBooking = (booking: any) => {
    setEditingBooking(booking);
    setDialogOpen(true);
  };

  const handlePrintBooking = (booking: any) => {
    if (!booking) {
      alert('No booking selected for printing');
      return;
    }

    console.log('Printing booking:', booking.bookingNumber);

    // Create a new window for printing
    const printWindow = window.open('', '_blank');
    if (!printWindow) {
      alert('Please allow popups to print booking confirmation');
      return;
    }

    // Generate booking confirmation HTML
    const bookingHTML = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Booking Confirmation - ${booking.bookingNumber}</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; }
          .header { text-align: center; border-bottom: 2px solid #333; padding-bottom: 20px; margin-bottom: 20px; }
          .booking-details { margin-bottom: 20px; }
          .section { margin-bottom: 15px; }
          .label { font-weight: bold; }
          .value { margin-left: 10px; }
          .footer { margin-top: 30px; text-align: center; font-size: 12px; color: #666; }
          @media print { body { margin: 0; } }
        </style>
      </head>
      <body>
        <div class="header">
          <h1>Booking Confirmation</h1>
          <h2>${booking.hotelName || 'Hotel Name'}</h2>
          <p>Booking Number: <strong>${booking.bookingNumber}</strong></p>
        </div>

        <div class="booking-details">
          <div class="section">
            <h3>Guest Information</h3>
            <p><span class="label">Name:</span><span class="value">${booking.guestName}</span></p>
            <p><span class="label">Email:</span><span class="value">${booking.guestEmail}</span></p>
            <p><span class="label">Phone:</span><span class="value">${booking.guestPhone}</span></p>
          </div>

          <div class="section">
            <h3>Booking Details</h3>
            <p><span class="label">Room Number:</span><span class="value">${booking.roomNumber}</span></p>
            <p><span class="label">Room Type:</span><span class="value">${booking.roomType}</span></p>
            <p><span class="label">Check-in Date:</span><span class="value">${booking.checkInDate}</span></p>
            <p><span class="label">Check-out Date:</span><span class="value">${booking.checkOutDate}</span></p>
            <p><span class="label">Number of Guests:</span><span class="value">${booking.numberOfGuests}</span></p>
            <p><span class="label">Status:</span><span class="value">${booking.status}</span></p>
          </div>

          <div class="section">
            <h3>Payment Information</h3>
            <p><span class="label">Total Amount:</span><span class="value">$${booking.totalAmount}</span></p>
            <p><span class="label">Paid Amount:</span><span class="value">$${booking.paidAmount}</span></p>
            <p><span class="label">Payment Status:</span><span class="value">${booking.paymentStatus}</span></p>
          </div>
        </div>

        <div class="footer">
          <p>Generated on ${new Date().toLocaleString()}</p>
          <p>Thank you for choosing our hotel!</p>
        </div>
      </body>
      </html>
    `;

    // Write HTML to print window and print
    printWindow.document.write(bookingHTML);
    printWindow.document.close();
    printWindow.focus();
    printWindow.print();
    printWindow.close();
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'confirmed':
        return <ConfirmedIcon sx={{ color: 'white' }} />;
      case 'pending':
        return <PendingIcon sx={{ color: 'white' }} />;
      case 'cancelled':
        return <CancelledIcon sx={{ color: 'white' }} />;
      default:
        return <ConfirmedIcon sx={{ color: 'white' }} />;
    }
  };

  const confirmedBookings = mockBookings.filter(b => b.status === 'confirmed');
  const pendingBookings = mockBookings.filter(b => b.status === 'pending');
  const cancelledBookings = mockBookings.filter(b => b.status === 'cancelled');
  const todayCheckIns = mockBookings.filter(b => 
    format(b.checkInDate, 'yyyy-MM-dd') === format(new Date(), 'yyyy-MM-dd')
  );
  const todayCheckOuts = mockBookings.filter(b => 
    format(b.checkOutDate, 'yyyy-MM-dd') === format(new Date(), 'yyyy-MM-dd')
  );

  const totalRevenue = mockBookings
    .filter(b => b.status !== 'cancelled')
    .reduce((sum, b) => sum + b.totalAmount, 0);

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 3 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1">
          Booking Management
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => setDialogOpen(true)}
        >
          New Booking
        </Button>
      </Box>

      {/* Booking Statistics */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Avatar sx={{ bgcolor: '#4caf50', mx: 'auto', mb: 1 }}>
                <ConfirmedIcon />
              </Avatar>
              <Typography variant="h4" color="success.main">
                {confirmedBookings.length}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                Confirmed Bookings
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Avatar sx={{ bgcolor: '#ff9800', mx: 'auto', mb: 1 }}>
                <PendingIcon />
              </Avatar>
              <Typography variant="h4" color="warning.main">
                {pendingBookings.length}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                Pending Bookings
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Avatar sx={{ bgcolor: '#2196f3', mx: 'auto', mb: 1 }}>
                <DateIcon />
              </Avatar>
              <Typography variant="h4" color="primary.main">
                {todayCheckIns.length}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                Today's Check-ins
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Avatar sx={{ bgcolor: '#9c27b0', mx: 'auto', mb: 1 }}>
                <MoneyIcon />
              </Avatar>
              <Typography variant="h4" color="secondary.main">
                ${totalRevenue.toLocaleString()}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                Total Revenue
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Today's Activities */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Today's Check-ins
              </Typography>
              {todayCheckIns.length === 0 ? (
                <Typography variant="body2" color="textSecondary">
                  No check-ins scheduled for today
                </Typography>
              ) : (
                <List dense>
                  {todayCheckIns.map((booking) => (
                    <ListItem key={booking.id}>
                      <ListItemIcon>
                        <GuestIcon />
                      </ListItemIcon>
                      <ListItemText
                        primary={`${booking.guestName} - Room ${booking.roomNumber}`}
                        secondary={`${booking.numberOfGuests} guests • ${booking.roomType}`}
                      />
                    </ListItem>
                  ))}
                </List>
              )}
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Today's Check-outs
              </Typography>
              {todayCheckOuts.length === 0 ? (
                <Typography variant="body2" color="textSecondary">
                  No check-outs scheduled for today
                </Typography>
              ) : (
                <List dense>
                  {todayCheckOuts.map((booking) => (
                    <ListItem key={booking.id}>
                      <ListItemIcon>
                        <GuestIcon />
                      </ListItemIcon>
                      <ListItemText
                        primary={`${booking.guestName} - Room ${booking.roomNumber}`}
                        secondary={`${booking.numberOfGuests} guests • ${booking.roomType}`}
                      />
                    </ListItem>
                  ))}
                </List>
              )}
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Bookings Tabs */}
      <Card>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={(_, newValue) => setTabValue(newValue)}>
            <Tab label={`All Bookings (${mockBookings.length})`} />
            <Tab label={`Confirmed (${confirmedBookings.length})`} />
            <Tab label={`Pending (${pendingBookings.length})`} />
            <Tab label={`Cancelled (${cancelledBookings.length})`} />
          </Tabs>
        </Box>

        <TabPanel value={tabValue} index={0}>
          <Box p={3}>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Booking #</TableCell>
                    <TableCell>Guest</TableCell>
                    <TableCell>Room</TableCell>
                    <TableCell>Dates</TableCell>
                    <TableCell>Amount</TableCell>
                    <TableCell>Status</TableCell>
                    <TableCell>Payment</TableCell>
                    <TableCell align="center">Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {mockBookings.map((booking) => (
                    <TableRow key={booking.id}>
                      <TableCell>
                        <Typography variant="body2" fontWeight="bold">
                          {booking.bookingNumber}
                        </Typography>
                        <Typography variant="caption" color="textSecondary">
                          {format(booking.bookingDate, 'MMM dd, yyyy')}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Box>
                          <Typography variant="body2" fontWeight="bold">
                            {booking.guestName}
                          </Typography>
                          <Typography variant="caption" color="textSecondary">
                            {booking.guestEmail}
                          </Typography>
                          <br />
                          <Typography variant="caption" color="textSecondary">
                            {booking.guestPhone}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          Room {booking.roomNumber}
                        </Typography>
                        <Typography variant="caption" color="textSecondary">
                          {booking.roomType} • {booking.numberOfGuests} guests
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2">
                          {format(booking.checkInDate, 'MMM dd')} - {format(booking.checkOutDate, 'MMM dd')}
                        </Typography>
                        <Typography variant="caption" color="textSecondary">
                          {differenceInDays(booking.checkOutDate, booking.checkInDate)} nights
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Typography variant="body2" fontWeight="bold">
                          ${booking.totalAmount}
                        </Typography>
                        <Typography variant="caption" color="textSecondary">
                          Paid: ${booking.paidAmount}
                        </Typography>
                      </TableCell>
                      <TableCell>
                        <Chip
                          icon={getStatusIcon(booking.status)}
                          label={booking.status.charAt(0).toUpperCase() + booking.status.slice(1)}
                          size="small"
                          sx={{
                            backgroundColor: getStatusColor(booking.status),
                            color: 'white',
                            '& .MuiChip-icon': {
                              color: 'white'
                            }
                          }}
                        />
                      </TableCell>
                      <TableCell>
                        <Chip
                          label={booking.paymentStatus.charAt(0).toUpperCase() + booking.paymentStatus.slice(1)}
                          size="small"
                          variant="outlined"
                          sx={{
                            borderColor: getPaymentStatusColor(booking.paymentStatus),
                            color: getPaymentStatusColor(booking.paymentStatus)
                          }}
                        />
                      </TableCell>
                      <TableCell align="center">
                        <Tooltip title="View Details">
                          <IconButton size="small" color="primary" onClick={() => handleViewBooking(booking)}>
                            <ViewIcon />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Edit Booking">
                          <IconButton size="small" color="secondary" onClick={() => handleEditBooking(booking)}>
                            <EditIcon />
                          </IconButton>
                        </Tooltip>
                        <Tooltip title="Print">
                          <IconButton size="small" onClick={() => handlePrintBooking(booking)}>
                            <PrintIcon />
                          </IconButton>
                        </Tooltip>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Box>
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          <Box p={3}>
            <Typography variant="h6" gutterBottom>
              Confirmed Bookings ({confirmedBookings.length})
            </Typography>
            {/* Similar table structure for confirmed bookings */}
          </Box>
        </TabPanel>

        <TabPanel value={tabValue} index={2}>
          <Box p={3}>
            <Typography variant="h6" gutterBottom>
              Pending Bookings ({pendingBookings.length})
            </Typography>
            {pendingBookings.length === 0 ? (
              <Box textAlign="center" py={4}>
                <PendingIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                <Typography variant="h6" gutterBottom>
                  No pending bookings
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  All bookings have been confirmed or cancelled.
                </Typography>
              </Box>
            ) : (
              <Alert severity="warning" sx={{ mb: 2 }}>
                You have {pendingBookings.length} pending bookings that require attention.
              </Alert>
            )}
          </Box>
        </TabPanel>

        <TabPanel value={tabValue} index={3}>
          <Box p={3}>
            <Typography variant="h6" gutterBottom>
              Cancelled Bookings ({cancelledBookings.length})
            </Typography>
            {/* Similar table structure for cancelled bookings */}
          </Box>
        </TabPanel>
      </Card>

      {/* View Booking Dialog */}
      <Dialog open={viewDialogOpen} onClose={() => setViewDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          Booking Details: {viewingBooking?.bookingNumber}
        </DialogTitle>
        <DialogContent>
          {viewingBooking && (
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Typography variant="h6" gutterBottom>
                  Guest Information
                </Typography>
                <List>
                  <ListItem>
                    <ListItemIcon>
                      <GuestIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Guest Name"
                      secondary={viewingBooking.guestName}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      <EmailIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Email"
                      secondary={viewingBooking.guestEmail}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      <PhoneIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Phone"
                      secondary={viewingBooking.guestPhone}
                    />
                  </ListItem>
                </List>
              </Grid>

              <Grid item xs={12} md={6}>
                <Typography variant="h6" gutterBottom>
                  Booking Information
                </Typography>
                <List>
                  <ListItem>
                    <ListItemIcon>
                      <RoomIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Room"
                      secondary={`Room ${viewingBooking.roomNumber} (${viewingBooking.roomType})`}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      <DateIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Stay Duration"
                      secondary={`${viewingBooking.checkInDate} - ${viewingBooking.checkOutDate} (${viewingBooking.nights} nights)`}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      <GuestIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Guests"
                      secondary={`${viewingBooking.numberOfGuests} guest(s)`}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      <MoneyIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Payment"
                      secondary={`Total: $${viewingBooking.totalAmount} | Paid: $${viewingBooking.paidAmount} | Status: ${viewingBooking.paymentStatus}`}
                    />
                  </ListItem>
                </List>
              </Grid>
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => {
              setViewDialogOpen(false);
              handleEditBooking(viewingBooking);
            }}
            startIcon={<EditIcon />}
            color="primary"
          >
            Edit Booking
          </Button>
          <Button onClick={() => handlePrintBooking(viewingBooking)} startIcon={<PrintIcon />}>
            Print
          </Button>
          <Button onClick={() => setViewDialogOpen(false)}>
            Close
          </Button>
        </DialogActions>
      </Dialog>

      {/* Edit Booking Dialog */}
      <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingBooking ? 'Edit Booking' : 'New Booking'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Guest Name"
                value={editingBooking?.guestName || ''}
                disabled
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Guest Email"
                value={editingBooking?.guestEmail || ''}
                disabled
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Guest Phone"
                value={editingBooking?.guestPhone || ''}
                disabled
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Room Number"
                value={editingBooking?.roomNumber || ''}
                disabled
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Check-in Date"
                value={editingBooking?.checkInDate || ''}
                disabled
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Check-out Date"
                value={editingBooking?.checkOutDate || ''}
                disabled
              />
            </Grid>
            <Grid item xs={12}>
              <Alert severity="info">
                This is a demo version. Full editing functionality will be available in the complete system.
              </Alert>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDialogOpen(false)}>
            Cancel
          </Button>
          <Button variant="contained" onClick={() => setDialogOpen(false)}>
            Save Changes
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default VendorBookingsPage;
