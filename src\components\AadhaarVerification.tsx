import React, { useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  <PERSON>alogA<PERSON>,
  Button,
  TextField,
  Typography,
  Box,
  Stepper,
  Step,
  StepLabel,
  Alert,
  CircularProgress,
  Card,
  CardContent,
  Divider,
  Chip
} from '@mui/material';
import {
  Security as SecurityIcon,
  Verified as VerifiedIcon,
  Send as SendIcon,
  Check as CheckIcon
} from '@mui/icons-material';
import {
  sendAadhaarOTP,
  verifyAadhaarOTP,
  validateAadhaarNumber,
  formatAadhaarNumber,
  maskAadhaarNumber,
  AadhaarVerificationResponse
} from '../services/aadhaarService';
import { useNotification } from '../hooks/useNotification';

interface AadhaarVerificationProps {
  open: boolean;
  onClose: () => void;
  onVerificationComplete: (verificationData: AadhaarVerificationResponse) => void;
  guestName?: string;
  guestPhone?: string;
}

const AadhaarVerification: React.FC<AadhaarVerificationProps> = ({
  open,
  onClose,
  onVerificationComplete,
  guestName,
  guestPhone
}) => {
  const { showSuccess, showError, showInfo } = useNotification();
  
  // State management
  const [activeStep, setActiveStep] = useState(0);
  const [aadhaarNumber, setAadhaarNumber] = useState('');
  const [otp, setOtp] = useState('');
  const [transactionId, setTransactionId] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [verificationData, setVerificationData] = useState<AadhaarVerificationResponse | null>(null);

  const steps = ['Enter Aadhaar', 'Verify OTP', 'Verification Complete'];

  // Reset form when dialog opens/closes
  React.useEffect(() => {
    if (open) {
      setActiveStep(0);
      setAadhaarNumber('');
      setOtp('');
      setTransactionId('');
      setError('');
      setVerificationData(null);
    }
  }, [open]);

  // Handle Aadhaar number input
  const handleAadhaarChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value.replace(/[^\d]/g, ''); // Only digits
    if (value.length <= 12) {
      setAadhaarNumber(value);
      setError('');
    }
  };

  // Send OTP
  const handleSendOTP = async () => {
    if (!validateAadhaarNumber(aadhaarNumber)) {
      setError('Please enter a valid 12-digit Aadhaar number');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const response = await sendAadhaarOTP({ aadhaarNumber });
      
      if (response.success && response.transactionId) {
        setTransactionId(response.transactionId);
        setActiveStep(1);
        showSuccess('OTP sent to your registered mobile number');
        showInfo('Enter OTP: 123456 (Demo mode)');
      } else {
        setError(response.message || 'Failed to send OTP');
        showError(response.message || 'Failed to send OTP');
      }
    } catch (err: any) {
      const errorMessage = err.message || 'Failed to send OTP';
      setError(errorMessage);
      showError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // Verify OTP
  const handleVerifyOTP = async () => {
    if (!otp || otp.length !== 6) {
      setError('Please enter a valid 6-digit OTP');
      return;
    }

    setLoading(true);
    setError('');

    try {
      const response = await verifyAadhaarOTP({
        aadhaarNumber,
        otp,
        transactionId
      });

      if (response.success && response.verified) {
        setVerificationData(response);
        setActiveStep(2);
        showSuccess('Aadhaar verified successfully!');
      } else {
        setError(response.message || 'OTP verification failed');
        showError(response.message || 'OTP verification failed');
      }
    } catch (err: any) {
      const errorMessage = err.message || 'OTP verification failed';
      setError(errorMessage);
      showError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  // Complete verification
  const handleComplete = () => {
    if (verificationData) {
      onVerificationComplete(verificationData);
      onClose();
    }
  };

  // Render step content
  const renderStepContent = () => {
    switch (activeStep) {
      case 0:
        return (
          <Box>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              Enter your 12-digit Aadhaar number to verify your identity
            </Typography>
            <TextField
              fullWidth
              label="Aadhaar Number"
              value={formatAadhaarNumber(aadhaarNumber)}
              onChange={handleAadhaarChange}
              placeholder="XXXX XXXX XXXX"
              margin="normal"
              error={!!error}
              helperText={error || 'Enter your 12-digit Aadhaar number'}
              InputProps={{
                startAdornment: <SecurityIcon color="action" sx={{ mr: 1 }} />
              }}
            />
            <Alert severity="info" sx={{ mt: 2 }}>
              <Typography variant="body2">
                <strong>Demo Mode:</strong> Use any 12-digit number for testing
              </Typography>
            </Alert>
          </Box>
        );

      case 1:
        return (
          <Box>
            <Typography variant="body2" color="text.secondary" gutterBottom>
              Enter the OTP sent to your registered mobile number
            </Typography>
            <Box sx={{ mb: 2 }}>
              <Typography variant="body2">
                Aadhaar: {maskAadhaarNumber(aadhaarNumber)}
              </Typography>
            </Box>
            <TextField
              fullWidth
              label="Enter OTP"
              value={otp}
              onChange={(e) => {
                const value = e.target.value.replace(/[^\d]/g, '');
                if (value.length <= 6) {
                  setOtp(value);
                  setError('');
                }
              }}
              placeholder="123456"
              margin="normal"
              error={!!error}
              helperText={error || 'Enter the 6-digit OTP'}
              InputProps={{
                startAdornment: <SendIcon color="action" sx={{ mr: 1 }} />
              }}
            />
            <Alert severity="info" sx={{ mt: 2 }}>
              <Typography variant="body2">
                <strong>Demo OTP:</strong> Use 123456 for successful verification
              </Typography>
            </Alert>
          </Box>
        );

      case 2:
        return (
          <Box>
            <Box textAlign="center" sx={{ mb: 3 }}>
              <VerifiedIcon color="success" sx={{ fontSize: 64, mb: 2 }} />
              <Typography variant="h6" color="success.main">
                Aadhaar Verified Successfully!
              </Typography>
            </Box>
            
            {verificationData?.data && (
              <Card variant="outlined">
                <CardContent>
                  <Typography variant="subtitle1" gutterBottom>
                    Verified Details:
                  </Typography>
                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Typography variant="body2" color="text.secondary">Name:</Typography>
                      <Typography variant="body2">{verificationData.data.name}</Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Typography variant="body2" color="text.secondary">Aadhaar:</Typography>
                      <Typography variant="body2">{maskAadhaarNumber(verificationData.data.aadhaarNumber)}</Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Typography variant="body2" color="text.secondary">Gender:</Typography>
                      <Typography variant="body2">{verificationData.data.gender}</Typography>
                    </Box>
                    <Divider sx={{ my: 1 }} />
                    <Typography variant="body2" color="text.secondary">Address:</Typography>
                    <Typography variant="body2">
                      {verificationData.data.address.street}, {verificationData.data.address.city}, {verificationData.data.address.state} - {verificationData.data.address.pincode}
                    </Typography>
                  </Box>
                </CardContent>
              </Card>
            )}
            
            <Alert severity="success" sx={{ mt: 2 }}>
              Your identity has been verified. You can now complete your booking.
            </Alert>
          </Box>
        );

      default:
        return null;
    }
  };

  return (
    <Dialog 
      open={open} 
      onClose={onClose} 
      maxWidth="sm" 
      fullWidth
      PaperProps={{
        sx: { minHeight: 400 }
      }}
    >
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <SecurityIcon color="primary" />
          <Typography variant="h6">Aadhaar Verification</Typography>
          <Chip label="Required" color="warning" size="small" />
        </Box>
      </DialogTitle>

      <DialogContent>
        <Box sx={{ mb: 3 }}>
          <Stepper activeStep={activeStep} alternativeLabel>
            {steps.map((label) => (
              <Step key={label}>
                <StepLabel>{label}</StepLabel>
              </Step>
            ))}
          </Stepper>
        </Box>

        {renderStepContent()}
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose} disabled={loading}>
          Cancel
        </Button>
        
        {activeStep === 0 && (
          <Button
            variant="contained"
            onClick={handleSendOTP}
            disabled={loading || !aadhaarNumber || aadhaarNumber.length !== 12}
            startIcon={loading ? <CircularProgress size={20} /> : <SendIcon />}
          >
            {loading ? 'Sending...' : 'Send OTP'}
          </Button>
        )}
        
        {activeStep === 1 && (
          <Button
            variant="contained"
            onClick={handleVerifyOTP}
            disabled={loading || !otp || otp.length !== 6}
            startIcon={loading ? <CircularProgress size={20} /> : <CheckIcon />}
          >
            {loading ? 'Verifying...' : 'Verify OTP'}
          </Button>
        )}
        
        {activeStep === 2 && (
          <Button
            variant="contained"
            color="success"
            onClick={handleComplete}
            startIcon={<VerifiedIcon />}
          >
            Complete Verification
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default AadhaarVerification;
