import React, { useState, useEffect } from 'react';
import {
  Box,
  <PERSON>pography,
  Card,
  CardContent,
  Grid,
  TextField,
  Button,
  Switch,
  FormControlLabel,
  Divider,
  Alert,
  CircularProgress,
  Tabs,
  Tab,
  FormControl,
  InputLabel,
  Select,
  MenuItem
} from '@mui/material';
import {
  Save as SaveIcon,
  Notifications as NotificationIcon,
  Security as SecurityIcon,
  Business as BusinessIcon,
  Payment as PaymentIcon
} from '@mui/icons-material';
import { useAuth } from '../hooks/useAuth';
import { updateUserProfile } from '../services/authService';
import { getHotelsByVendor, updateHotel } from '../services/hotelService';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`settings-tabpanel-${index}`}
      aria-labelledby={`settings-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

const SettingsPage: React.FC = () => {
  const { user } = useAuth();
  const [tabValue, setTabValue] = useState(0);
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState('');
  const [error, setError] = useState('');
  const [hotels, setHotels] = useState<any[]>([]);

  // Profile settings
  const [profileData, setProfileData] = useState({
    name: user?.name || '',
    email: user?.email || '',
    phone: user?.phone || '',
    address: '',
    timezone: 'UTC',
    language: 'en'
  });

  // Business settings
  const [businessData, setBusinessData] = useState({
    companyName: '',
    businessType: 'hotel',
    taxId: '',
    address: '',
    website: '',
    description: ''
  });

  // Notification settings
  const [notificationSettings, setNotificationSettings] = useState({
    emailNotifications: true,
    smsNotifications: false,
    bookingAlerts: true,
    paymentAlerts: true,
    staffAlerts: true,
    maintenanceAlerts: true
  });

  // Security settings
  const [securitySettings, setSecuritySettings] = useState({
    twoFactorAuth: false,
    sessionTimeout: 30,
    passwordExpiry: 90,
    loginAlerts: true
  });

  useEffect(() => {
    if (user) {
      setProfileData({
        name: user.name || '',
        email: user.email || '',
        phone: user.phone || '',
        address: user.address || '',
        timezone: user.timezone || 'UTC',
        language: user.language || 'en'
      });
      
      fetchHotels();
    }
  }, [user]);

  const fetchHotels = async () => {
    if (!user || !user.vendorId) return;

    try {
      const hotelData = await getHotelsByVendor(user.vendorId);
      setHotels(hotelData);
      
      if (hotelData.length > 0) {
        const hotel = hotelData[0];
        setBusinessData({
          companyName: hotel.name || '',
          businessType: 'hotel',
          taxId: hotel.taxId || '',
          address: hotel.address || '',
          website: hotel.website || '',
          description: hotel.description || ''
        });
      }
    } catch (err: any) {
      console.error('Error fetching hotels:', err);
    }
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleProfileSave = async () => {
    if (!user) return;

    try {
      setLoading(true);
      setError('');
      setSuccess('');

      await updateUserProfile(user.id, profileData);
      setSuccess('Profile updated successfully!');
    } catch (err: any) {
      setError(err.message || 'Failed to update profile');
    } finally {
      setLoading(false);
    }
  };

  const handleBusinessSave = async () => {
    if (!user || hotels.length === 0) return;

    try {
      setLoading(true);
      setError('');
      setSuccess('');

      // Update the first hotel with business information
      await updateHotel(hotels[0].id, {
        name: businessData.companyName,
        description: businessData.description,
        website: businessData.website,
        address: businessData.address,
        taxId: businessData.taxId
      });

      setSuccess('Business settings updated successfully!');
    } catch (err: any) {
      setError(err.message || 'Failed to update business settings');
    } finally {
      setLoading(false);
    }
  };

  const handleNotificationSave = async () => {
    try {
      setLoading(true);
      setError('');
      setSuccess('');

      // In a real app, you would save these to user preferences
      // For now, just simulate success
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setSuccess('Notification settings updated successfully!');
    } catch (err: any) {
      setError(err.message || 'Failed to update notification settings');
    } finally {
      setLoading(false);
    }
  };

  const handleSecuritySave = async () => {
    try {
      setLoading(true);
      setError('');
      setSuccess('');

      // In a real app, you would save these to user security settings
      // For now, just simulate success
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      setSuccess('Security settings updated successfully!');
    } catch (err: any) {
      setError(err.message || 'Failed to update security settings');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box>
      <Typography variant="h4" component="h1" gutterBottom>
        Settings
      </Typography>

      {success && (
        <Alert severity="success" sx={{ mb: 3 }}>
          {success}
        </Alert>
      )}

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Card>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={handleTabChange}>
            <Tab label="Profile" icon={<BusinessIcon />} />
            <Tab label="Business" icon={<BusinessIcon />} />
            <Tab label="Notifications" icon={<NotificationIcon />} />
            <Tab label="Security" icon={<SecurityIcon />} />
          </Tabs>
        </Box>

        {/* Profile Settings */}
        <TabPanel value={tabValue} index={0}>
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Full Name"
                value={profileData.name}
                onChange={(e) => setProfileData(prev => ({ ...prev, name: e.target.value }))}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Email"
                type="email"
                value={profileData.email}
                onChange={(e) => setProfileData(prev => ({ ...prev, email: e.target.value }))}
                disabled
                helperText="Email cannot be changed"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Phone"
                value={profileData.phone}
                onChange={(e) => setProfileData(prev => ({ ...prev, phone: e.target.value }))}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Timezone</InputLabel>
                <Select
                  value={profileData.timezone}
                  label="Timezone"
                  onChange={(e) => setProfileData(prev => ({ ...prev, timezone: e.target.value }))}
                >
                  <MenuItem value="UTC">UTC</MenuItem>
                  <MenuItem value="America/New_York">Eastern Time</MenuItem>
                  <MenuItem value="America/Chicago">Central Time</MenuItem>
                  <MenuItem value="America/Denver">Mountain Time</MenuItem>
                  <MenuItem value="America/Los_Angeles">Pacific Time</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Address"
                multiline
                rows={2}
                value={profileData.address}
                onChange={(e) => setProfileData(prev => ({ ...prev, address: e.target.value }))}
              />
            </Grid>
            <Grid item xs={12}>
              <Button
                variant="contained"
                startIcon={<SaveIcon />}
                onClick={handleProfileSave}
                disabled={loading}
              >
                {loading ? <CircularProgress size={20} /> : 'Save Profile'}
              </Button>
            </Grid>
          </Grid>
        </TabPanel>

        {/* Business Settings */}
        <TabPanel value={tabValue} index={1}>
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Company Name"
                value={businessData.companyName}
                onChange={(e) => setBusinessData(prev => ({ ...prev, companyName: e.target.value }))}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Tax ID"
                value={businessData.taxId}
                onChange={(e) => setBusinessData(prev => ({ ...prev, taxId: e.target.value }))}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Website"
                value={businessData.website}
                onChange={(e) => setBusinessData(prev => ({ ...prev, website: e.target.value }))}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Business Address"
                multiline
                rows={2}
                value={businessData.address}
                onChange={(e) => setBusinessData(prev => ({ ...prev, address: e.target.value }))}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Business Description"
                multiline
                rows={3}
                value={businessData.description}
                onChange={(e) => setBusinessData(prev => ({ ...prev, description: e.target.value }))}
              />
            </Grid>
            <Grid item xs={12}>
              <Button
                variant="contained"
                startIcon={<SaveIcon />}
                onClick={handleBusinessSave}
                disabled={loading}
              >
                {loading ? <CircularProgress size={20} /> : 'Save Business Settings'}
              </Button>
            </Grid>
          </Grid>
        </TabPanel>

        {/* Notification Settings */}
        <TabPanel value={tabValue} index={2}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Communication Preferences
              </Typography>
              <FormControlLabel
                control={
                  <Switch
                    checked={notificationSettings.emailNotifications}
                    onChange={(e) => setNotificationSettings(prev => ({ 
                      ...prev, 
                      emailNotifications: e.target.checked 
                    }))}
                  />
                }
                label="Email Notifications"
              />
              <FormControlLabel
                control={
                  <Switch
                    checked={notificationSettings.smsNotifications}
                    onChange={(e) => setNotificationSettings(prev => ({ 
                      ...prev, 
                      smsNotifications: e.target.checked 
                    }))}
                  />
                }
                label="SMS Notifications"
              />
            </Grid>
            <Grid item xs={12}>
              <Divider sx={{ my: 2 }} />
              <Typography variant="h6" gutterBottom>
                Alert Types
              </Typography>
              <FormControlLabel
                control={
                  <Switch
                    checked={notificationSettings.bookingAlerts}
                    onChange={(e) => setNotificationSettings(prev => ({ 
                      ...prev, 
                      bookingAlerts: e.target.checked 
                    }))}
                  />
                }
                label="Booking Alerts"
              />
              <FormControlLabel
                control={
                  <Switch
                    checked={notificationSettings.paymentAlerts}
                    onChange={(e) => setNotificationSettings(prev => ({ 
                      ...prev, 
                      paymentAlerts: e.target.checked 
                    }))}
                  />
                }
                label="Payment Alerts"
              />
              <FormControlLabel
                control={
                  <Switch
                    checked={notificationSettings.staffAlerts}
                    onChange={(e) => setNotificationSettings(prev => ({ 
                      ...prev, 
                      staffAlerts: e.target.checked 
                    }))}
                  />
                }
                label="Staff Alerts"
              />
              <FormControlLabel
                control={
                  <Switch
                    checked={notificationSettings.maintenanceAlerts}
                    onChange={(e) => setNotificationSettings(prev => ({ 
                      ...prev, 
                      maintenanceAlerts: e.target.checked 
                    }))}
                  />
                }
                label="Maintenance Alerts"
              />
            </Grid>
            <Grid item xs={12}>
              <Button
                variant="contained"
                startIcon={<SaveIcon />}
                onClick={handleNotificationSave}
                disabled={loading}
              >
                {loading ? <CircularProgress size={20} /> : 'Save Notification Settings'}
              </Button>
            </Grid>
          </Grid>
        </TabPanel>

        {/* Security Settings */}
        <TabPanel value={tabValue} index={3}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Typography variant="h6" gutterBottom>
                Authentication
              </Typography>
              <FormControlLabel
                control={
                  <Switch
                    checked={securitySettings.twoFactorAuth}
                    onChange={(e) => setSecuritySettings(prev => ({ 
                      ...prev, 
                      twoFactorAuth: e.target.checked 
                    }))}
                  />
                }
                label="Two-Factor Authentication"
              />
              <FormControlLabel
                control={
                  <Switch
                    checked={securitySettings.loginAlerts}
                    onChange={(e) => setSecuritySettings(prev => ({ 
                      ...prev, 
                      loginAlerts: e.target.checked 
                    }))}
                  />
                }
                label="Login Alerts"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Session Timeout (minutes)"
                type="number"
                value={securitySettings.sessionTimeout}
                onChange={(e) => setSecuritySettings(prev => ({ 
                  ...prev, 
                  sessionTimeout: Number(e.target.value) 
                }))}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Password Expiry (days)"
                type="number"
                value={securitySettings.passwordExpiry}
                onChange={(e) => setSecuritySettings(prev => ({ 
                  ...prev, 
                  passwordExpiry: Number(e.target.value) 
                }))}
              />
            </Grid>
            <Grid item xs={12}>
              <Button
                variant="contained"
                startIcon={<SaveIcon />}
                onClick={handleSecuritySave}
                disabled={loading}
              >
                {loading ? <CircularProgress size={20} /> : 'Save Security Settings'}
              </Button>
            </Grid>
          </Grid>
        </TabPanel>
      </Card>
    </Box>
  );
};

export default SettingsPage;
