import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Grid,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Alert,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  ListItemSecondaryAction,
  Divider,
  Badge,
  Tooltip,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Tabs,
  Tab
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Person as PersonIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  Work as WorkIcon,
  Schedule as ScheduleIcon,
  CheckCircle as ActiveIcon,
  Cancel as InactiveIcon,
  Visibility as ViewIcon,
  Assignment as TaskIcon,
  AccessTime as TimeIcon,
  Star as RatingIcon
} from '@mui/icons-material';
import { useAuth } from '../hooks/useAuth';
import { StaffMember } from '../types';
import { getStaffForHotel, createStaffMember, updateStaffMember, deleteStaffMember } from '../services/staffService';
import { getHotelsByVendor } from '../services/hotelService';
import { format } from 'date-fns';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`staff-tabpanel-${index}`}
      aria-labelledby={`staff-tab-${index}`}
      {...other}
    >
      {value === index && <Box>{children}</Box>}
    </div>
  );
}

const VendorStaffPage: React.FC = () => {
  const { user } = useAuth();
  const [hotels, setHotels] = useState<any[]>([]);
  const [selectedHotel, setSelectedHotel] = useState<string>('');
  const [staff, setStaff] = useState<StaffMember[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingStaff, setEditingStaff] = useState<StaffMember | null>(null);
  const [tabValue, setTabValue] = useState(0);

  // Mock staff data for demonstration
  const [mockStaff] = useState<any[]>([
    {
      id: '1',
      name: 'Sarah Wilson',
      email: '<EMAIL>',
      phone: '+****************',
      role: 'front_desk',
      department: 'Front Office',
      isActive: true,
      hireDate: new Date('2023-01-15'),
      salary: 45000,
      avatar: '',
      shift: 'morning',
      performance: 4.8,
      tasksCompleted: 156,
      hoursWorked: 160,
      lastLogin: new Date(Date.now() - 2 * 60 * 60 * 1000),
      skills: ['Customer Service', 'PMS Systems', 'Multilingual'],
      emergencyContact: {
        name: 'John Wilson',
        phone: '+****************',
        relationship: 'Spouse'
      }
    },
    {
      id: '2',
      name: 'Michael Rodriguez',
      email: '<EMAIL>',
      phone: '+****************',
      role: 'housekeeping',
      department: 'Housekeeping',
      isActive: true,
      hireDate: new Date('2022-08-20'),
      salary: 35000,
      avatar: '',
      shift: 'morning',
      performance: 4.6,
      tasksCompleted: 89,
      hoursWorked: 152,
      lastLogin: new Date(Date.now() - 4 * 60 * 60 * 1000),
      skills: ['Room Cleaning', 'Laundry', 'Inventory Management'],
      emergencyContact: {
        name: 'Maria Rodriguez',
        phone: '+****************',
        relationship: 'Mother'
      }
    },
    {
      id: '3',
      name: 'Emily Chen',
      email: '<EMAIL>',
      phone: '+****************',
      role: 'manager',
      department: 'Management',
      isActive: true,
      hireDate: new Date('2021-03-10'),
      salary: 65000,
      avatar: '',
      shift: 'full_time',
      performance: 4.9,
      tasksCompleted: 234,
      hoursWorked: 168,
      lastLogin: new Date(Date.now() - 30 * 60 * 1000),
      skills: ['Leadership', 'Operations', 'Staff Training', 'Budget Management'],
      emergencyContact: {
        name: 'David Chen',
        phone: '+****************',
        relationship: 'Brother'
      }
    },
    {
      id: '4',
      name: 'James Thompson',
      email: '<EMAIL>',
      phone: '+****************',
      role: 'maintenance',
      department: 'Maintenance',
      isActive: true,
      hireDate: new Date('2022-11-05'),
      salary: 42000,
      avatar: '',
      shift: 'evening',
      performance: 4.7,
      tasksCompleted: 67,
      hoursWorked: 144,
      lastLogin: new Date(Date.now() - 6 * 60 * 60 * 1000),
      skills: ['Electrical', 'Plumbing', 'HVAC', 'General Repairs'],
      emergencyContact: {
        name: 'Lisa Thompson',
        phone: '+****************',
        relationship: 'Wife'
      }
    },
    {
      id: '5',
      name: 'Anna Kowalski',
      email: '<EMAIL>',
      phone: '+****************',
      role: 'front_desk',
      department: 'Front Office',
      isActive: false,
      hireDate: new Date('2023-06-01'),
      salary: 43000,
      avatar: '',
      shift: 'evening',
      performance: 4.4,
      tasksCompleted: 45,
      hoursWorked: 80,
      lastLogin: new Date(Date.now() - 48 * 60 * 60 * 1000),
      skills: ['Customer Service', 'Reservations'],
      emergencyContact: {
        name: 'Peter Kowalski',
        phone: '+****************',
        relationship: 'Father'
      }
    }
  ]);

  useEffect(() => {
    const fetchHotels = async () => {
      if (!user?.vendorId) return;
      
      try {
        setLoading(true);
        const vendorHotels = await getHotelsByVendor(user.vendorId);
        setHotels(vendorHotels);
        if (vendorHotels.length > 0) {
          setSelectedHotel(vendorHotels[0].id);
        }
      } catch (err: any) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchHotels();
  }, [user]);

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'manager':
        return '#1976d2';
      case 'front_desk':
        return '#388e3c';
      case 'housekeeping':
        return '#f57c00';
      case 'maintenance':
        return '#d32f2f';
      case 'security':
        return '#7b1fa2';
      default:
        return '#757575';
    }
  };

  const getShiftColor = (shift: string) => {
    switch (shift) {
      case 'morning':
        return '#4caf50';
      case 'evening':
        return '#ff9800';
      case 'night':
        return '#3f51b5';
      case 'full_time':
        return '#9c27b0';
      default:
        return '#757575';
    }
  };

  const activeStaff = mockStaff.filter(s => s.isActive);
  const inactiveStaff = mockStaff.filter(s => !s.isActive);

  const departmentStats = {
    'Front Office': mockStaff.filter(s => s.department === 'Front Office' && s.isActive).length,
    'Housekeeping': mockStaff.filter(s => s.department === 'Housekeeping' && s.isActive).length,
    'Maintenance': mockStaff.filter(s => s.department === 'Maintenance' && s.isActive).length,
    'Management': mockStaff.filter(s => s.department === 'Management' && s.isActive).length
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 3 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1">
          Staff Management
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => setDialogOpen(true)}
        >
          Add New Staff
        </Button>
      </Box>

      {/* Staff Statistics */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Avatar sx={{ bgcolor: '#4caf50', mx: 'auto', mb: 1 }}>
                <ActiveIcon />
              </Avatar>
              <Typography variant="h4" color="success.main">
                {activeStaff.length}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                Active Staff
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Avatar sx={{ bgcolor: '#f44336', mx: 'auto', mb: 1 }}>
                <InactiveIcon />
              </Avatar>
              <Typography variant="h4" color="error.main">
                {inactiveStaff.length}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                Inactive Staff
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Avatar sx={{ bgcolor: '#ff9800', mx: 'auto', mb: 1 }}>
                <TaskIcon />
              </Avatar>
              <Typography variant="h4" color="warning.main">
                {mockStaff.reduce((sum, s) => sum + s.tasksCompleted, 0)}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                Tasks Completed
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Avatar sx={{ bgcolor: '#2196f3', mx: 'auto', mb: 1 }}>
                <TimeIcon />
              </Avatar>
              <Typography variant="h4" color="primary.main">
                {Math.round(mockStaff.reduce((sum, s) => sum + s.hoursWorked, 0) / mockStaff.length)}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                Avg Hours/Month
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Department Overview */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Department Overview
          </Typography>
          <Grid container spacing={2}>
            {Object.entries(departmentStats).map(([dept, count]) => (
              <Grid item xs={6} sm={3} key={dept}>
                <Box textAlign="center" p={2}>
                  <Typography variant="h5" color="primary">
                    {count}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    {dept}
                  </Typography>
                </Box>
              </Grid>
            ))}
          </Grid>
        </CardContent>
      </Card>

      {/* Staff Tabs */}
      <Card>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={(_, newValue) => setTabValue(newValue)}>
            <Tab label={`Active Staff (${activeStaff.length})`} />
            <Tab label={`Inactive Staff (${inactiveStaff.length})`} />
            <Tab label="Performance" />
          </Tabs>
        </Box>

        <TabPanel value={tabValue} index={0}>
          <Box p={3}>
            <Grid container spacing={3}>
              {activeStaff.map((member) => (
                <Grid item xs={12} md={6} lg={4} key={member.id}>
                  <Card
                    sx={{
                      '&:hover': {
                        boxShadow: 3,
                        transform: 'translateY(-2px)',
                        transition: 'all 0.2s'
                      }
                    }}
                  >
                    <CardContent>
                      <Box display="flex" alignItems="center" mb={2}>
                        <Avatar
                          sx={{
                            bgcolor: getRoleColor(member.role),
                            mr: 2,
                            width: 56,
                            height: 56
                          }}
                        >
                          {member.name.split(' ').map(n => n[0]).join('')}
                        </Avatar>
                        <Box flex={1}>
                          <Typography variant="h6">
                            {member.name}
                          </Typography>
                          <Typography variant="body2" color="textSecondary">
                            {member.department}
                          </Typography>
                          <Box display="flex" alignItems="center" gap={1} mt={0.5}>
                            <Chip
                              label={member.role.replace('_', ' ').toUpperCase()}
                              size="small"
                              sx={{
                                backgroundColor: getRoleColor(member.role),
                                color: 'white',
                                fontSize: '0.7rem'
                              }}
                            />
                            <Chip
                              label={member.shift.replace('_', ' ').toUpperCase()}
                              size="small"
                              variant="outlined"
                              sx={{
                                borderColor: getShiftColor(member.shift),
                                color: getShiftColor(member.shift),
                                fontSize: '0.7rem'
                              }}
                            />
                          </Box>
                        </Box>
                      </Box>

                      <List dense>
                        <ListItem>
                          <ListItemIcon>
                            <EmailIcon sx={{ fontSize: 16 }} />
                          </ListItemIcon>
                          <ListItemText
                            primary={member.email}
                            primaryTypographyProps={{ variant: 'body2' }}
                          />
                        </ListItem>
                        <ListItem>
                          <ListItemIcon>
                            <PhoneIcon sx={{ fontSize: 16 }} />
                          </ListItemIcon>
                          <ListItemText
                            primary={member.phone}
                            primaryTypographyProps={{ variant: 'body2' }}
                          />
                        </ListItem>
                        <ListItem>
                          <ListItemIcon>
                            <RatingIcon sx={{ fontSize: 16 }} />
                          </ListItemIcon>
                          <ListItemText
                            primary={`Performance: ${member.performance}/5.0`}
                            primaryTypographyProps={{ variant: 'body2' }}
                          />
                        </ListItem>
                        <ListItem>
                          <ListItemIcon>
                            <TaskIcon sx={{ fontSize: 16 }} />
                          </ListItemIcon>
                          <ListItemText
                            primary={`${member.tasksCompleted} tasks completed`}
                            primaryTypographyProps={{ variant: 'body2' }}
                          />
                        </ListItem>
                      </List>

                      <Box display="flex" justifyContent="space-between" alignItems="center" mt={2}>
                        <Typography variant="caption" color="textSecondary">
                          Last login: {format(member.lastLogin, 'MMM dd, HH:mm')}
                        </Typography>
                        <Box>
                          <Tooltip title="View Details">
                            <IconButton size="small" color="primary">
                              <ViewIcon />
                            </IconButton>
                          </Tooltip>
                          <Tooltip title="Edit Staff">
                            <IconButton size="small" color="secondary">
                              <EditIcon />
                            </IconButton>
                          </Tooltip>
                        </Box>
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
              ))}
            </Grid>
          </Box>
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          <Box p={3}>
            {inactiveStaff.length === 0 ? (
              <Box textAlign="center" py={4}>
                <PersonIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                <Typography variant="h6" gutterBottom>
                  No inactive staff members
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  All staff members are currently active.
                </Typography>
              </Box>
            ) : (
              <Grid container spacing={3}>
                {inactiveStaff.map((member) => (
                  <Grid item xs={12} md={6} lg={4} key={member.id}>
                    <Card sx={{ opacity: 0.7 }}>
                      <CardContent>
                        <Box display="flex" alignItems="center" mb={2}>
                          <Avatar
                            sx={{
                              bgcolor: '#9e9e9e',
                              mr: 2,
                              width: 56,
                              height: 56
                            }}
                          >
                            {member.name.split(' ').map(n => n[0]).join('')}
                          </Avatar>
                          <Box flex={1}>
                            <Typography variant="h6">
                              {member.name}
                            </Typography>
                            <Typography variant="body2" color="textSecondary">
                              {member.department}
                            </Typography>
                            <Chip
                              label="INACTIVE"
                              size="small"
                              color="error"
                              sx={{ mt: 0.5 }}
                            />
                          </Box>
                        </Box>
                        <Typography variant="body2" color="textSecondary">
                          Last active: {format(member.lastLogin, 'MMM dd, yyyy')}
                        </Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            )}
          </Box>
        </TabPanel>

        <TabPanel value={tabValue} index={2}>
          <Box p={3}>
            <TableContainer component={Paper}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Staff Member</TableCell>
                    <TableCell>Department</TableCell>
                    <TableCell align="center">Performance</TableCell>
                    <TableCell align="center">Tasks Completed</TableCell>
                    <TableCell align="center">Hours Worked</TableCell>
                    <TableCell align="center">Efficiency</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {activeStaff
                    .sort((a, b) => b.performance - a.performance)
                    .map((member) => (
                      <TableRow key={member.id}>
                        <TableCell>
                          <Box display="flex" alignItems="center">
                            <Avatar
                              sx={{
                                bgcolor: getRoleColor(member.role),
                                mr: 2,
                                width: 32,
                                height: 32
                              }}
                            >
                              {member.name.split(' ').map(n => n[0]).join('')}
                            </Avatar>
                            <Box>
                              <Typography variant="body2" fontWeight="bold">
                                {member.name}
                              </Typography>
                              <Typography variant="caption" color="textSecondary">
                                {member.role.replace('_', ' ')}
                              </Typography>
                            </Box>
                          </Box>
                        </TableCell>
                        <TableCell>{member.department}</TableCell>
                        <TableCell align="center">
                          <Box display="flex" alignItems="center" justifyContent="center">
                            <RatingIcon sx={{ fontSize: 16, color: '#ffc107', mr: 0.5 }} />
                            <Typography variant="body2">
                              {member.performance}/5.0
                            </Typography>
                          </Box>
                        </TableCell>
                        <TableCell align="center">
                          <Typography variant="body2">
                            {member.tasksCompleted}
                          </Typography>
                        </TableCell>
                        <TableCell align="center">
                          <Typography variant="body2">
                            {member.hoursWorked}h
                          </Typography>
                        </TableCell>
                        <TableCell align="center">
                          <Typography variant="body2">
                            {(member.tasksCompleted / member.hoursWorked * 10).toFixed(1)}
                          </Typography>
                        </TableCell>
                      </TableRow>
                    ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Box>
        </TabPanel>
      </Card>
    </Box>
  );
};

export default VendorStaffPage;
