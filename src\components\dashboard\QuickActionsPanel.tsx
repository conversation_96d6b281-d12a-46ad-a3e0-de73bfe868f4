import React from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Grid,
  IconButton,
  Tooltip,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip
} from '@mui/material';
import {
  Add as AddIcon,
  PersonAdd as PersonAddIcon,
  BookOnline as BookingIcon,
  CheckCircle as CheckInIcon,
  ExitToApp as CheckOutIcon,
  CleaningServices as CleaningIcon,
  Build as MaintenanceIcon,
  Assessment as ReportIcon,
  Schedule as ScheduleIcon,
  Notifications as NotificationIcon,
  Room as RoomIcon,
  People as PeopleIcon,
  Hotel as HotelIcon,
  Settings as SettingsIcon,
  Print as PrintIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  Emergency as EmergencyIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';

interface QuickAction {
  id: string;
  title: string;
  description: string;
  icon: React.ReactElement;
  color: string;
  path?: string;
  onClick?: () => void;
  badge?: number;
  urgent?: boolean;
}

interface QuickActionsPanelProps {
  userRole?: string;
  onActionClick?: (actionId: string) => void;
}

const QuickActionsPanel: React.FC<QuickActionsPanelProps> = ({
  userRole = 'vendor',
  onActionClick
}) => {
  const navigate = useNavigate();

  const handleActionClick = (action: QuickAction) => {
    if (action.path) {
      navigate(action.path);
    }
    if (action.onClick) {
      action.onClick();
    }
    if (onActionClick) {
      onActionClick(action.id);
    }
  };

  // Define actions based on user role
  const getActionsForRole = (): QuickAction[] => {
    const commonActions: QuickAction[] = [
      {
        id: 'new_booking',
        title: 'New Booking',
        description: 'Create a new reservation',
        icon: <BookingIcon />,
        color: '#1976d2',
        path: '/hotel-dashboard/bookings'
      },
      {
        id: 'check_in',
        title: 'Check-in Guest',
        description: 'Process guest check-in',
        icon: <CheckInIcon />,
        color: '#388e3c',
        onClick: () => console.log('Check-in guest')
      },
      {
        id: 'check_out',
        title: 'Check-out Guest',
        description: 'Process guest check-out',
        icon: <CheckOutIcon />,
        color: '#f57c00',
        onClick: () => console.log('Check-out guest')
      },
      {
        id: 'room_status',
        title: 'Room Status',
        description: 'View and update room status',
        icon: <RoomIcon />,
        color: '#7b1fa2',
        path: '/hotel-dashboard/rooms'
      }
    ];

    const vendorActions: QuickAction[] = [
      ...commonActions,
      {
        id: 'add_staff',
        title: 'Add Staff',
        description: 'Add new staff member',
        icon: <PersonAddIcon />,
        color: '#d32f2f',
        path: '/hotel-dashboard/staff'
      },
      {
        id: 'housekeeping',
        title: 'Housekeeping',
        description: 'Manage cleaning tasks',
        icon: <CleaningIcon />,
        color: '#00796b',
        onClick: () => console.log('Housekeeping tasks')
      },
      {
        id: 'maintenance',
        title: 'Maintenance',
        description: 'Report maintenance issues',
        icon: <MaintenanceIcon />,
        color: '#5d4037',
        onClick: () => console.log('Maintenance request')
      },
      {
        id: 'reports',
        title: 'Reports',
        description: 'View analytics and reports',
        icon: <ReportIcon />,
        color: '#455a64',
        path: '/hotel-dashboard/analytics'
      }
    ];

    const staffActions: QuickAction[] = [
      {
        id: 'check_in',
        title: 'Check-in Guest',
        description: 'Process guest check-in',
        icon: <CheckInIcon />,
        color: '#388e3c',
        onClick: () => console.log('Check-in guest')
      },
      {
        id: 'check_out',
        title: 'Check-out Guest',
        description: 'Process guest check-out',
        icon: <CheckOutIcon />,
        color: '#f57c00',
        onClick: () => console.log('Check-out guest')
      },
      {
        id: 'housekeeping',
        title: 'Housekeeping',
        description: 'Update room cleaning status',
        icon: <CleaningIcon />,
        color: '#00796b',
        onClick: () => console.log('Housekeeping update')
      },
      {
        id: 'schedule',
        title: 'My Schedule',
        description: 'View work schedule',
        icon: <ScheduleIcon />,
        color: '#7b1fa2',
        path: '/staff-dashboard/schedule'
      }
    ];

    switch (userRole) {
      case 'staff':
        return staffActions;
      case 'vendor':
      case 'admin':
        return vendorActions;
      default:
        return commonActions;
    }
  };

  const actions = getActionsForRole();

  // Emergency actions
  const emergencyActions: QuickAction[] = [
    {
      id: 'emergency',
      title: 'Emergency',
      description: 'Report emergency situation',
      icon: <EmergencyIcon />,
      color: '#d32f2f',
      onClick: () => console.log('Emergency reported'),
      urgent: true
    }
  ];

  // Communication actions
  const communicationActions: QuickAction[] = [
    {
      id: 'call_manager',
      title: 'Call Manager',
      description: 'Quick call to manager',
      icon: <PhoneIcon />,
      color: '#1976d2',
      onClick: () => console.log('Calling manager')
    },
    {
      id: 'send_email',
      title: 'Send Email',
      description: 'Send email notification',
      icon: <EmailIcon />,
      color: '#388e3c',
      onClick: () => console.log('Send email')
    }
  ];

  return (
    <Card>
      <CardContent>
        <Typography variant="h6" gutterBottom>
          Quick Actions
        </Typography>

        {/* Emergency Actions */}
        <Box mb={2}>
          <Typography variant="subtitle2" color="error" gutterBottom>
            Emergency
          </Typography>
          <Grid container spacing={1}>
            {emergencyActions.map((action) => (
              <Grid item xs={12} key={action.id}>
                <Button
                  fullWidth
                  variant="contained"
                  color="error"
                  startIcon={action.icon}
                  onClick={() => handleActionClick(action)}
                  sx={{
                    justifyContent: 'flex-start',
                    textTransform: 'none',
                    py: 1
                  }}
                >
                  {action.title}
                </Button>
              </Grid>
            ))}
          </Grid>
        </Box>

        <Divider sx={{ my: 2 }} />

        {/* Main Actions */}
        <Box mb={2}>
          <Typography variant="subtitle2" gutterBottom>
            Main Actions
          </Typography>
          <Grid container spacing={1}>
            {actions.map((action) => (
              <Grid item xs={12} sm={6} key={action.id}>
                <Button
                  fullWidth
                  variant="outlined"
                  startIcon={action.icon}
                  onClick={() => handleActionClick(action)}
                  sx={{
                    borderColor: action.color,
                    color: action.color,
                    '&:hover': {
                      borderColor: action.color,
                      backgroundColor: `${action.color}10`
                    },
                    justifyContent: 'flex-start',
                    textTransform: 'none',
                    py: 1.5,
                    position: 'relative'
                  }}
                >
                  <Box>
                    <Typography variant="body2" fontWeight="medium">
                      {action.title}
                    </Typography>
                    <Typography variant="caption" color="textSecondary" display="block">
                      {action.description}
                    </Typography>
                  </Box>
                  {action.badge && (
                    <Chip
                      label={action.badge}
                      size="small"
                      color="error"
                      sx={{
                        position: 'absolute',
                        top: 4,
                        right: 4,
                        height: 20,
                        fontSize: '0.7rem'
                      }}
                    />
                  )}
                </Button>
              </Grid>
            ))}
          </Grid>
        </Box>

        <Divider sx={{ my: 2 }} />

        {/* Communication Actions */}
        <Box>
          <Typography variant="subtitle2" gutterBottom>
            Communication
          </Typography>
          <Grid container spacing={1}>
            {communicationActions.map((action) => (
              <Grid item xs={6} key={action.id}>
                <Tooltip title={action.description}>
                  <IconButton
                    onClick={() => handleActionClick(action)}
                    sx={{
                      backgroundColor: `${action.color}10`,
                      color: action.color,
                      '&:hover': {
                        backgroundColor: `${action.color}20`
                      },
                      width: '100%',
                      borderRadius: 1,
                      py: 1
                    }}
                  >
                    {action.icon}
                  </IconButton>
                </Tooltip>
              </Grid>
            ))}
          </Grid>
        </Box>

        {/* Recent Actions */}
        <Box mt={3}>
          <Typography variant="subtitle2" gutterBottom>
            Recent Actions
          </Typography>
          <List dense>
            <ListItem>
              <ListItemIcon>
                <CheckInIcon sx={{ color: '#388e3c', fontSize: 20 }} />
              </ListItemIcon>
              <ListItemText
                primary="Guest Check-in"
                secondary="Room 205 - 2 min ago"
                primaryTypographyProps={{ variant: 'body2' }}
                secondaryTypographyProps={{ variant: 'caption' }}
              />
            </ListItem>
            <ListItem>
              <ListItemIcon>
                <CleaningIcon sx={{ color: '#00796b', fontSize: 20 }} />
              </ListItemIcon>
              <ListItemText
                primary="Room Cleaned"
                secondary="Room 101 - 15 min ago"
                primaryTypographyProps={{ variant: 'body2' }}
                secondaryTypographyProps={{ variant: 'caption' }}
              />
            </ListItem>
            <ListItem>
              <ListItemIcon>
                <BookingIcon sx={{ color: '#1976d2', fontSize: 20 }} />
              </ListItemIcon>
              <ListItemText
                primary="New Booking"
                secondary="Room 301 - 1 hour ago"
                primaryTypographyProps={{ variant: 'body2' }}
                secondaryTypographyProps={{ variant: 'caption' }}
              />
            </ListItem>
          </List>
        </Box>
      </CardContent>
    </Card>
  );
};

export default QuickActionsPanel;
