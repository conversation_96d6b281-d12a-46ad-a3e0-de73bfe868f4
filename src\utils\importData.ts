import { db } from '../firebase/config';
import { collection, doc, setDoc, Timestamp } from 'firebase/firestore';

// Sample data to import
const sampleData = {
  hotels: {
    'hotel-1': {
      name: 'Grand Plaza Hotel',
      description: 'Luxury hotel in the heart of the city with world-class amenities and exceptional service.',
      address: '123 Main Street',
      city: 'New York',
      state: 'NY',
      country: 'USA',
      zipCode: '10001',
      phone: '******-1000',
      email: '<EMAIL>',
      website: 'https://grandplaza.com',
      vendorId: 'vendor-1',
      vendorName: 'Hotel Vendor',
      rating: 4.5,
      totalRooms: 150,
      availableRooms: 120,
      amenities: [
        'Free WiFi', 'Swimming Pool', 'Fitness Center', 'Restaurant',
        'Spa & Wellness', 'Business Center', 'Room Service', 'Concierge',
        'Parking', 'Pet Friendly'
      ],
      images: [
        'https://images.unsplash.com/photo-1566073771259-6a8506099945',
        'https://images.unsplash.com/photo-1564501049412-61c2a3083791'
      ],
      checkInTime: '15:00',
      checkOutTime: '11:00',
      policies: {
        cancellation: 'Free cancellation up to 24 hours before check-in',
        pets: 'Pets allowed with additional fee',
        smoking: 'Non-smoking property'
      },
      isActive: true,
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    },
    'hotel-2': {
      name: 'Seaside Resort & Spa',
      description: 'Beautiful beachfront resort with stunning ocean views and premium spa services.',
      address: '456 Ocean Drive',
      city: 'Miami',
      state: 'FL',
      country: 'USA',
      zipCode: '33139',
      phone: '******-2000',
      email: '<EMAIL>',
      website: 'https://seasideresort.com',
      vendorId: 'vendor-1',
      vendorName: 'Hotel Vendor',
      rating: 4.8,
      totalRooms: 200,
      availableRooms: 180,
      amenities: [
        'Beachfront', 'Free WiFi', 'Multiple Pools', 'Spa Services',
        'Water Sports', 'Fine Dining', 'Kids Club', 'Tennis Court',
        'Valet Parking', '24/7 Room Service'
      ],
      images: [
        'https://images.unsplash.com/photo-1571896349842-33c89424de2d',
        'https://images.unsplash.com/photo-1520250497591-112f2f40a3f4'
      ],
      checkInTime: '16:00',
      checkOutTime: '12:00',
      policies: {
        cancellation: 'Free cancellation up to 48 hours before check-in',
        pets: 'No pets allowed',
        smoking: 'Designated smoking areas only'
      },
      isActive: true,
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    }
  },
  rooms: {
    'room-1': {
      hotelId: 'hotel-1',
      hotelName: 'Grand Plaza Hotel',
      roomNumber: '101',
      type: 'single',
      status: 'available',
      price: 150,
      currency: 'USD',
      capacity: 1,
      bedType: 'Queen Bed',
      size: 25,
      sizeUnit: 'sqm',
      amenities: [
        'Free WiFi', 'Smart TV', 'Air Conditioning', 'Mini Bar',
        'Coffee Maker', 'Safe', 'Hair Dryer', 'Iron & Board'
      ],
      images: ['https://images.unsplash.com/photo-1631049307264-da0ec9d70304'],
      description: 'Comfortable single room with modern amenities and city view.',
      floor: 1,
      view: 'City View',
      isActive: true,
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    },
    'room-2': {
      hotelId: 'hotel-1',
      hotelName: 'Grand Plaza Hotel',
      roomNumber: '102',
      type: 'double',
      status: 'available',
      price: 200,
      currency: 'USD',
      capacity: 2,
      bedType: 'King Bed',
      size: 35,
      sizeUnit: 'sqm',
      amenities: [
        'Free WiFi', 'Smart TV', 'Air Conditioning', 'Mini Bar',
        'Coffee Maker', 'Safe', 'Hair Dryer', 'Iron & Board',
        'Balcony', 'Sofa'
      ],
      images: ['https://images.unsplash.com/photo-1618773928121-c32242e63f39'],
      description: 'Spacious double room with king bed and private balcony.',
      floor: 1,
      view: 'Garden View',
      isActive: true,
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    }
  },
  staff: {
    'staff-1': {
      hotelId: 'hotel-1',
      vendorId: 'vendor-1',
      employeeId: 'EMP001',
      name: 'John Manager',
      email: '<EMAIL>',
      phone: '******-1001',
      role: 'manager',
      department: 'Management',
      position: 'General Manager',
      salary: 75000,
      currency: 'USD',
      hireDate: Timestamp.fromDate(new Date('2024-01-15')),
      status: 'active',
      address: '789 Staff Street, New York, NY 10001',
      dateOfBirth: Timestamp.fromDate(new Date('1985-03-20')),
      emergencyContact: {
        name: 'Jane Manager',
        phone: '******-1002',
        relationship: 'Spouse'
      },
      skills: ['Leadership', 'Customer Service', 'Operations Management'],
      certifications: ['Hotel Management Certificate', 'First Aid'],
      workSchedule: {
        monday: { start: '08:00', end: '17:00' },
        tuesday: { start: '08:00', end: '17:00' },
        wednesday: { start: '08:00', end: '17:00' },
        thursday: { start: '08:00', end: '17:00' },
        friday: { start: '08:00', end: '17:00' },
        saturday: { start: '09:00', end: '15:00' },
        sunday: 'off'
      },
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    }
  },
  bookings: {
    'booking-1': {
      hotelId: 'hotel-1',
      hotelName: 'Grand Plaza Hotel',
      roomId: 'room-1',
      roomNumber: '101',
      roomType: 'single',
      bookingId: 'BK001',
      userId: 'guest-1',
      guestName: 'Alice Johnson',
      guestEmail: '<EMAIL>',
      guestPhone: '******-3001',
      checkInDate: Timestamp.fromDate(new Date('2025-07-15')),
      checkOutDate: Timestamp.fromDate(new Date('2025-07-18')),
      nights: 3,
      adults: 1,
      children: 0,
      roomRate: 150,
      totalAmount: 450,
      currency: 'USD',
      taxes: 45,
      fees: 15,
      finalAmount: 510,
      status: 'confirmed',
      paymentStatus: 'paid',
      paymentMethod: 'Credit Card',
      specialRequests: 'Late check-in requested, non-smoking room',
      source: 'Direct Booking',
      notes: 'VIP guest, provide welcome amenities',
      createdAt: Timestamp.now(),
      updatedAt: Timestamp.now()
    }
  }
};

export const importSampleData = async (): Promise<void> => {
  try {
    console.log('🚀 Starting sample data import...');

    // Import hotels
    for (const [hotelId, hotelData] of Object.entries(sampleData.hotels)) {
      await setDoc(doc(db, 'hotels', hotelId), hotelData);
      console.log(`✅ Imported hotel: ${hotelData.name}`);
    }

    // Import rooms
    for (const [roomId, roomData] of Object.entries(sampleData.rooms)) {
      await setDoc(doc(db, 'rooms', roomId), roomData);
      console.log(`✅ Imported room: ${roomData.roomNumber}`);
    }

    // Import staff
    for (const [staffId, staffData] of Object.entries(sampleData.staff)) {
      await setDoc(doc(db, 'staff', staffId), staffData);
      console.log(`✅ Imported staff: ${staffData.name}`);
    }

    // Import bookings
    for (const [bookingId, bookingData] of Object.entries(sampleData.bookings)) {
      await setDoc(doc(db, 'bookings', bookingId), bookingData);
      console.log(`✅ Imported booking: ${bookingData.bookingId}`);
    }

    console.log('🎉 Sample data import completed successfully!');
  } catch (error) {
    console.error('❌ Error importing sample data:', error);
    throw error;
  }
};
