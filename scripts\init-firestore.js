const admin = require('firebase-admin');
const { v4: uuidv4 } = require('uuid');

// Initialize Firebase Admin SDK
const serviceAccount = require('./service-account.json');

admin.initializeApp({
  credential: admin.credential.cert(serviceAccount),
  projectId: 'linkinblink-f544a'
});

const db = admin.firestore();

// Sample data
const sampleData = {
  users: [
    {
      id: 'super-admin-1',
      email: '<EMAIL>',
      name: 'Super Administrator',
      role: 'super_admin',
      phone: '******-0001',
      isActive: true,
      createdAt: admin.firestore.Timestamp.now(),
      updatedAt: admin.firestore.Timestamp.now()
    },
    {
      id: 'vendor-1',
      email: '<EMAIL>',
      name: 'Hotel Vendor 1',
      role: 'vendor',
      phone: '******-0002',
      vendorId: 'vendor-1',
      isActive: true,
      createdAt: admin.firestore.Timestamp.now(),
      updatedAt: admin.firestore.Timestamp.now()
    }
  ],
  hotels: [
    {
      id: 'hotel-1',
      name: 'Grand Plaza Hotel',
      description: 'Luxury hotel in the heart of the city',
      address: '123 Main Street',
      city: 'New York',
      state: 'NY',
      country: 'USA',
      zipCode: '10001',
      phone: '******-1000',
      email: '<EMAIL>',
      website: 'https://grandplaza.com',
      vendorId: 'vendor-1',
      vendorName: 'Hotel Vendor 1',
      rating: 4.5,
      amenities: ['WiFi', 'Pool', 'Gym', 'Restaurant', 'Spa'],
      images: [],
      isActive: true,
      createdAt: admin.firestore.Timestamp.now(),
      updatedAt: admin.firestore.Timestamp.now()
    },
    {
      id: 'hotel-2',
      name: 'Seaside Resort',
      description: 'Beautiful beachfront resort',
      address: '456 Ocean Drive',
      city: 'Miami',
      state: 'FL',
      country: 'USA',
      zipCode: '33101',
      phone: '******-2000',
      email: '<EMAIL>',
      website: 'https://seasideresort.com',
      vendorId: 'vendor-1',
      vendorName: 'Hotel Vendor 1',
      rating: 4.8,
      amenities: ['WiFi', 'Beach Access', 'Pool', 'Restaurant', 'Bar'],
      images: [],
      isActive: true,
      createdAt: admin.firestore.Timestamp.now(),
      updatedAt: admin.firestore.Timestamp.now()
    }
  ],
  rooms: [
    {
      id: 'room-1',
      hotelId: 'hotel-1',
      roomNumber: '101',
      type: 'single',
      status: 'available',
      price: 150,
      capacity: 1,
      amenities: ['WiFi', 'TV', 'AC'],
      images: [],
      description: 'Comfortable single room',
      floor: 1,
      isActive: true,
      createdAt: admin.firestore.Timestamp.now(),
      updatedAt: admin.firestore.Timestamp.now()
    },
    {
      id: 'room-2',
      hotelId: 'hotel-1',
      roomNumber: '102',
      type: 'double',
      status: 'available',
      price: 200,
      capacity: 2,
      amenities: ['WiFi', 'TV', 'AC', 'Mini Bar'],
      images: [],
      description: 'Spacious double room',
      floor: 1,
      isActive: true,
      createdAt: admin.firestore.Timestamp.now(),
      updatedAt: admin.firestore.Timestamp.now()
    }
  ],
  staff: [
    {
      id: 'staff-1',
      hotelId: 'hotel-1',
      vendorId: 'vendor-1',
      name: 'John Manager',
      email: '<EMAIL>',
      phone: '******-1001',
      role: 'manager',
      department: 'Management',
      salary: 60000,
      hireDate: admin.firestore.Timestamp.now(),
      status: 'active',
      address: '789 Staff Street, New York, NY 10001',
      emergencyContact: {
        name: 'Jane Manager',
        phone: '******-1002',
        relationship: 'Spouse'
      },
      createdAt: admin.firestore.Timestamp.now(),
      updatedAt: admin.firestore.Timestamp.now()
    },
    {
      id: 'staff-2',
      hotelId: 'hotel-1',
      vendorId: 'vendor-1',
      name: 'Sarah Receptionist',
      email: '<EMAIL>',
      phone: '******-1003',
      role: 'receptionist',
      department: 'Front Desk',
      salary: 35000,
      hireDate: admin.firestore.Timestamp.now(),
      status: 'active',
      address: '321 Staff Avenue, New York, NY 10001',
      emergencyContact: {
        name: 'Mike Receptionist',
        phone: '******-1004',
        relationship: 'Brother'
      },
      createdAt: admin.firestore.Timestamp.now(),
      updatedAt: admin.firestore.Timestamp.now()
    }
  ],
  bookings: [
    {
      id: 'booking-1',
      hotelId: 'hotel-1',
      hotelName: 'Grand Plaza Hotel',
      roomId: 'room-1',
      roomNumber: '101',
      userId: 'guest-1',
      guestName: 'Alice Johnson',
      guestEmail: '<EMAIL>',
      guestPhone: '******-3001',
      checkInDate: admin.firestore.Timestamp.fromDate(new Date('2025-07-15')),
      checkOutDate: admin.firestore.Timestamp.fromDate(new Date('2025-07-18')),
      adults: 1,
      children: 0,
      totalAmount: 450,
      status: 'confirmed',
      paymentStatus: 'paid',
      specialRequests: 'Late check-in',
      createdAt: admin.firestore.Timestamp.now(),
      updatedAt: admin.firestore.Timestamp.now()
    }
  ],
  shifts: [
    {
      id: 'shift-1',
      hotelId: 'hotel-1',
      vendorId: 'vendor-1',
      staffId: 'staff-2',
      staffName: 'Sarah Receptionist',
      date: admin.firestore.Timestamp.fromDate(new Date('2025-07-08')),
      role: 'receptionist',
      shiftType: 'morning',
      startTime: admin.firestore.Timestamp.fromDate(new Date('2025-07-08T06:00:00')),
      endTime: admin.firestore.Timestamp.fromDate(new Date('2025-07-08T14:00:00')),
      notes: 'Regular morning shift',
      createdAt: admin.firestore.Timestamp.now(),
      updatedAt: admin.firestore.Timestamp.now()
    }
  ]
};

async function initializeFirestore() {
  try {
    console.log('Starting Firestore initialization...');

    // Initialize collections
    for (const [collectionName, documents] of Object.entries(sampleData)) {
      console.log(`Initializing ${collectionName} collection...`);
      
      for (const doc of documents) {
        const { id, ...data } = doc;
        await db.collection(collectionName).doc(id).set(data);
        console.log(`  Created ${collectionName}/${id}`);
      }
    }

    console.log('Firestore initialization completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('Error initializing Firestore:', error);
    process.exit(1);
  }
}

initializeFirestore();
