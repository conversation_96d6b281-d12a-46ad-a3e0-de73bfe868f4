import React from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Container,
  Typography,
  Card,
  CardContent,
  CardActions,
  Button,
  Grid,
  Avatar
} from '@mui/material';
import {
  Hotel as HotelIcon,
  People as StaffIcon,
  AdminPanelSettings as AdminIcon
} from '@mui/icons-material';

const PortalSelector: React.FC = () => {
  const navigate = useNavigate();

  const portals = [
    {
      title: 'Hotels/Vendor Portal',
      description: 'Manage your hotels, rooms, bookings, and staff. Perfect for hotel owners and vendors.',
      icon: <HotelIcon sx={{ fontSize: 48 }} />,
      color: '#1976d2',
      path: '/hotel-portal',
      features: [
        'Hotel Management',
        'Room Management', 
        'Booking System',
        'Staff Management',
        'Revenue Analytics'
      ]
    },
    {
      title: 'Staff Portal',
      description: 'Access your work schedule, view bookings, and manage daily operations.',
      icon: <StaffIcon sx={{ fontSize: 48 }} />,
      color: '#388e3c',
      path: '/staff-portal',
      features: [
        'Work Schedule',
        'Time-off Requests',
        'Guest Check-in/out',
        'Room Status Updates',
        'Task Management'
      ]
    },
    {
      title: 'Super Admin Panel',
      description: 'System-wide administration, vendor management, and platform oversight.',
      icon: <AdminIcon sx={{ fontSize: 48 }} />,
      color: '#d32f2f',
      path: '/admin-portal',
      features: [
        'Vendor Management',
        'System Analytics',
        'User Management',
        'Platform Settings',
        'Global Reports'
      ]
    }
  ];

  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
        py: 8
      }}
    >
      <Container maxWidth="lg">
        <Box textAlign="center" mb={6}>
          <Typography
            variant="h2"
            component="h1"
            gutterBottom
            sx={{ color: 'white', fontWeight: 'bold' }}
          >
            LinkinBlink Hotel Management
          </Typography>
          <Typography
            variant="h5"
            sx={{ color: 'rgba(255,255,255,0.8)', mb: 4 }}
          >
            Choose your portal to get started
          </Typography>
        </Box>

        <Grid container spacing={4}>
          {portals.map((portal, index) => (
            <Grid item xs={12} md={4} key={index}>
              <Card
                sx={{
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  transition: 'transform 0.3s ease-in-out',
                  '&:hover': {
                    transform: 'translateY(-8px)',
                    boxShadow: 6
                  }
                }}
              >
                <CardContent sx={{ flexGrow: 1, textAlign: 'center', p: 4 }}>
                  <Avatar
                    sx={{
                      bgcolor: portal.color,
                      width: 80,
                      height: 80,
                      mx: 'auto',
                      mb: 3
                    }}
                  >
                    {portal.icon}
                  </Avatar>
                  
                  <Typography variant="h5" component="h2" gutterBottom fontWeight="bold">
                    {portal.title}
                  </Typography>
                  
                  <Typography variant="body1" color="text.secondary" paragraph>
                    {portal.description}
                  </Typography>

                  <Box sx={{ mt: 3 }}>
                    <Typography variant="h6" gutterBottom color={portal.color}>
                      Key Features:
                    </Typography>
                    {portal.features.map((feature, idx) => (
                      <Typography
                        key={idx}
                        variant="body2"
                        sx={{ mb: 0.5, color: 'text.secondary' }}
                      >
                        • {feature}
                      </Typography>
                    ))}
                  </Box>
                </CardContent>
                
                <CardActions sx={{ p: 3, pt: 0 }}>
                  <Button
                    variant="contained"
                    fullWidth
                    size="large"
                    onClick={() => navigate(portal.path)}
                    sx={{
                      bgcolor: portal.color,
                      '&:hover': {
                        bgcolor: portal.color,
                        filter: 'brightness(0.9)'
                      }
                    }}
                  >
                    Enter Portal
                  </Button>
                </CardActions>
              </Card>
            </Grid>
          ))}
        </Grid>

        <Box textAlign="center" mt={6}>
          <Typography variant="body2" sx={{ color: 'rgba(255,255,255,0.7)' }}>
            Need help? Contact <NAME_EMAIL>
          </Typography>
        </Box>
      </Container>
    </Box>
  );
};

export default PortalSelector;
