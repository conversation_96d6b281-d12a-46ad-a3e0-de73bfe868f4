import {
  collection,
  doc,
  getDoc,
  getDocs,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit as firestoreLimit,
  Timestamp,
  serverTimestamp
} from 'firebase/firestore';
import { db } from '../firebase/config';
import { Hotel, QueryOptions } from '../types';

// Collection name
const HOTELS_COLLECTION = 'hotels';

/**
 * Get all hotels
 */
export const getAllHotels = async (options?: QueryOptions): Promise<Hotel[]> => {
  try {
    let hotelsQuery = query(
      collection(db, HOTELS_COLLECTION),
      where('isActive', '==', true),
      orderBy('name', 'asc')
    );
    
    if (options?.limit) {
      hotelsQuery = query(hotelsQuery, firestoreLimit(options.limit));
    }
    
    const querySnapshot = await getDocs(hotelsQuery);
    const hotels: Hotel[] = [];
    
    querySnapshot.forEach((doc) => {
      hotels.push({
        id: doc.id,
        ...doc.data() as Omit<Hotel, 'id'>
      });
    });
    
    return hotels;
  } catch (error) {
    console.error('Error getting hotels:', error);
    throw error;
  }
};

/**
 * Get hotels by vendor ID
 */
export const getHotelsByVendor = async (
  vendorId: string,
  options?: QueryOptions
): Promise<Hotel[]> => {
  try {
    let hotelsQuery = query(
      collection(db, HOTELS_COLLECTION),
      where('vendorId', '==', vendorId),
      where('isActive', '==', true),
      orderBy('name', 'asc')
    );
    
    if (options?.limit) {
      hotelsQuery = query(hotelsQuery, firestoreLimit(options.limit));
    }
    
    const querySnapshot = await getDocs(hotelsQuery);
    const hotels: Hotel[] = [];
    
    querySnapshot.forEach((doc) => {
      hotels.push({
        id: doc.id,
        ...doc.data() as Omit<Hotel, 'id'>
      });
    });
    
    return hotels;
  } catch (error) {
    console.error('Error getting hotels by vendor:', error);
    throw error;
  }
};

/**
 * Get a hotel by ID
 */
export const getHotelById = async (hotelId: string): Promise<Hotel | null> => {
  try {
    const docRef = doc(db, HOTELS_COLLECTION, hotelId);
    const docSnap = await getDoc(docRef);
    
    if (docSnap.exists()) {
      return {
        id: docSnap.id,
        ...docSnap.data() as Omit<Hotel, 'id'>
      };
    } else {
      return null;
    }
  } catch (error) {
    console.error('Error getting hotel by ID:', error);
    throw error;
  }
};

/**
 * Create a new hotel
 */
export const createHotel = async (
  hotel: Omit<Hotel, 'id' | 'createdAt' | 'updatedAt'>
): Promise<Hotel> => {
  try {
    const now = Timestamp.now();
    const newHotel: Omit<Hotel, 'id'> = {
      ...hotel,
      isActive: true,
      createdAt: now,
      updatedAt: now
    };
    
    const docRef = await addDoc(collection(db, HOTELS_COLLECTION), newHotel);
    
    return {
      ...newHotel,
      id: docRef.id
    };
  } catch (error) {
    console.error('Error creating hotel:', error);
    throw error;
  }
};

/**
 * Update a hotel
 */
export const updateHotel = async (
  hotelId: string,
  updates: Partial<Omit<Hotel, 'id' | 'createdAt' | 'updatedAt'>>
): Promise<void> => {
  try {
    const updateData = {
      ...updates,
      updatedAt: serverTimestamp()
    };
    
    await updateDoc(doc(db, HOTELS_COLLECTION, hotelId), updateData);
  } catch (error) {
    console.error('Error updating hotel:', error);
    throw error;
  }
};

/**
 * Delete a hotel (soft delete)
 */
export const deleteHotel = async (hotelId: string): Promise<void> => {
  try {
    await updateDoc(doc(db, HOTELS_COLLECTION, hotelId), {
      isActive: false,
      updatedAt: serverTimestamp()
    });
  } catch (error) {
    console.error('Error deleting hotel:', error);
    throw error;
  }
};

/**
 * Search hotels by name or location
 */
export const searchHotels = async (
  searchTerm: string,
  options?: QueryOptions
): Promise<Hotel[]> => {
  try {
    // Note: This is a simple search. For more advanced search,
    // consider using Algolia or similar search service
    const hotelsQuery = query(
      collection(db, HOTELS_COLLECTION),
      where('isActive', '==', true),
      orderBy('name', 'asc')
    );
    
    const querySnapshot = await getDocs(hotelsQuery);
    const hotels: Hotel[] = [];
    
    querySnapshot.forEach((doc) => {
      const hotelData = {
        id: doc.id,
        ...doc.data() as Omit<Hotel, 'id'>
      };
      
      // Filter by search term
      const searchLower = searchTerm.toLowerCase();
      if (
        hotelData.name.toLowerCase().includes(searchLower) ||
        hotelData.city.toLowerCase().includes(searchLower) ||
        hotelData.state.toLowerCase().includes(searchLower) ||
        hotelData.address.toLowerCase().includes(searchLower)
      ) {
        hotels.push(hotelData);
      }
    });
    
    // Apply limit if specified
    if (options?.limit) {
      return hotels.slice(0, options.limit);
    }
    
    return hotels;
  } catch (error) {
    console.error('Error searching hotels:', error);
    throw error;
  }
};

/**
 * Get hotels by city
 */
export const getHotelsByCity = async (
  city: string,
  options?: QueryOptions
): Promise<Hotel[]> => {
  try {
    let hotelsQuery = query(
      collection(db, HOTELS_COLLECTION),
      where('city', '==', city),
      where('isActive', '==', true),
      orderBy('name', 'asc')
    );
    
    if (options?.limit) {
      hotelsQuery = query(hotelsQuery, firestoreLimit(options.limit));
    }
    
    const querySnapshot = await getDocs(hotelsQuery);
    const hotels: Hotel[] = [];
    
    querySnapshot.forEach((doc) => {
      hotels.push({
        id: doc.id,
        ...doc.data() as Omit<Hotel, 'id'>
      });
    });
    
    return hotels;
  } catch (error) {
    console.error('Error getting hotels by city:', error);
    throw error;
  }
};

/**
 * Get hotel statistics
 */
export const getHotelStats = async (hotelId: string): Promise<{
  totalRooms: number;
  occupiedRooms: number;
  availableRooms: number;
  totalBookings: number;
  totalRevenue: number;
}> => {
  try {
    // This would typically involve multiple queries to different collections
    // For now, returning mock data structure
    return {
      totalRooms: 0,
      occupiedRooms: 0,
      availableRooms: 0,
      totalBookings: 0,
      totalRevenue: 0
    };
  } catch (error) {
    console.error('Error getting hotel stats:', error);
    throw error;
  }
};
