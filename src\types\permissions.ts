import { Timestamp } from 'firebase/firestore';

// Granular permissions for hotel operations
export type Permission = 
  // Guest Management
  | 'guest.checkin'
  | 'guest.checkout' 
  | 'guest.view'
  | 'guest.edit'
  | 'guest.create'
  
  // Room Management
  | 'room.view'
  | 'room.edit'
  | 'room.status.update'
  | 'room.maintenance.request'
  | 'room.cleaning.update'
  
  // Booking Management
  | 'booking.view'
  | 'booking.create'
  | 'booking.edit'
  | 'booking.cancel'
  | 'booking.modify'
  
  // Staff Management
  | 'staff.view'
  | 'staff.create'
  | 'staff.edit'
  | 'staff.delete'
  | 'staff.schedule.view'
  | 'staff.schedule.edit'
  
  // Financial
  | 'finance.view'
  | 'finance.reports'
  | 'finance.payments'
  
  // Hotel Management
  | 'hotel.settings'
  | 'hotel.analytics'
  | 'hotel.reports'
  
  // System
  | 'system.admin'
  | 'system.backup';

// Staff role with specific permissions
export interface StaffRole {
  id: string;
  name: string;
  displayName: string;
  permissions: Permission[];
  description: string;
  isDefault: boolean;
}

// Predefined staff roles for hotels
export const DEFAULT_STAFF_ROLES: StaffRole[] = [
  {
    id: 'front_desk_manager',
    name: 'front_desk_manager',
    displayName: 'Front Desk Manager',
    permissions: [
      'guest.checkin',
      'guest.checkout',
      'guest.view',
      'guest.edit',
      'guest.create',
      'booking.view',
      'booking.create',
      'booking.edit',
      'booking.cancel',
      'booking.modify',
      'room.view',
      'room.status.update',
      'staff.view',
      'staff.schedule.view',
      'finance.view'
    ],
    description: 'Manages front desk operations, guest services, and bookings',
    isDefault: true
  },
  {
    id: 'receptionist',
    name: 'receptionist',
    displayName: 'Receptionist',
    permissions: [
      'guest.checkin',
      'guest.checkout',
      'guest.view',
      'booking.view',
      'booking.create',
      'room.view',
      'room.status.update'
    ],
    description: 'Handles guest check-in/out and basic booking operations',
    isDefault: true
  },
  {
    id: 'housekeeping_supervisor',
    name: 'housekeeping_supervisor',
    displayName: 'Housekeeping Supervisor',
    permissions: [
      'room.view',
      'room.status.update',
      'room.cleaning.update',
      'room.maintenance.request',
      'staff.view',
      'staff.schedule.view'
    ],
    description: 'Supervises housekeeping operations and room maintenance',
    isDefault: true
  },
  {
    id: 'housekeeper',
    name: 'housekeeper',
    displayName: 'Housekeeper',
    permissions: [
      'room.view',
      'room.cleaning.update',
      'room.maintenance.request'
    ],
    description: 'Responsible for room cleaning and basic maintenance reporting',
    isDefault: true
  },
  {
    id: 'maintenance_staff',
    name: 'maintenance_staff',
    displayName: 'Maintenance Staff',
    permissions: [
      'room.view',
      'room.status.update',
      'room.maintenance.request'
    ],
    description: 'Handles hotel maintenance and repairs',
    isDefault: true
  },
  {
    id: 'hotel_manager',
    name: 'hotel_manager',
    displayName: 'Hotel Manager',
    permissions: [
      'guest.checkin',
      'guest.checkout',
      'guest.view',
      'guest.edit',
      'guest.create',
      'booking.view',
      'booking.create',
      'booking.edit',
      'booking.cancel',
      'booking.modify',
      'room.view',
      'room.edit',
      'room.status.update',
      'room.maintenance.request',
      'room.cleaning.update',
      'staff.view',
      'staff.create',
      'staff.edit',
      'staff.schedule.view',
      'staff.schedule.edit',
      'finance.view',
      'finance.reports',
      'hotel.analytics',
      'hotel.reports'
    ],
    description: 'Full hotel operations management',
    isDefault: true
  },
  {
    id: 'security_guard',
    name: 'security_guard',
    displayName: 'Security Guard',
    permissions: [
      'guest.view',
      'room.view'
    ],
    description: 'Hotel security and safety monitoring',
    isDefault: true
  }
];

// Permission groups for easier management
export const PERMISSION_GROUPS = {
  'Guest Management': [
    'guest.checkin',
    'guest.checkout',
    'guest.view',
    'guest.edit',
    'guest.create'
  ],
  'Room Operations': [
    'room.view',
    'room.edit',
    'room.status.update',
    'room.maintenance.request',
    'room.cleaning.update'
  ],
  'Booking Management': [
    'booking.view',
    'booking.create',
    'booking.edit',
    'booking.cancel',
    'booking.modify'
  ],
  'Staff Management': [
    'staff.view',
    'staff.create',
    'staff.edit',
    'staff.delete',
    'staff.schedule.view',
    'staff.schedule.edit'
  ],
  'Financial': [
    'finance.view',
    'finance.reports',
    'finance.payments'
  ],
  'Administration': [
    'hotel.settings',
    'hotel.analytics',
    'hotel.reports',
    'system.admin',
    'system.backup'
  ]
};

// User permission interface
export interface UserPermissions {
  id: string;
  userId: string;
  hotelId: string;
  roleId: string;
  permissions: Permission[];
  grantedBy: string;
  grantedAt: Timestamp;
  expiresAt?: Timestamp;
  isActive: boolean;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

// Permission check helper
export const hasPermission = (userPermissions: Permission[], requiredPermission: Permission): boolean => {
  return userPermissions.includes(requiredPermission);
};

// Multiple permission check
export const hasAnyPermission = (userPermissions: Permission[], requiredPermissions: Permission[]): boolean => {
  return requiredPermissions.some(permission => userPermissions.includes(permission));
};

// All permissions check
export const hasAllPermissions = (userPermissions: Permission[], requiredPermissions: Permission[]): boolean => {
  return requiredPermissions.every(permission => userPermissions.includes(permission));
};
