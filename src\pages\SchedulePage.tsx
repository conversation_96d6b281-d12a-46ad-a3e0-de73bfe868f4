import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Alert
} from '@mui/material';
import { useAuth } from '../hooks/useAuth';
import StaffCalendar from '../components/staff/StaffCalendar';
import { getHotelsByVendor } from '../services/hotelService';
import { getStaffForHotel } from '../services/staffService';
import { 
  getShiftsForHotel, 
  getTimeOffRequestsForHotel,
  createShift,
  updateShift,
  deleteShift
} from '../services/shiftService';

const SchedulePage: React.FC = () => {
  const { user } = useAuth();
  const [hotels, setHotels] = useState<any[]>([]);
  const [selectedHotel, setSelectedHotel] = useState<string>('');
  const [staffList, setStaffList] = useState<any[]>([]);
  const [shifts, setShifts] = useState<any[]>([]);
  const [timeOffRequests, setTimeOffRequests] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    fetchHotels();
  }, [user]);

  useEffect(() => {
    if (selectedHotel) {
      fetchScheduleData();
    }
  }, [selectedHotel]);

  const fetchHotels = async () => {
    if (!user) return;

    try {
      let hotelData: any[] = [];
      
      if (user.role === 'super_admin') {
        // For super admin, would need to fetch all hotels
        hotelData = [];
      } else if ((user.role === 'admin' || user.role === 'vendor') && user.vendorId) {
        hotelData = await getHotelsByVendor(user.vendorId);
      } else if (user.hotelId) {
        // For staff, just show their hotel
        hotelData = [{ id: user.hotelId, name: 'Your Hotel' }];
      }

      setHotels(hotelData);
      if (hotelData.length > 0) {
        setSelectedHotel(hotelData[0].id);
      }
    } catch (err: any) {
      console.error('Error fetching hotels:', err);
      setError(err.message || 'Failed to load hotels');
    }
  };

  const fetchScheduleData = async () => {
    if (!selectedHotel) return;

    try {
      setLoading(true);
      setError(null);

      // Fetch staff, shifts, and time off requests in parallel
      const [staffData, shiftsData, timeOffData] = await Promise.all([
        getStaffForHotel(selectedHotel),
        getShiftsForHotel(selectedHotel),
        getTimeOffRequestsForHotel(selectedHotel)
      ]);

      setStaffList(staffData);
      setShifts(shiftsData);
      setTimeOffRequests(timeOffData);
    } catch (err: any) {
      console.error('Error fetching schedule data:', err);
      setError(err.message || 'Failed to load schedule data');
    } finally {
      setLoading(false);
    }
  };

  const handleShiftCreate = async (shiftData: any) => {
    try {
      await createShift(shiftData);
      fetchScheduleData(); // Refresh data
    } catch (err: any) {
      console.error('Error creating shift:', err);
      throw err;
    }
  };

  const handleShiftUpdate = async (shiftId: string, updates: any) => {
    try {
      await updateShift(shiftId, updates);
      fetchScheduleData(); // Refresh data
    } catch (err: any) {
      console.error('Error updating shift:', err);
      throw err;
    }
  };

  const handleShiftDelete = async (shiftId: string) => {
    try {
      await deleteShift(shiftId);
      fetchScheduleData(); // Refresh data
    } catch (err: any) {
      console.error('Error deleting shift:', err);
      throw err;
    }
  };

  const handleRefresh = () => {
    fetchScheduleData();
  };

  if (loading && !selectedHotel) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1">
          Staff Schedule
        </Typography>
      </Box>

      {hotels.length > 1 && (
        <Box mb={3}>
          <FormControl sx={{ minWidth: 200 }}>
            <InputLabel>Select Hotel</InputLabel>
            <Select
              value={selectedHotel}
              label="Select Hotel"
              onChange={(e) => setSelectedHotel(e.target.value)}
            >
              {hotels.map((hotel) => (
                <MenuItem key={hotel.id} value={hotel.id}>
                  {hotel.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Box>
      )}

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {selectedHotel && (
        <StaffCalendar
          hotelId={selectedHotel}
          vendorId={user?.vendorId || user?.id || ''}
          staffList={staffList}
          shifts={shifts}
          timeOffRequests={timeOffRequests}
          loading={loading}
          error={error}
          onRefresh={handleRefresh}
          onShiftCreate={handleShiftCreate}
          onShiftUpdate={handleShiftUpdate}
          onShiftDelete={handleShiftDelete}
        />
      )}
    </Box>
  );
};

export default SchedulePage;
