const { spawn } = require('child_process');
const path = require('path');

const projectDir = 'C:\\Users\\<USER>\\Desktop\\linkinblink-hotel-management';
process.chdir(projectDir);

console.log('Starting build process...');
console.log('Working directory:', process.cwd());

const buildProcess = spawn('npm', ['run', 'build'], {
  stdio: 'inherit',
  shell: true
});

buildProcess.on('close', (code) => {
  console.log(`Build process exited with code ${code}`);
  if (code === 0) {
    console.log('✅ Build completed successfully!');
  } else {
    console.log('❌ Build failed with errors.');
  }
});

buildProcess.on('error', (error) => {
  console.error('Error starting build process:', error);
});
