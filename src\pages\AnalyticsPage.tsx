import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Alert,
  Tabs,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  AttachMoney as MoneyIcon,
  Hotel as HotelIcon,
  People as PeopleIcon,
  Assessment as AssessmentIcon
} from '@mui/icons-material';
import { useAuth } from '../hooks/useAuth';
import { getHotelsByVendor } from '../services/hotelService';
import { getBookingStats } from '../services/bookingService';
import { getRoomStats } from '../services/roomService';
import { getStaffStats } from '../services/staffService';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`analytics-tabpanel-${index}`}
      aria-labelledby={`analytics-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

interface StatCardProps {
  title: string;
  value: string | number;
  icon: React.ReactElement;
  color: string;
  trend?: string;
}

const StatCard: React.FC<StatCardProps> = ({ title, value, icon, color, trend }) => (
  <Card>
    <CardContent>
      <Box display="flex" alignItems="center" justifyContent="space-between">
        <Box>
          <Typography color="textSecondary" gutterBottom variant="body2">
            {title}
          </Typography>
          <Typography variant="h4" component="h2">
            {value}
          </Typography>
          {trend && (
            <Typography variant="body2" sx={{ color: 'success.main', mt: 1 }}>
              {trend}
            </Typography>
          )}
        </Box>
        <Box
          sx={{
            backgroundColor: color,
            borderRadius: '50%',
            width: 56,
            height: 56,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: 'white'
          }}
        >
          {icon}
        </Box>
      </Box>
    </CardContent>
  </Card>
);

const AnalyticsPage: React.FC = () => {
  const { user } = useAuth();
  const [hotels, setHotels] = useState<any[]>([]);
  const [selectedHotel, setSelectedHotel] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [tabValue, setTabValue] = useState(0);
  
  const [analytics, setAnalytics] = useState({
    revenue: {
      total: 0,
      thisMonth: 0,
      lastMonth: 0,
      growth: 0
    },
    bookings: {
      total: 0,
      pending: 0,
      confirmed: 0,
      checkedIn: 0,
      checkedOut: 0,
      cancelled: 0
    },
    rooms: {
      total: 0,
      available: 0,
      occupied: 0,
      maintenance: 0,
      occupancyRate: 0
    },
    staff: {
      total: 0,
      active: 0,
      departments: {} as Record<string, number>
    }
  });

  useEffect(() => {
    fetchHotels();
  }, [user]);

  useEffect(() => {
    if (selectedHotel) {
      fetchAnalytics();
    }
  }, [selectedHotel]);

  const fetchHotels = async () => {
    if (!user) return;

    try {
      let hotelData: any[] = [];
      
      if (user.role === 'super_admin') {
        // For super admin, would need to fetch all hotels
        hotelData = [];
      } else if ((user.role === 'admin' || user.role === 'vendor') && user.vendorId) {
        hotelData = await getHotelsByVendor(user.vendorId);
      }

      setHotels(hotelData);
      if (hotelData.length > 0) {
        setSelectedHotel(hotelData[0].id);
      }
    } catch (err: any) {
      console.error('Error fetching hotels:', err);
      setError(err.message || 'Failed to load hotels');
    }
  };

  const fetchAnalytics = async () => {
    if (!selectedHotel) return;

    try {
      setLoading(true);
      setError(null);

      // Fetch analytics data in parallel
      const [bookingStats, roomStats, staffStats] = await Promise.all([
        getBookingStats(selectedHotel),
        getRoomStats(selectedHotel),
        getStaffStats(selectedHotel)
      ]);

      setAnalytics({
        revenue: {
          total: bookingStats.totalRevenue,
          thisMonth: bookingStats.totalRevenue * 0.3, // Mock data
          lastMonth: bookingStats.totalRevenue * 0.25, // Mock data
          growth: 20 // Mock growth percentage
        },
        bookings: {
          total: bookingStats.totalBookings,
          pending: bookingStats.pendingBookings,
          confirmed: bookingStats.confirmedBookings,
          checkedIn: bookingStats.checkedInBookings,
          checkedOut: bookingStats.checkedOutBookings,
          cancelled: bookingStats.cancelledBookings
        },
        rooms: {
          total: roomStats.totalRooms,
          available: roomStats.availableRooms,
          occupied: roomStats.occupiedRooms,
          maintenance: roomStats.maintenanceRooms,
          occupancyRate: roomStats.totalRooms > 0 ? 
            Math.round((roomStats.occupiedRooms / roomStats.totalRooms) * 100) : 0
        },
        staff: {
          total: staffStats.totalStaff,
          active: staffStats.activeStaff,
          departments: staffStats.departmentBreakdown || {}
        }
      });
    } catch (err: any) {
      console.error('Error fetching analytics:', err);
      setError(err.message || 'Failed to load analytics');
    } finally {
      setLoading(false);
    }
  };

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  if (loading && !selectedHotel) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1">
          Revenue Analytics
        </Typography>
      </Box>

      {hotels.length > 1 && (
        <Box mb={3}>
          <FormControl sx={{ minWidth: 200 }}>
            <InputLabel>Select Hotel</InputLabel>
            <Select
              value={selectedHotel}
              label="Select Hotel"
              onChange={(e) => setSelectedHotel(e.target.value)}
            >
              {hotels.map((hotel) => (
                <MenuItem key={hotel.id} value={hotel.id}>
                  {hotel.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Box>
      )}

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {loading ? (
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
          <CircularProgress />
        </Box>
      ) : (
        <>
          {/* Key Metrics */}
          <Grid container spacing={3} sx={{ mb: 4 }}>
            <Grid item xs={12} sm={6} md={3}>
              <StatCard
                title="Total Revenue"
                value={`$${analytics.revenue.total.toLocaleString()}`}
                icon={<MoneyIcon />}
                color="#7b1fa2"
                trend={`+${analytics.revenue.growth}% from last month`}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <StatCard
                title="Occupancy Rate"
                value={`${analytics.rooms.occupancyRate}%`}
                icon={<HotelIcon />}
                color="#1976d2"
              />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <StatCard
                title="Total Bookings"
                value={analytics.bookings.total}
                icon={<AssessmentIcon />}
                color="#f57c00"
              />
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <StatCard
                title="Active Staff"
                value={analytics.staff.active}
                icon={<PeopleIcon />}
                color="#388e3c"
              />
            </Grid>
          </Grid>

          {/* Detailed Analytics */}
          <Card>
            <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
              <Tabs value={tabValue} onChange={handleTabChange}>
                <Tab label="Revenue" />
                <Tab label="Bookings" />
                <Tab label="Rooms" />
                <Tab label="Staff" />
              </Tabs>
            </Box>

            {/* Revenue Tab */}
            <TabPanel value={tabValue} index={0}>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Typography variant="h6" gutterBottom>
                    Revenue Breakdown
                  </Typography>
                  <TableContainer component={Paper} variant="outlined">
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell>Period</TableCell>
                          <TableCell align="right">Amount</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        <TableRow>
                          <TableCell>This Month</TableCell>
                          <TableCell align="right">${analytics.revenue.thisMonth.toLocaleString()}</TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell>Last Month</TableCell>
                          <TableCell align="right">${analytics.revenue.lastMonth.toLocaleString()}</TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell><strong>Total</strong></TableCell>
                          <TableCell align="right"><strong>${analytics.revenue.total.toLocaleString()}</strong></TableCell>
                        </TableRow>
                      </TableBody>
                    </Table>
                  </TableContainer>
                </Grid>
              </Grid>
            </TabPanel>

            {/* Bookings Tab */}
            <TabPanel value={tabValue} index={1}>
              <Typography variant="h6" gutterBottom>
                Booking Status Overview
              </Typography>
              <Grid container spacing={2}>
                <Grid item>
                  <Chip label={`Pending: ${analytics.bookings.pending}`} color="warning" />
                </Grid>
                <Grid item>
                  <Chip label={`Confirmed: ${analytics.bookings.confirmed}`} color="info" />
                </Grid>
                <Grid item>
                  <Chip label={`Checked In: ${analytics.bookings.checkedIn}`} color="success" />
                </Grid>
                <Grid item>
                  <Chip label={`Checked Out: ${analytics.bookings.checkedOut}`} color="default" />
                </Grid>
                <Grid item>
                  <Chip label={`Cancelled: ${analytics.bookings.cancelled}`} color="error" />
                </Grid>
              </Grid>
            </TabPanel>

            {/* Rooms Tab */}
            <TabPanel value={tabValue} index={2}>
              <Typography variant="h6" gutterBottom>
                Room Status Overview
              </Typography>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <TableContainer component={Paper} variant="outlined">
                    <Table>
                      <TableHead>
                        <TableRow>
                          <TableCell>Status</TableCell>
                          <TableCell align="right">Count</TableCell>
                          <TableCell align="right">Percentage</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        <TableRow>
                          <TableCell>Available</TableCell>
                          <TableCell align="right">{analytics.rooms.available}</TableCell>
                          <TableCell align="right">
                            {analytics.rooms.total > 0 ? 
                              Math.round((analytics.rooms.available / analytics.rooms.total) * 100) : 0}%
                          </TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell>Occupied</TableCell>
                          <TableCell align="right">{analytics.rooms.occupied}</TableCell>
                          <TableCell align="right">{analytics.rooms.occupancyRate}%</TableCell>
                        </TableRow>
                        <TableRow>
                          <TableCell>Maintenance</TableCell>
                          <TableCell align="right">{analytics.rooms.maintenance}</TableCell>
                          <TableCell align="right">
                            {analytics.rooms.total > 0 ? 
                              Math.round((analytics.rooms.maintenance / analytics.rooms.total) * 100) : 0}%
                          </TableCell>
                        </TableRow>
                      </TableBody>
                    </Table>
                  </TableContainer>
                </Grid>
              </Grid>
            </TabPanel>

            {/* Staff Tab */}
            <TabPanel value={tabValue} index={3}>
              <Typography variant="h6" gutterBottom>
                Staff Overview
              </Typography>
              <Grid container spacing={3}>
                <Grid item xs={12} md={6}>
                  <Typography variant="subtitle1" gutterBottom>
                    Department Breakdown
                  </Typography>
                  {Object.entries(analytics.staff.departments).map(([dept, count]) => (
                    <Box key={dept} display="flex" justifyContent="space-between" mb={1}>
                      <Typography>{dept}</Typography>
                      <Typography>{count} staff</Typography>
                    </Box>
                  ))}
                </Grid>
              </Grid>
            </TabPanel>
          </Card>
        </>
      )}
    </Box>
  );
};

export default AnalyticsPage;
