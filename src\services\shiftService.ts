import {
  collection,
  doc,
  getDoc,
  getDocs,
  addDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit as firestoreLimit,
  Timestamp,
  serverTimestamp
} from 'firebase/firestore';
import { db } from '../firebase/config';
import { Shift, TimeOffRequest, QueryOptions } from '../types';

// Collection names
const SHIFTS_COLLECTION = 'shifts';
const TIME_OFF_REQUESTS_COLLECTION = 'timeOffRequests';

/**
 * Get all shifts for a hotel
 */
export const getShiftsForHotel = async (
  hotelId: string,
  options?: QueryOptions
): Promise<Shift[]> => {
  try {
    let shiftsQuery = query(
      collection(db, SHIFTS_COLLECTION),
      where('hotelId', '==', hotelId),
      orderBy('date', 'desc')
    );
    
    if (options?.limit) {
      shiftsQuery = query(shiftsQuery, firestoreLimit(options.limit));
    }
    
    const querySnapshot = await getDocs(shiftsQuery);
    const shifts: Shift[] = [];
    
    querySnapshot.forEach((doc) => {
      shifts.push({
        id: doc.id,
        ...doc.data() as Omit<Shift, 'id'>
      });
    });
    
    return shifts;
  } catch (error) {
    console.error('Error getting shifts for hotel:', error);
    throw error;
  }
};

/**
 * Get shifts by date range
 */
export const getShiftsByDateRange = async (
  hotelId: string,
  startDate: Date,
  endDate: Date
): Promise<Shift[]> => {
  try {
    const startTimestamp = Timestamp.fromDate(startDate);
    const endTimestamp = Timestamp.fromDate(endDate);
    
    const shiftsQuery = query(
      collection(db, SHIFTS_COLLECTION),
      where('hotelId', '==', hotelId),
      where('date', '>=', startTimestamp),
      where('date', '<=', endTimestamp),
      orderBy('date', 'asc')
    );
    
    const querySnapshot = await getDocs(shiftsQuery);
    const shifts: Shift[] = [];
    
    querySnapshot.forEach((doc) => {
      shifts.push({
        id: doc.id,
        ...doc.data() as Omit<Shift, 'id'>
      });
    });
    
    return shifts;
  } catch (error) {
    console.error('Error getting shifts by date range:', error);
    throw error;
  }
};

/**
 * Get shifts for a staff member
 */
export const getShiftsForStaff = async (
  staffId: string,
  options?: QueryOptions
): Promise<Shift[]> => {
  try {
    let shiftsQuery = query(
      collection(db, SHIFTS_COLLECTION),
      where('staffId', '==', staffId),
      orderBy('date', 'desc')
    );
    
    if (options?.limit) {
      shiftsQuery = query(shiftsQuery, firestoreLimit(options.limit));
    }
    
    const querySnapshot = await getDocs(shiftsQuery);
    const shifts: Shift[] = [];
    
    querySnapshot.forEach((doc) => {
      shifts.push({
        id: doc.id,
        ...doc.data() as Omit<Shift, 'id'>
      });
    });
    
    return shifts;
  } catch (error) {
    console.error('Error getting shifts for staff:', error);
    throw error;
  }
};

/**
 * Create a new shift
 */
export const createShift = async (
  shift: Omit<Shift, 'id' | 'createdAt' | 'updatedAt'>
): Promise<Shift> => {
  try {
    const now = Timestamp.now();
    const newShift: Omit<Shift, 'id'> = {
      ...shift,
      createdAt: now,
      updatedAt: now
    };
    
    const docRef = await addDoc(collection(db, SHIFTS_COLLECTION), newShift);
    
    return {
      ...newShift,
      id: docRef.id
    };
  } catch (error) {
    console.error('Error creating shift:', error);
    throw error;
  }
};

/**
 * Update a shift
 */
export const updateShift = async (
  shiftId: string,
  updates: Partial<Omit<Shift, 'id' | 'createdAt' | 'updatedAt'>>
): Promise<void> => {
  try {
    const updateData = {
      ...updates,
      updatedAt: serverTimestamp()
    };
    
    await updateDoc(doc(db, SHIFTS_COLLECTION, shiftId), updateData);
  } catch (error) {
    console.error('Error updating shift:', error);
    throw error;
  }
};

/**
 * Delete a shift
 */
export const deleteShift = async (shiftId: string): Promise<void> => {
  try {
    await deleteDoc(doc(db, SHIFTS_COLLECTION, shiftId));
  } catch (error) {
    console.error('Error deleting shift:', error);
    throw error;
  }
};

/**
 * Get time off requests for a hotel
 */
export const getTimeOffRequestsForHotel = async (
  hotelId: string,
  options?: QueryOptions
): Promise<TimeOffRequest[]> => {
  try {
    let requestsQuery = query(
      collection(db, TIME_OFF_REQUESTS_COLLECTION),
      where('hotelId', '==', hotelId),
      orderBy('createdAt', 'desc')
    );
    
    if (options?.limit) {
      requestsQuery = query(requestsQuery, firestoreLimit(options.limit));
    }
    
    const querySnapshot = await getDocs(requestsQuery);
    const requests: TimeOffRequest[] = [];
    
    querySnapshot.forEach((doc) => {
      requests.push({
        id: doc.id,
        ...doc.data() as Omit<TimeOffRequest, 'id'>
      });
    });
    
    return requests;
  } catch (error) {
    console.error('Error getting time off requests for hotel:', error);
    throw error;
  }
};

/**
 * Create a time off request
 */
export const createTimeOffRequest = async (
  request: Omit<TimeOffRequest, 'id' | 'createdAt' | 'updatedAt'>
): Promise<TimeOffRequest> => {
  try {
    const now = Timestamp.now();
    const newRequest: Omit<TimeOffRequest, 'id'> = {
      ...request,
      status: 'pending',
      createdAt: now,
      updatedAt: now
    };
    
    const docRef = await addDoc(collection(db, TIME_OFF_REQUESTS_COLLECTION), newRequest);
    
    return {
      ...newRequest,
      id: docRef.id
    };
  } catch (error) {
    console.error('Error creating time off request:', error);
    throw error;
  }
};

/**
 * Update a time off request
 */
export const updateTimeOffRequest = async (
  requestId: string,
  updates: Partial<Omit<TimeOffRequest, 'id' | 'createdAt' | 'updatedAt'>>
): Promise<void> => {
  try {
    const updateData = {
      ...updates,
      updatedAt: serverTimestamp()
    };
    
    await updateDoc(doc(db, TIME_OFF_REQUESTS_COLLECTION, requestId), updateData);
  } catch (error) {
    console.error('Error updating time off request:', error);
    throw error;
  }
};

/**
 * Approve a time off request
 */
export const approveTimeOffRequest = async (
  requestId: string,
  approvedBy: string
): Promise<void> => {
  try {
    await updateDoc(doc(db, TIME_OFF_REQUESTS_COLLECTION, requestId), {
      status: 'approved',
      approvedBy,
      approvedAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });
  } catch (error) {
    console.error('Error approving time off request:', error);
    throw error;
  }
};

/**
 * Reject a time off request
 */
export const rejectTimeOffRequest = async (
  requestId: string,
  approvedBy: string
): Promise<void> => {
  try {
    await updateDoc(doc(db, TIME_OFF_REQUESTS_COLLECTION, requestId), {
      status: 'rejected',
      approvedBy,
      approvedAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });
  } catch (error) {
    console.error('Error rejecting time off request:', error);
    throw error;
  }
};
