import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Grid,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Tabs,
  Tab,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  Badge,
  Tooltip,
  Menu,
  MenuList,
  MenuItem as MenuItemComponent
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  CheckCircle as ConfirmedIcon,
  Schedule as PendingIcon,
  Cancel as CancelledIcon,
  Payment as PaymentIcon,
  Person as GuestIcon,
  Hotel as RoomIcon,
  DateRange as DateIcon,
  AttachMoney as MoneyIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  Print as PrintIcon,
  Send as SendIcon,
  MoreVert as MoreIcon,
  Security as SecurityIcon,
  Login as CheckInIcon,
  Logout as CheckOutIcon,
  Receipt as ReceiptIcon,
  Save as SaveIcon,
  Cancel as CancelIcon
} from '@mui/icons-material';
import { useAuth } from '../hooks/useAuth';
import { Booking } from '../types';
import { getBookingsForHotel, createBooking, updateBooking, cancelBooking } from '../services/bookingService';
import { getHotelsByVendor, getAllHotels } from '../services/hotelService';
import { collection, query, where, onSnapshot, orderBy } from 'firebase/firestore';
import { db } from '../firebase/config';
import { isAadhaarVerificationRequired, getAadhaarVerificationStatus } from '../services/aadhaarService';
import AadhaarVerification from '../components/AadhaarVerification';
import { format, addDays, differenceInDays } from 'date-fns';

// Utility function to convert Timestamp to Date
const toDate = (date: any): Date => {
  if (!date) return new Date();
  if (date instanceof Date) return date;
  if (date.toDate && typeof date.toDate === 'function') return date.toDate();
  return new Date(date);
};

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`bookings-tabpanel-${index}`}
      aria-labelledby={`bookings-tab-${index}`}
      {...other}
    >
      {value === index && <Box>{children}</Box>}
    </div>
  );
}

const BookingManagement: React.FC = () => {
  const { user } = useAuth();
  const [hotels, setHotels] = useState<any[]>([]);
  const [selectedHotel, setSelectedHotel] = useState<string>('all');
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [viewDialogOpen, setViewDialogOpen] = useState(false);
  const [editingBooking, setEditingBooking] = useState<Booking | null>(null);
  const [viewingBooking, setViewingBooking] = useState<Booking | null>(null);
  const [tabValue, setTabValue] = useState(0);
  const [actionMenuAnchor, setActionMenuAnchor] = useState<null | HTMLElement>(null);
  const [selectedBookingForAction, setSelectedBookingForAction] = useState<any | null>(null);

  // Aadhaar verification state
  const [aadhaarVerificationOpen, setAadhaarVerificationOpen] = useState(false);
  const [pendingBookingData, setPendingBookingData] = useState<any>(null);

  const [formData, setFormData] = useState({
    guestName: '',
    guestEmail: '',
    guestPhone: '',
    hotelId: '',
    roomNumber: '',
    roomType: '',
    checkInDate: '',
    checkOutDate: '',
    numberOfGuests: 1,
    totalAmount: 0,
    paidAmount: 0,
    status: 'pending' as any,
    paymentStatus: 'pending' as any,
    specialRequests: '',
    notes: ''
  });

  // Mock booking data for demonstration
  const [mockBookings] = useState<any[]>([
    {
      id: '1',
      bookingNumber: 'BK001',
      guestName: 'John Smith',
      guestEmail: '<EMAIL>',
      guestPhone: '+****************',
      hotelId: '1',
      hotelName: 'Grand Plaza Hotel',
      roomNumber: '101',
      roomType: 'Standard',
      checkInDate: new Date(),
      checkOutDate: addDays(new Date(), 3),
      numberOfGuests: 2,
      totalAmount: 360,
      paidAmount: 360,
      status: 'confirmed',
      paymentStatus: 'paid',
      bookingDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
      specialRequests: 'Late check-in requested',
      source: 'website',
      notes: 'VIP guest - provide welcome amenities'
    },
    {
      id: '2',
      bookingNumber: 'BK002',
      guestName: 'Sarah Johnson',
      guestEmail: '<EMAIL>',
      guestPhone: '+****************',
      hotelId: '1',
      hotelName: 'Grand Plaza Hotel',
      roomNumber: '205',
      roomType: 'Deluxe',
      checkInDate: addDays(new Date(), 1),
      checkOutDate: addDays(new Date(), 4),
      numberOfGuests: 1,
      totalAmount: 540,
      paidAmount: 0,
      status: 'pending',
      paymentStatus: 'pending',
      bookingDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
      specialRequests: 'Non-smoking room',
      source: 'phone',
      notes: 'Business traveler'
    },
    {
      id: '3',
      bookingNumber: 'BK003',
      guestName: 'Michael Brown',
      guestEmail: '<EMAIL>',
      guestPhone: '+****************',
      hotelId: '2',
      hotelName: 'Seaside Resort',
      roomNumber: '301',
      roomType: 'Suite',
      checkInDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
      checkOutDate: addDays(new Date(), 2),
      numberOfGuests: 4,
      totalAmount: 900,
      paidAmount: 450,
      status: 'checked_in',
      paymentStatus: 'partial',
      bookingDate: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
      specialRequests: 'Extra bed required',
      source: 'booking.com',
      notes: 'Family vacation'
    },
    {
      id: '4',
      bookingNumber: 'BK004',
      guestName: 'Emily Davis',
      guestEmail: '<EMAIL>',
      guestPhone: '+****************',
      hotelId: '1',
      hotelName: 'Grand Plaza Hotel',
      roomNumber: '102',
      roomType: 'Standard',
      checkInDate: addDays(new Date(), 7),
      checkOutDate: addDays(new Date(), 10),
      numberOfGuests: 2,
      totalAmount: 360,
      paidAmount: 0,
      status: 'cancelled',
      paymentStatus: 'refunded',
      bookingDate: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
      specialRequests: '',
      source: 'website',
      notes: 'Cancelled due to emergency'
    },
    {
      id: '5',
      bookingNumber: 'BK005',
      guestName: 'David Wilson',
      guestEmail: '<EMAIL>',
      guestPhone: '+****************',
      hotelId: '2',
      hotelName: 'Seaside Resort',
      roomNumber: '203',
      roomType: 'Deluxe',
      checkInDate: addDays(new Date(), 14),
      checkOutDate: addDays(new Date(), 17),
      numberOfGuests: 2,
      totalAmount: 540,
      paidAmount: 540,
      status: 'confirmed',
      paymentStatus: 'paid',
      bookingDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
      specialRequests: 'Anniversary celebration',
      source: 'website',
      notes: 'Honeymoon package requested'
    }
  ]);

  useEffect(() => {
    fetchHotels();
  }, [user]);

  useEffect(() => {
    if (!user || !selectedHotel) return;

    let unsubscribe: (() => void) | undefined;

    // Set up real-time listener for bookings
    if (user.role === 'super_admin') {
      if (selectedHotel === 'all') {
        // Listen to all bookings (this might be expensive, consider pagination)
        const bookingsQuery = query(
          collection(db, 'bookings'),
          orderBy('createdAt', 'desc')
        );

        unsubscribe = onSnapshot(bookingsQuery, (snapshot) => {
          const bookingList: Booking[] = [];
          snapshot.forEach((doc) => {
            bookingList.push({
              id: doc.id,
              ...doc.data() as Omit<Booking, 'id'>
            });
          });
          setBookings(bookingList);
          setLoading(false);
        }, (error) => {
          console.error('Error listening to all bookings:', error);
          setError(error.message);
          fetchBookings();
        });
      } else {
        // Listen to bookings for specific hotel
        const hotelBookingsQuery = query(
          collection(db, 'bookings'),
          where('hotelId', '==', selectedHotel),
          orderBy('createdAt', 'desc')
        );

        unsubscribe = onSnapshot(hotelBookingsQuery, (snapshot) => {
          const bookingList: Booking[] = [];
          snapshot.forEach((doc) => {
            bookingList.push({
              id: doc.id,
              ...doc.data() as Omit<Booking, 'id'>
            });
          });
          setBookings(bookingList);
          setLoading(false);
        }, (error) => {
          console.error('Error listening to hotel bookings:', error);
          setError(error.message);
          fetchBookings();
        });
      }
    } else if (user.role === 'vendor' && user.hotelId) {
      // Vendor sees only their hotel's bookings
      const vendorBookingsQuery = query(
        collection(db, 'bookings'),
        where('hotelId', '==', user.hotelId),
        orderBy('createdAt', 'desc')
      );

      unsubscribe = onSnapshot(vendorBookingsQuery, (snapshot) => {
        const bookingList: Booking[] = [];
        snapshot.forEach((doc) => {
          bookingList.push({
            id: doc.id,
            ...doc.data() as Omit<Booking, 'id'>
          });
        });
        setBookings(bookingList);
        setLoading(false);
      }, (error) => {
        console.error('Error listening to vendor bookings:', error);
        setError(error.message);
        fetchBookings();
      });
    }

    // Cleanup listener on unmount
    return () => {
      if (unsubscribe) {
        unsubscribe();
      }
    };
  }, [user, selectedHotel]);

  const fetchHotels = async () => {
    try {
      if (user?.role === 'super_admin') {
        // Super admin sees all hotels
        const allHotels = [
          { id: '1', name: 'Grand Plaza Hotel' },
          { id: '2', name: 'Seaside Resort' },
          { id: '3', name: 'Mountain Lodge' }
        ];
        setHotels(allHotels);
      } else if (user?.vendorId) {
        // Vendor sees only their hotels
        const vendorHotels = [{ id: '1', name: 'Grand Plaza Hotel' }];
        setHotels(vendorHotels);
        setSelectedHotel('1');
      }
    } catch (err: any) {
      setError(err.message);
    }
  };

  const fetchBookings = async () => {
    try {
      setLoading(true);
      let allBookings: Booking[] = [];

      if (user?.role === 'super_admin') {
        // Super admin can see all bookings
        if (selectedHotel === 'all') {
          // Get bookings from all hotels
          const hotelList = await getAllHotels();
          for (const hotel of hotelList) {
            const hotelBookings = await getBookingsForHotel(hotel.id);
            allBookings.push(...hotelBookings);
          }
        } else {
          allBookings = await getBookingsForHotel(selectedHotel);
        }
      } else if (user?.role === 'vendor' && user.hotelId) {
        // Vendor sees only their hotel's bookings
        allBookings = await getBookingsForHotel(user.hotelId);
      }

      setBookings(allBookings);
    } catch (err: any) {
      console.error('Error fetching bookings:', err);
      setError(err.message);
      // Fallback to mock data if Firebase fails
      setBookings(mockBookings);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'confirmed':
        return '#4caf50';
      case 'pending':
        return '#ff9800';
      case 'cancelled':
        return '#f44336';
      case 'checked_in':
        return '#2196f3';
      case 'checked_out':
        return '#9e9e9e';
      default:
        return '#757575';
    }
  };

  const getPaymentStatusColor = (status: string) => {
    switch (status) {
      case 'paid':
        return '#4caf50';
      case 'partial':
        return '#ff9800';
      case 'pending':
        return '#f44336';
      case 'refunded':
        return '#9e9e9e';
      default:
        return '#757575';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'confirmed':
        return <ConfirmedIcon sx={{ color: 'white' }} />;
      case 'pending':
        return <PendingIcon sx={{ color: 'white' }} />;
      case 'cancelled':
        return <CancelledIcon sx={{ color: 'white' }} />;
      case 'checked_in':
        return <CheckInIcon sx={{ color: 'white' }} />;
      case 'checked_out':
        return <CheckOutIcon sx={{ color: 'white' }} />;
      default:
        return <ConfirmedIcon sx={{ color: 'white' }} />;
    }
  };

  const handleOpenDialog = (booking?: any) => {
    if (booking) {
      setEditingBooking(booking);
      setFormData({
        guestName: booking.guestName,
        guestEmail: booking.guestEmail,
        guestPhone: booking.guestPhone,
        hotelId: booking.hotelId,
        roomNumber: booking.roomNumber,
        roomType: booking.roomType,
        checkInDate: format(booking.checkInDate, 'yyyy-MM-dd'),
        checkOutDate: format(booking.checkOutDate, 'yyyy-MM-dd'),
        numberOfGuests: booking.numberOfGuests,
        totalAmount: booking.totalAmount,
        paidAmount: booking.paidAmount,
        status: booking.status,
        paymentStatus: booking.paymentStatus,
        specialRequests: booking.specialRequests || '',
        notes: booking.notes || ''
      });
    } else {
      setEditingBooking(null);
      setFormData({
        guestName: '',
        guestEmail: '',
        guestPhone: '',
        hotelId: selectedHotel !== 'all' ? selectedHotel : '',
        roomNumber: '',
        roomType: '',
        checkInDate: format(new Date(), 'yyyy-MM-dd'),
        checkOutDate: format(addDays(new Date(), 1), 'yyyy-MM-dd'),
        numberOfGuests: 1,
        totalAmount: 0,
        paidAmount: 0,
        status: 'pending',
        paymentStatus: 'pending',
        specialRequests: '',
        notes: ''
      });
    }
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setEditingBooking(null);
  };

  const handleViewBooking = (booking: any) => {
    setViewingBooking(booking);
    setViewDialogOpen(true);
  };

  const handleFormChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSaveBooking = async () => {
    try {
      if (editingBooking) {
        // Update existing booking
        const updateData = {
          guestName: formData.guestName,
          guestEmail: formData.guestEmail,
          guestPhone: formData.guestPhone,
          roomNumber: formData.roomNumber,
          roomType: formData.roomType,
          checkInDate: new Date(formData.checkInDate),
          checkOutDate: new Date(formData.checkOutDate),
          numberOfGuests: formData.numberOfGuests,
          totalAmount: formData.totalAmount,
          paidAmount: formData.paidAmount,
          status: formData.status,
          paymentStatus: formData.paymentStatus,
          specialRequests: formData.specialRequests,
          notes: formData.notes
        };
        await updateBooking(editingBooking.id, updateData);
        handleCloseDialog();
        await fetchBookings();
      } else {
        // Create new booking - check if Aadhaar verification is required
        const bookingData = {
          ...formData,
          bookingNumber: `BK${String(Date.now()).slice(-6)}`,
          bookingDate: new Date(),
          checkInDate: new Date(formData.checkInDate),
          checkOutDate: new Date(formData.checkOutDate),
          hotelId: formData.hotelId || selectedHotel,
          hotelName: 'Hotel Name', // Default hotel name
          roomId: `room-${Date.now()}`, // Generate room ID
          userId: user?.id || '',
          adults: formData.numberOfGuests || 1,
          children: 0, // Default to 0 children
          status: 'pending' as const,
          paymentStatus: 'pending' as const
        };

        // Check if Aadhaar verification is required
        if (isAadhaarVerificationRequired(bookingData)) {
          // Store booking data and open Aadhaar verification
          setPendingBookingData(bookingData);
          setAadhaarVerificationOpen(true);
          return; // Don't close dialog yet
        } else {
          // No Aadhaar verification required, create booking directly
          await createBooking(bookingData);
          handleCloseDialog();
          await fetchBookings();
        }
      }
    } catch (err: any) {
      console.error('Error saving booking:', err);
      setError(err.message);
    }
  };

  const handleDeleteBooking = async (bookingId: string) => {
    if (window.confirm('Are you sure you want to delete this booking?')) {
      try {
        await cancelBooking(bookingId);
        await fetchBookings();
      } catch (err: any) {
        console.error('Error deleting booking:', err);
        setError(err.message);
      }
    }
  };

  // Handle Aadhaar verification completion
  const handleAadhaarVerificationComplete = async (verificationData: any) => {
    try {
      if (pendingBookingData) {
        // Add Aadhaar verification data to booking
        const bookingWithAadhaar = {
          ...pendingBookingData,
          aadhaarVerification: {
            required: true,
            verified: true,
            aadhaarNumber: verificationData.data?.aadhaarNumber,
            verifiedName: verificationData.data?.name,
            verificationDate: new Date(),
            transactionId: verificationData.transactionId
          },
          status: 'confirmed' as const // Booking is confirmed after Aadhaar verification
        };

        await createBooking(bookingWithAadhaar);

        // Reset state
        setPendingBookingData(null);
        setAadhaarVerificationOpen(false);
        handleCloseDialog();
        await fetchBookings();

        // Show success message
        alert('Booking created successfully with Aadhaar verification!');
      }
    } catch (err: any) {
      console.error('Error creating booking with Aadhaar verification:', err);
      setError(err.message);
    }
  };

  // Handle Aadhaar verification cancellation
  const handleAadhaarVerificationCancel = () => {
    setAadhaarVerificationOpen(false);
    setPendingBookingData(null);
    // Keep the booking dialog open so user can try again or modify details
  };

  const handleActionMenuOpen = (event: React.MouseEvent<HTMLElement>, booking: any) => {
    setActionMenuAnchor(event.currentTarget);
    setSelectedBookingForAction(booking);
  };

  const handleActionMenuClose = () => {
    setActionMenuAnchor(null);
    setSelectedBookingForAction(null);
  };

  const handleCheckIn = (booking: any) => {
    console.log('Checking in:', booking.bookingNumber);
    // Update booking status to checked_in
    handleActionMenuClose();
  };

  const handleCheckOut = (booking: any) => {
    console.log('Checking out:', booking.bookingNumber);
    // Update booking status to checked_out
    handleActionMenuClose();
  };

  const handlePrintBooking = (booking: any) => {
    console.log('Printing booking:', booking.bookingNumber);
    // Generate and print booking confirmation
    window.print();
    handleActionMenuClose();
  };

  const handleSendConfirmation = (booking: any) => {
    console.log('Sending confirmation email:', booking.guestEmail);
    // Send confirmation email
    handleActionMenuClose();
  };

  const confirmedBookings = bookings.filter(b => b.status === 'confirmed');
  const pendingBookings = bookings.filter(b => b.status === 'pending');
  const cancelledBookings = bookings.filter(b => b.status === 'cancelled');
  const checkedInBookings = bookings.filter(b => b.status === 'checked_in');

  const totalRevenue = bookings
    .filter(b => b.status !== 'cancelled')
    .reduce((sum, b) => sum + b.totalAmount, 0);

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 3 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1">
          Booking Management
        </Typography>
        <Box display="flex" gap={2} alignItems="center">
          {user?.role === 'super_admin' && (
            <FormControl size="small" sx={{ minWidth: 200 }}>
              <InputLabel>Select Hotel</InputLabel>
              <Select
                value={selectedHotel}
                label="Select Hotel"
                onChange={(e) => setSelectedHotel(e.target.value)}
              >
                <MenuItem value="all">All Hotels</MenuItem>
                {hotels.map((hotel) => (
                  <MenuItem key={hotel.id} value={hotel.id}>
                    {hotel.name}
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          )}
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => handleOpenDialog()}
          >
            New Booking
          </Button>
        </Box>
      </Box>

      {/* Booking Statistics */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Avatar sx={{ bgcolor: '#4caf50', mx: 'auto', mb: 1 }}>
                <ConfirmedIcon />
              </Avatar>
              <Typography variant="h4" color="success.main">
                {confirmedBookings.length}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                Confirmed Bookings
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Avatar sx={{ bgcolor: '#ff9800', mx: 'auto', mb: 1 }}>
                <PendingIcon />
              </Avatar>
              <Typography variant="h4" color="warning.main">
                {pendingBookings.length}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                Pending Bookings
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Avatar sx={{ bgcolor: '#2196f3', mx: 'auto', mb: 1 }}>
                <CheckInIcon />
              </Avatar>
              <Typography variant="h4" color="primary.main">
                {checkedInBookings.length}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                Checked In
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Avatar sx={{ bgcolor: '#9c27b0', mx: 'auto', mb: 1 }}>
                <MoneyIcon />
              </Avatar>
              <Typography variant="h4" color="secondary.main">
                ${totalRevenue.toLocaleString()}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                Total Revenue
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Bookings Table */}
      <Card>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={(_, newValue) => setTabValue(newValue)}>
            <Tab label={`All Bookings (${bookings.length})`} />
            <Tab label={`Confirmed (${confirmedBookings.length})`} />
            <Tab label={`Pending (${pendingBookings.length})`} />
            <Tab label={`Checked In (${checkedInBookings.length})`} />
          </Tabs>
        </Box>

        <TabPanel value={tabValue} index={0}>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Booking #</TableCell>
                  <TableCell>Guest</TableCell>
                  <TableCell>Hotel</TableCell>
                  <TableCell>Room</TableCell>
                  <TableCell>Dates</TableCell>
                  <TableCell>Amount</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Payment</TableCell>
                  <TableCell align="center">Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {bookings.map((booking) => (
                  <TableRow key={booking.id}>
                    <TableCell>
                      <Typography variant="body2" fontWeight="bold">
                        {booking.bookingNumber}
                      </Typography>
                      <Typography variant="caption" color="textSecondary">
                        {format(toDate(booking.bookingDate), 'MMM dd, yyyy')}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Box>
                        <Typography variant="body2" fontWeight="bold">
                          {booking.guestName}
                        </Typography>
                        <Typography variant="caption" color="textSecondary">
                          {booking.guestEmail}
                        </Typography>
                        <br />
                        <Typography variant="caption" color="textSecondary">
                          {booking.guestPhone}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {booking.hotelName}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        Room {booking.roomNumber}
                      </Typography>
                      <Typography variant="caption" color="textSecondary">
                        {booking.roomType} • {booking.numberOfGuests} guests
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {format(toDate(booking.checkInDate), 'MMM dd')} - {format(toDate(booking.checkOutDate), 'MMM dd')}
                      </Typography>
                      <Typography variant="caption" color="textSecondary">
                        {differenceInDays(toDate(booking.checkOutDate), toDate(booking.checkInDate))} nights
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2" fontWeight="bold">
                        ${booking.totalAmount}
                      </Typography>
                      <Typography variant="caption" color="textSecondary">
                        Paid: ${booking.paidAmount}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Chip
                        icon={getStatusIcon(booking.status)}
                        label={booking.status.charAt(0).toUpperCase() + booking.status.slice(1).replace('_', ' ')}
                        size="small"
                        sx={{
                          backgroundColor: getStatusColor(booking.status),
                          color: 'white',
                          '& .MuiChip-icon': {
                            color: 'white'
                          }
                        }}
                      />
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={booking.paymentStatus.charAt(0).toUpperCase() + booking.paymentStatus.slice(1)}
                        size="small"
                        variant="outlined"
                        sx={{
                          borderColor: getPaymentStatusColor(booking.paymentStatus),
                          color: getPaymentStatusColor(booking.paymentStatus)
                        }}
                      />
                    </TableCell>
                    <TableCell align="center">
                      <Tooltip title="View Details">
                        <IconButton size="small" color="primary" onClick={() => handleViewBooking(booking)}>
                          <ViewIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Edit Booking">
                        <IconButton size="small" color="secondary" onClick={() => handleOpenDialog(booking)}>
                          <EditIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="More Actions">
                        <IconButton size="small" onClick={(e) => handleActionMenuOpen(e, booking)}>
                          <MoreIcon />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          <Box p={3}>
            <Typography variant="h6" gutterBottom>
              Confirmed Bookings ({confirmedBookings.length})
            </Typography>
            {/* Similar table structure for confirmed bookings */}
          </Box>
        </TabPanel>

        <TabPanel value={tabValue} index={2}>
          <Box p={3}>
            <Typography variant="h6" gutterBottom>
              Pending Bookings ({pendingBookings.length})
            </Typography>
            {pendingBookings.length === 0 ? (
              <Box textAlign="center" py={4}>
                <PendingIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                <Typography variant="h6" gutterBottom>
                  No pending bookings
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  All bookings have been confirmed or cancelled.
                </Typography>
              </Box>
            ) : (
              <Alert severity="warning" sx={{ mb: 2 }}>
                You have {pendingBookings.length} pending bookings that require attention.
              </Alert>
            )}
          </Box>
        </TabPanel>

        <TabPanel value={tabValue} index={3}>
          <Box p={3}>
            <Typography variant="h6" gutterBottom>
              Checked In Guests ({checkedInBookings.length})
            </Typography>
            {/* Similar table structure for checked in bookings */}
          </Box>
        </TabPanel>
      </Card>

      {/* Action Menu */}
      <Menu
        anchorEl={actionMenuAnchor}
        open={Boolean(actionMenuAnchor)}
        onClose={handleActionMenuClose}
      >
        <MenuItemComponent onClick={() => handleCheckIn(selectedBookingForAction)}>
          <ListItemIcon>
            <CheckInIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Check In</ListItemText>
        </MenuItemComponent>
        <MenuItemComponent onClick={() => handleCheckOut(selectedBookingForAction)}>
          <ListItemIcon>
            <CheckOutIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Check Out</ListItemText>
        </MenuItemComponent>
        <MenuItemComponent onClick={() => handlePrintBooking(selectedBookingForAction)}>
          <ListItemIcon>
            <PrintIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Print Confirmation</ListItemText>
        </MenuItemComponent>
        <MenuItemComponent onClick={() => handleSendConfirmation(selectedBookingForAction)}>
          <ListItemIcon>
            <SendIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Send Confirmation</ListItemText>
        </MenuItemComponent>
        <Divider />
        <MenuItemComponent onClick={() => handleDeleteBooking(selectedBookingForAction?.id)}>
          <ListItemIcon>
            <DeleteIcon fontSize="small" />
          </ListItemIcon>
          <ListItemText>Cancel Booking</ListItemText>
        </MenuItemComponent>
      </Menu>

      {/* Add/Edit Booking Dialog */}
      <Dialog open={dialogOpen} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingBooking ? 'Edit Booking' : 'Create New Booking'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Guest Name"
                value={formData.guestName}
                onChange={(e) => handleFormChange('guestName', e.target.value)}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Guest Email"
                type="email"
                value={formData.guestEmail}
                onChange={(e) => handleFormChange('guestEmail', e.target.value)}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Guest Phone"
                value={formData.guestPhone}
                onChange={(e) => handleFormChange('guestPhone', e.target.value)}
                required
              />
              {/* Aadhaar Verification Indicator */}
              {formData.guestPhone && isAadhaarVerificationRequired({ guestPhone: formData.guestPhone }) && (
                <Alert severity="info" sx={{ mt: 1 }}>
                  <Typography variant="body2">
                    📱 Aadhaar verification will be required for this booking
                  </Typography>
                </Alert>
              )}
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth required>
                <InputLabel>Hotel</InputLabel>
                <Select
                  value={formData.hotelId}
                  label="Hotel"
                  onChange={(e) => handleFormChange('hotelId', e.target.value)}
                >
                  {hotels.map((hotel) => (
                    <MenuItem key={hotel.id} value={hotel.id}>
                      {hotel.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Room Number"
                value={formData.roomNumber}
                onChange={(e) => handleFormChange('roomNumber', e.target.value)}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth required>
                <InputLabel>Room Type</InputLabel>
                <Select
                  value={formData.roomType}
                  label="Room Type"
                  onChange={(e) => handleFormChange('roomType', e.target.value)}
                >
                  <MenuItem value="Standard">Standard</MenuItem>
                  <MenuItem value="Deluxe">Deluxe</MenuItem>
                  <MenuItem value="Suite">Suite</MenuItem>
                  <MenuItem value="Presidential">Presidential</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Check-in Date"
                type="date"
                value={formData.checkInDate}
                onChange={(e) => handleFormChange('checkInDate', e.target.value)}
                InputLabelProps={{ shrink: true }}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Check-out Date"
                type="date"
                value={formData.checkOutDate}
                onChange={(e) => handleFormChange('checkOutDate', e.target.value)}
                InputLabelProps={{ shrink: true }}
                required
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                label="Number of Guests"
                type="number"
                value={formData.numberOfGuests}
                onChange={(e) => handleFormChange('numberOfGuests', parseInt(e.target.value) || 1)}
                inputProps={{ min: 1, max: 10 }}
                required
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                label="Total Amount"
                type="number"
                value={formData.totalAmount}
                onChange={(e) => handleFormChange('totalAmount', parseFloat(e.target.value) || 0)}
                inputProps={{ min: 0, step: 0.01 }}
                required
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                label="Paid Amount"
                type="number"
                value={formData.paidAmount}
                onChange={(e) => handleFormChange('paidAmount', parseFloat(e.target.value) || 0)}
                inputProps={{ min: 0, step: 0.01 }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Booking Status</InputLabel>
                <Select
                  value={formData.status}
                  label="Booking Status"
                  onChange={(e) => handleFormChange('status', e.target.value)}
                >
                  <MenuItem value="pending">Pending</MenuItem>
                  <MenuItem value="confirmed">Confirmed</MenuItem>
                  <MenuItem value="checked_in">Checked In</MenuItem>
                  <MenuItem value="checked_out">Checked Out</MenuItem>
                  <MenuItem value="cancelled">Cancelled</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Payment Status</InputLabel>
                <Select
                  value={formData.paymentStatus}
                  label="Payment Status"
                  onChange={(e) => handleFormChange('paymentStatus', e.target.value)}
                >
                  <MenuItem value="pending">Pending</MenuItem>
                  <MenuItem value="partial">Partial</MenuItem>
                  <MenuItem value="paid">Paid</MenuItem>
                  <MenuItem value="refunded">Refunded</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Special Requests"
                multiline
                rows={2}
                value={formData.specialRequests}
                onChange={(e) => handleFormChange('specialRequests', e.target.value)}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Internal Notes"
                multiline
                rows={2}
                value={formData.notes}
                onChange={(e) => handleFormChange('notes', e.target.value)}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog} startIcon={<CancelIcon />}>
            Cancel
          </Button>
          <Button onClick={handleSaveBooking} variant="contained" startIcon={<SaveIcon />}>
            {editingBooking ? 'Update Booking' : 'Create Booking'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* View Booking Dialog */}
      <Dialog open={viewDialogOpen} onClose={() => setViewDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          Booking Details: {viewingBooking?.bookingNumber}
        </DialogTitle>
        <DialogContent>
          {viewingBooking && (
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Typography variant="h6" gutterBottom>
                  Guest Information
                </Typography>
                <List>
                  <ListItem>
                    <ListItemIcon>
                      <GuestIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Guest Name"
                      secondary={viewingBooking.guestName}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      <EmailIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Email"
                      secondary={viewingBooking.guestEmail}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      <PhoneIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Phone"
                      secondary={viewingBooking.guestPhone}
                    />
                  </ListItem>

                  {/* Aadhaar Verification Status */}
                  {viewingBooking && (() => {
                    const aadhaarStatus = getAadhaarVerificationStatus(viewingBooking);
                    if (aadhaarStatus.required) {
                      return (
                        <ListItem>
                          <ListItemIcon>
                            <SecurityIcon color={aadhaarStatus.completed ? 'success' : 'warning'} />
                          </ListItemIcon>
                          <ListItemText
                            primary="Aadhaar Verification"
                            secondary={
                              aadhaarStatus.completed
                                ? `✅ Verified - ${aadhaarStatus.verificationData?.verifiedName || 'Name verified'}`
                                : '⚠️ Verification Required'
                            }
                          />
                        </ListItem>
                      );
                    }
                    return null;
                  })()}
                </List>
              </Grid>

              <Grid item xs={12} md={6}>
                <Typography variant="h6" gutterBottom>
                  Booking Information
                </Typography>
                <List>
                  <ListItem>
                    <ListItemIcon>
                      <RoomIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Hotel & Room"
                      secondary={`${viewingBooking.hotelName} - Room ${viewingBooking.roomNumber} (${viewingBooking.roomType})`}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      <DateIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Stay Duration"
                      secondary={`${format(toDate(viewingBooking.checkInDate), 'MMM dd, yyyy')} - ${format(toDate(viewingBooking.checkOutDate), 'MMM dd, yyyy')} (${differenceInDays(toDate(viewingBooking.checkOutDate), toDate(viewingBooking.checkInDate))} nights)`}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      <GuestIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Guests"
                      secondary={`${viewingBooking.numberOfGuests} guest(s)`}
                    />
                  </ListItem>
                </List>
              </Grid>

              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom>
                  Payment & Status
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={6} sm={3}>
                    <Card>
                      <CardContent sx={{ textAlign: 'center' }}>
                        <Typography variant="h5" color="primary">
                          ${viewingBooking.totalAmount}
                        </Typography>
                        <Typography variant="body2">Total Amount</Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                  <Grid item xs={6} sm={3}>
                    <Card>
                      <CardContent sx={{ textAlign: 'center' }}>
                        <Typography variant="h5" color="success.main">
                          ${viewingBooking.paidAmount}
                        </Typography>
                        <Typography variant="body2">Paid Amount</Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                  <Grid item xs={6} sm={3}>
                    <Card>
                      <CardContent sx={{ textAlign: 'center' }}>
                        <Chip
                          label={viewingBooking.status.replace('_', ' ').toUpperCase()}
                          color={viewingBooking.status === 'confirmed' ? 'success' : 'warning'}
                        />
                        <Typography variant="body2" sx={{ mt: 1 }}>Booking Status</Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                  <Grid item xs={6} sm={3}>
                    <Card>
                      <CardContent sx={{ textAlign: 'center' }}>
                        <Chip
                          label={viewingBooking.paymentStatus.toUpperCase()}
                          color={viewingBooking.paymentStatus === 'paid' ? 'success' : 'warning'}
                        />
                        <Typography variant="body2" sx={{ mt: 1 }}>Payment Status</Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                </Grid>
              </Grid>

              {viewingBooking.specialRequests && (
                <Grid item xs={12}>
                  <Typography variant="h6" gutterBottom>
                    Special Requests
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    {viewingBooking.specialRequests}
                  </Typography>
                </Grid>
              )}

              {viewingBooking.notes && (
                <Grid item xs={12}>
                  <Typography variant="h6" gutterBottom>
                    Internal Notes
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    {viewingBooking.notes}
                  </Typography>
                </Grid>
              )}
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => handlePrintBooking(viewingBooking)} startIcon={<PrintIcon />}>
            Print
          </Button>
          <Button onClick={() => handleSendConfirmation(viewingBooking)} startIcon={<SendIcon />}>
            Send Confirmation
          </Button>
          <Button onClick={() => setViewDialogOpen(false)}>
            Close
          </Button>
        </DialogActions>
      </Dialog>

      {/* Aadhaar Verification Dialog */}
      <AadhaarVerification
        open={aadhaarVerificationOpen}
        onClose={handleAadhaarVerificationCancel}
        onVerificationComplete={handleAadhaarVerificationComplete}
        guestName={pendingBookingData?.guestName}
        guestPhone={pendingBookingData?.guestPhone}
      />
    </Box>
  );
};

export default BookingManagement;
