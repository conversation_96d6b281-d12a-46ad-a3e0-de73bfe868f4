import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Badge,
  Chip,
  Button,
  Divider,
  Menu,
  MenuItem,
  Alert
} from '@mui/material';
import {
  Notifications as NotificationIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  Error as ErrorIcon,
  CheckCircle as SuccessIcon,
  MoreVert as MoreIcon,
  MarkAsUnread as UnreadIcon,
  Delete as DeleteIcon,
  Clear as ClearIcon
} from '@mui/icons-material';
import { format } from 'date-fns';

interface Notification {
  id: string;
  type: 'info' | 'warning' | 'error' | 'success';
  title: string;
  message: string;
  timestamp: Date;
  isRead: boolean;
  category: 'booking' | 'maintenance' | 'staff' | 'system' | 'guest';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  actionUrl?: string;
}

interface NotificationsPanelProps {
  notifications: Notification[];
  onMarkAsRead: (id: string) => void;
  onDelete: (id: string) => void;
  onClearAll: () => void;
}

const NotificationsPanel: React.FC<NotificationsPanelProps> = ({
  notifications,
  onMarkAsRead,
  onDelete,
  onClearAll
}) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [selectedNotification, setSelectedNotification] = useState<string | null>(null);

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>, notificationId: string) => {
    setAnchorEl(event.currentTarget);
    setSelectedNotification(notificationId);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
    setSelectedNotification(null);
  };

  const getIcon = (type: string) => {
    switch (type) {
      case 'warning':
        return <WarningIcon sx={{ color: '#ff9800' }} />;
      case 'error':
        return <ErrorIcon sx={{ color: '#f44336' }} />;
      case 'success':
        return <SuccessIcon sx={{ color: '#4caf50' }} />;
      default:
        return <InfoIcon sx={{ color: '#2196f3' }} />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent':
        return '#f44336';
      case 'high':
        return '#ff9800';
      case 'medium':
        return '#2196f3';
      default:
        return '#9e9e9e';
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'booking':
        return '#1976d2';
      case 'maintenance':
        return '#d32f2f';
      case 'staff':
        return '#7b1fa2';
      case 'guest':
        return '#388e3c';
      default:
        return '#757575';
    }
  };

  const unreadCount = notifications.filter(n => !n.isRead).length;

  return (
    <Card>
      <CardContent>
        <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
          <Box display="flex" alignItems="center">
            <Badge badgeContent={unreadCount} color="error">
              <NotificationIcon />
            </Badge>
            <Typography variant="h6" sx={{ ml: 1 }}>
              Notifications
            </Typography>
          </Box>
          {notifications.length > 0 && (
            <Button
              size="small"
              startIcon={<ClearIcon />}
              onClick={onClearAll}
              color="error"
            >
              Clear All
            </Button>
          )}
        </Box>

        {notifications.length === 0 ? (
          <Alert severity="info">
            No notifications at this time.
          </Alert>
        ) : (
          <List sx={{ maxHeight: '400px', overflow: 'auto' }}>
            {notifications.map((notification, index) => (
              <React.Fragment key={notification.id}>
                <ListItem
                  sx={{
                    backgroundColor: notification.isRead ? 'transparent' : 'action.hover',
                    borderRadius: 1,
                    mb: 1
                  }}
                >
                  <ListItemIcon>
                    {getIcon(notification.type)}
                  </ListItemIcon>
                  <ListItemText
                    primary={
                      <Box display="flex" alignItems="center" gap={1}>
                        <Typography
                          variant="subtitle2"
                          sx={{
                            fontWeight: notification.isRead ? 'normal' : 'bold'
                          }}
                        >
                          {notification.title}
                        </Typography>
                        <Chip
                          label={notification.category}
                          size="small"
                          sx={{
                            backgroundColor: getCategoryColor(notification.category),
                            color: 'white',
                            fontSize: '0.7rem',
                            height: '20px'
                          }}
                        />
                        <Chip
                          label={notification.priority}
                          size="small"
                          variant="outlined"
                          sx={{
                            borderColor: getPriorityColor(notification.priority),
                            color: getPriorityColor(notification.priority),
                            fontSize: '0.7rem',
                            height: '20px'
                          }}
                        />
                      </Box>
                    }
                    secondary={
                      <Box>
                        <Typography variant="body2" color="textSecondary">
                          {notification.message}
                        </Typography>
                        <Typography variant="caption" color="textSecondary">
                          {format(notification.timestamp, 'MMM dd, yyyy HH:mm')}
                        </Typography>
                      </Box>
                    }
                  />
                  <ListItemSecondaryAction>
                    <IconButton
                      edge="end"
                      onClick={(e) => handleMenuOpen(e, notification.id)}
                    >
                      <MoreIcon />
                    </IconButton>
                  </ListItemSecondaryAction>
                </ListItem>
                {index < notifications.length - 1 && <Divider />}
              </React.Fragment>
            ))}
          </List>
        )}

        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleMenuClose}
        >
          <MenuItem
            onClick={() => {
              if (selectedNotification) {
                onMarkAsRead(selectedNotification);
              }
              handleMenuClose();
            }}
          >
            <ListItemIcon>
              <UnreadIcon fontSize="small" />
            </ListItemIcon>
            Mark as Read
          </MenuItem>
          <MenuItem
            onClick={() => {
              if (selectedNotification) {
                onDelete(selectedNotification);
              }
              handleMenuClose();
            }}
          >
            <ListItemIcon>
              <DeleteIcon fontSize="small" />
            </ListItemIcon>
            Delete
          </MenuItem>
        </Menu>
      </CardContent>
    </Card>
  );
};

// Sample notifications for demo
export const sampleNotifications: Notification[] = [
  {
    id: '1',
    type: 'warning',
    title: 'Low Inventory Alert',
    message: 'Towels are running low in housekeeping inventory',
    timestamp: new Date(Date.now() - 10 * 60 * 1000), // 10 minutes ago
    isRead: false,
    category: 'maintenance',
    priority: 'medium'
  },
  {
    id: '2',
    type: 'info',
    title: 'New Booking Confirmation',
    message: 'Booking #12345 requires confirmation within 24 hours',
    timestamp: new Date(Date.now() - 25 * 60 * 1000), // 25 minutes ago
    isRead: false,
    category: 'booking',
    priority: 'high'
  },
  {
    id: '3',
    type: 'success',
    title: 'Maintenance Completed',
    message: 'AC repair in Room 205 has been completed successfully',
    timestamp: new Date(Date.now() - 60 * 60 * 1000), // 1 hour ago
    isRead: true,
    category: 'maintenance',
    priority: 'low'
  },
  {
    id: '4',
    type: 'error',
    title: 'Payment Failed',
    message: 'Payment processing failed for booking #12340',
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
    isRead: false,
    category: 'booking',
    priority: 'urgent'
  },
  {
    id: '5',
    type: 'info',
    title: 'Staff Schedule Update',
    message: 'John Doe has requested a shift change for tomorrow',
    timestamp: new Date(Date.now() - 3 * 60 * 60 * 1000), // 3 hours ago
    isRead: true,
    category: 'staff',
    priority: 'medium'
  }
];

export default NotificationsPanel;
