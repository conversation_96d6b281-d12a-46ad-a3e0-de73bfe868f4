const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('Starting calendar fix script...');

// Check if package.json exists
const packageJsonPath = path.join(__dirname, 'package.json');
if (!fs.existsSync(packageJsonPath)) {
  console.error('Error: package.json not found!');
  process.exit(1);
}

// Read package.json
let packageJson;
try {
  packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
} catch (error) {
  console.error('Error reading package.json:', error);
  process.exit(1);
}

// Check if FullCalendar dependencies are already installed
const fullCalendarDeps = [
  '@fullcalendar/core',
  '@fullcalendar/daygrid',
  '@fullcalendar/interaction',
  '@fullcalendar/react',
  '@fullcalendar/timegrid'
];

const missingDeps = fullCalendarDeps.filter(dep => !packageJson.dependencies[dep]);

if (missingDeps.length === 0) {
  console.log('All FullCalendar dependencies are already installed!');
} else {
  console.log(`Missing dependencies: ${missingDeps.join(', ')}`);
  console.log('Installing missing dependencies...');
  
  try {
    // Install missing dependencies
    execSync(`npm install ${missingDeps.join(' ')}`, { stdio: 'inherit' });
    console.log('Dependencies installed successfully!');
  } catch (error) {
    console.error('Error installing dependencies:', error);
    process.exit(1);
  }
}

// Check if the StaffCalendar.tsx file exists
const staffCalendarPath = path.join(__dirname, 'src', 'components', 'staff', 'StaffCalendar.tsx');
if (!fs.existsSync(staffCalendarPath)) {
  console.error('Error: StaffCalendar.tsx not found!');
  process.exit(1);
}

// Read StaffCalendar.tsx
let staffCalendarContent;
try {
  staffCalendarContent = fs.readFileSync(staffCalendarPath, 'utf8');
} catch (error) {
  console.error('Error reading StaffCalendar.tsx:', error);
  process.exit(1);
}

// Check if the file is using the simplified version
if (staffCalendarContent.includes('The calendar view requires FullCalendar dependencies to be installed')) {
  console.log('StaffCalendar.tsx is using the simplified version. Updating to use FullCalendar...');
  
  // Create a backup of the current file
  const backupPath = `${staffCalendarPath}.backup`;
  fs.writeFileSync(backupPath, staffCalendarContent);
  console.log(`Backup created at ${backupPath}`);
  
  // Update the file with the FullCalendar implementation
  const fullCalendarImplementation = `import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Grid,
  CircularProgress,
  Alert,
  Chip
} from '@mui/material';
import {
  Add as AddIcon,
  Refresh as RefreshIcon,
  FilterList as FilterIcon,
  Person as PersonIcon,
  Event as EventIcon
} from '@mui/icons-material';
import FullCalendar from '@fullcalendar/react';
import dayGridPlugin from '@fullcalendar/daygrid';
import timeGridPlugin from '@fullcalendar/timegrid';
import interactionPlugin from '@fullcalendar/interaction';
import { EventInput, DateSelectArg, EventClickArg } from '@fullcalendar/core';
import { format, parseISO, setHours, setMinutes } from 'date-fns';
import { Timestamp } from 'firebase/firestore';

// Define shift types
const SHIFT_TYPE_MORNING = 'morning';
const SHIFT_TYPE_AFTERNOON = 'afternoon';
const SHIFT_TYPE_NIGHT = 'night';
const SHIFT_TYPE_CUSTOM = 'custom';

// Define shift times
const shiftTimes = {
  [SHIFT_TYPE_MORNING]: { start: '06:00', end: '14:00' },
  [SHIFT_TYPE_AFTERNOON]: { start: '14:00', end: '22:00' },
  [SHIFT_TYPE_NIGHT]: { start: '22:00', end: '06:00' }
};

interface StaffCalendarProps {
  hotelId: string;
  vendorId: string;
  staffList: any[];
  shifts: any[];
  timeOffRequests: any[];
  loading: boolean;
  error: string | null;
  onRefresh: () => void;
  onShiftCreate: (shift: any) => Promise<void>;
  onShiftUpdate: (shiftId: string, updates: any) => Promise<void>;
  onShiftDelete: (shiftId: string) => Promise<void>;
}

const StaffCalendar: React.FC<StaffCalendarProps> = ({
  hotelId,
  vendorId,
  staffList,
  shifts,
  timeOffRequests,
  loading,
  error,
  onRefresh,
  onShiftCreate,
  onShiftUpdate,
  onShiftDelete
}) => {
  const calendarRef = useRef<FullCalendar>(null);
  const [events, setEvents] = useState<EventInput[]>([]);
  const [selectedStaff, setSelectedStaff] = useState<string>('all');
  const [shiftDialogOpen, setShiftDialogOpen] = useState<boolean>(false);
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [selectedEvent, setSelectedEvent] = useState<EventInput | null>(null);
  const [shiftFormData, setShiftFormData] = useState<{
    staffId: string;
    shiftType: string;
    startTime: string;
    endTime: string;
    notes: string;
  }>({
    staffId: '',
    shiftType: SHIFT_TYPE_MORNING,
    startTime: '',
    endTime: '',
    notes: ''
  });

  // Convert shifts and time off requests to calendar events
  useEffect(() => {
    const calendarEvents: EventInput[] = [];
    
    // Add shifts to calendar events
    shifts.forEach(shift => {
      // Skip if filtering by staff and this shift is for a different staff member
      if (selectedStaff !== 'all' && shift.staffId !== selectedStaff) {
        return;
      }
      
      try {
        const startDate = shift.startTime.toDate();
        const endDate = shift.endTime.toDate();
        
        calendarEvents.push({
          id: \`shift_\${shift.id}\`,
          title: \`\${shift.staffName} - \${shift.shiftType}\`,
          start: startDate,
          end: endDate,
          allDay: false,
          extendedProps: {
            type: 'shift',
            shift: shift
          },
          backgroundColor: getShiftColor(shift.shiftType),
          borderColor: getShiftColor(shift.shiftType)
        });
      } catch (error) {
        console.error('Error processing shift:', error);
      }
    });
    
    // Add approved time off requests to calendar events
    timeOffRequests.forEach(timeOff => {
      // Skip if not approved
      if (timeOff.status !== 'approved') {
        return;
      }
      
      // Skip if filtering by staff and this time off is for a different staff member
      if (selectedStaff !== 'all' && timeOff.staffId !== selectedStaff) {
        return;
      }
      
      try {
        const startDate = timeOff.startDate.toDate();
        const endDate = timeOff.endDate.toDate();
        
        // Add one day to end date for proper display in calendar
        const displayEndDate = new Date(endDate);
        displayEndDate.setDate(displayEndDate.getDate() + 1);
        
        calendarEvents.push({
          id: \`timeoff_\${timeOff.id}\`,
          title: \`\${timeOff.staffName} - Time Off (\${timeOff.type})\`,
          start: startDate,
          end: displayEndDate,
          allDay: true,
          extendedProps: {
            type: 'timeoff',
            timeOff: timeOff
          },
          backgroundColor: '#f44336', // Red
          borderColor: '#d32f2f',
          textColor: '#ffffff'
        });
      } catch (error) {
        console.error('Error processing time off request:', error);
      }
    });
    
    setEvents(calendarEvents);
  }, [shifts, timeOffRequests, selectedStaff]);

  const handleDateSelect = (selectInfo: DateSelectArg) => {
    if (selectInfo.view.type === 'timeGridDay' || selectInfo.view.type === 'timeGridWeek') {
      // Open shift dialog for the selected date and time
      setSelectedDate(selectInfo.start);
      setSelectedEvent(null);
      
      // Set default form data
      setShiftFormData({
        staffId: selectedStaff !== 'all' ? selectedStaff : (staffList.length > 0 ? staffList[0].id || '' : ''),
        shiftType: SHIFT_TYPE_CUSTOM,
        startTime: format(selectInfo.start, 'HH:mm'),
        endTime: format(selectInfo.end, 'HH:mm'),
        notes: ''
      });
      
      setShiftDialogOpen(true);
    }
  };

  const handleEventClick = (clickInfo: EventClickArg) => {
    const eventType = clickInfo.event.extendedProps.type;
    
    if (eventType === 'shift') {
      // Open shift dialog for editing
      const shift = clickInfo.event.extendedProps.shift;
      setSelectedEvent(clickInfo.event);
      
      try {
        const shiftDate = shift.date.toDate();
        setSelectedDate(shiftDate);
        
        setShiftFormData({
          staffId: shift.staffId,
          shiftType: shift.shiftType,
          startTime: format(shift.startTime.toDate(), 'HH:mm'),
          endTime: format(shift.endTime.toDate(), 'HH:mm'),
          notes: shift.notes || ''
        });
        
        setShiftDialogOpen(true);
      } catch (error) {
        console.error('Error handling event click:', error);
      }
    }
  };

  const handleCloseShiftDialog = () => {
    setShiftDialogOpen(false);
    setSelectedDate(null);
    setSelectedEvent(null);
  };

  const handleShiftFormChange = (event: React.ChangeEvent<HTMLInputElement | { name?: string; value: unknown }>) => {
    const { name, value } = event.target;
    
    if (name) {
      setShiftFormData(prev => ({
        ...prev,
        [name]: value
      }));
      
      // Update shift times when shift type changes
      if (name === 'shiftType' && value !== SHIFT_TYPE_CUSTOM) {
        const shiftType = value as string;
        setShiftFormData(prev => ({
          ...prev,
          startTime: shiftTimes[shiftType].start,
          endTime: shiftTimes[shiftType].end
        }));
      }
    }
  };

  const handleSubmitShift = async () => {
    if (!selectedDate || !shiftFormData.staffId) return;
    
    try {
      const staffMember = staffList.find(staff => staff.id === shiftFormData.staffId);
      if (!staffMember) return;
      
      // Create date objects for start and end times
      const dateStr = format(selectedDate, 'yyyy-MM-dd');
      const startTimeDate = parseISO(\`\${dateStr}T\${shiftFormData.startTime}\`);
      const endTimeDate = parseISO(\`\${dateStr}T\${shiftFormData.endTime}\`);
      
      // Handle overnight shifts
      let adjustedEndTimeDate = endTimeDate;
      if (endTimeDate <= startTimeDate) {
        adjustedEndTimeDate = new Date(endTimeDate);
        adjustedEndTimeDate.setDate(adjustedEndTimeDate.getDate() + 1);
      }
      
      if (selectedEvent) {
        // Update existing shift
        const shift = selectedEvent.extendedProps.shift;
        
        await onShiftUpdate(shift.id, {
          staffId: shiftFormData.staffId,
          staffName: staffMember.name,
          shiftType: shiftFormData.shiftType,
          startTime: Timestamp.fromDate(startTimeDate),
          endTime: Timestamp.fromDate(adjustedEndTimeDate),
          notes: shiftFormData.notes
        });
      } else {
        // Create new shift
        await onShiftCreate({
          staffId: shiftFormData.staffId,
          staffName: staffMember.name,
          hotelId,
          vendorId,
          date: Timestamp.fromDate(selectedDate),
          role: staffMember.role,
          shiftType: shiftFormData.shiftType,
          startTime: Timestamp.fromDate(startTimeDate),
          endTime: Timestamp.fromDate(adjustedEndTimeDate),
          notes: shiftFormData.notes
        });
      }
      
      handleCloseShiftDialog();
    } catch (err) {
      console.error('Error saving shift:', err);
    }
  };

  const handleDeleteShift = async () => {
    if (!selectedEvent) return;
    
    try {
      const shift = selectedEvent.extendedProps.shift;
      await onShiftDelete(shift.id);
      handleCloseShiftDialog();
    } catch (err) {
      console.error('Error deleting shift:', err);
    }
  };

  const getShiftColor = (shiftType: string): string => {
    switch (shiftType) {
      case SHIFT_TYPE_MORNING:
        return '#2196f3'; // Blue
      case SHIFT_TYPE_AFTERNOON:
        return '#ff9800'; // Orange
      case SHIFT_TYPE_NIGHT:
        return '#673ab7'; // Deep Purple
      case SHIFT_TYPE_CUSTOM:
        return '#4caf50'; // Green
      default:
        return '#9e9e9e'; // Grey
    }
  };

  const handleStaffFilterChange = (event: React.ChangeEvent<{ value: unknown }>) => {
    setSelectedStaff(event.target.value as string);
  };

  return (
    <Box>
      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <FormControl sx={{ minWidth: 200 }}>
          <InputLabel id="staff-filter-label">Staff Member</InputLabel>
          <Select
            labelId="staff-filter-label"
            value={selectedStaff}
            label="Staff Member"
            onChange={handleStaffFilterChange as any}
            startAdornment={
              <PersonIcon sx={{ mr: 1, color: 'action.active' }} />
            }
          >
            <MenuItem value="all">All Staff</MenuItem>
            {staffList.map((staff) => (
              <MenuItem key={staff.id} value={staff.id || ''}>
                {staff.name}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
        
        <Button
          variant="outlined"
          startIcon={<RefreshIcon />}
          onClick={onRefresh}
          disabled={loading}
        >
          Refresh
        </Button>
      </Box>
      
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}
      
      <Paper sx={{ p: 2 }}>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 5 }}>
            <CircularProgress />
          </Box>
        ) : (
          <Box sx={{ height: 'calc(100vh - 250px)', minHeight: 600 }}>
            <FullCalendar
              ref={calendarRef}
              plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin]}
              initialView="timeGridWeek"
              headerToolbar={{
                left: 'prev,next today',
                center: 'title',
                right: 'dayGridMonth,timeGridWeek,timeGridDay'
              }}
              events={events}
              selectable={true}
              selectMirror={true}
              dayMaxEvents={true}
              weekends={true}
              select={handleDateSelect}
              eventClick={handleEventClick}
              height="100%"
              allDaySlot={true}
              slotDuration="00:30:00"
              slotLabelInterval="01:00:00"
              slotLabelFormat={{
                hour: 'numeric',
                minute: '2-digit',
                hour12: false
              }}
              nowIndicator={true}
            />
          </Box>
        )}
      </Paper>
      
      {/* Shift Dialog */}
      <Dialog open={shiftDialogOpen} onClose={handleCloseShiftDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {selectedEvent ? 'Edit Shift' : 'Add Shift'} - {selectedDate && format(selectedDate, 'EEEE, MMMM d, yyyy')}
        </DialogTitle>
        <DialogContent dividers>
          <FormControl fullWidth margin="normal">
            <InputLabel id="staff-label">Staff Member</InputLabel>
            <Select
              labelId="staff-label"
              name="staffId"
              value={shiftFormData.staffId}
              label="Staff Member"
              onChange={handleShiftFormChange}
            >
              {staffList.map((staff) => (
                <MenuItem key={staff.id} value={staff.id || ''}>
                  {staff.name} ({staff.role?.replace(/_/g, ' ') || 'Staff'})
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          
          <FormControl fullWidth margin="normal">
            <InputLabel id="shift-type-label">Shift Type</InputLabel>
            <Select
              labelId="shift-type-label"
              name="shiftType"
              value={shiftFormData.shiftType}
              label="Shift Type"
              onChange={handleShiftFormChange}
            >
              <MenuItem value={SHIFT_TYPE_MORNING}>Morning (6:00 - 14:00)</MenuItem>
              <MenuItem value={SHIFT_TYPE_AFTERNOON}>Afternoon (14:00 - 22:00)</MenuItem>
              <MenuItem value={SHIFT_TYPE_NIGHT}>Night (22:00 - 6:00)</MenuItem>
              <MenuItem value={SHIFT_TYPE_CUSTOM}>Custom</MenuItem>
            </Select>
          </FormControl>
          
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="Start Time"
                name="startTime"
                type="time"
                value={shiftFormData.startTime}
                onChange={handleShiftFormChange}
                InputLabelProps={{ shrink: true }}
                inputProps={{ step: 300 }}
                disabled={shiftFormData.shiftType !== SHIFT_TYPE_CUSTOM}
              />
            </Grid>
            <Grid item xs={6}>
              <TextField
                fullWidth
                label="End Time"
                name="endTime"
                type="time"
                value={shiftFormData.endTime}
                onChange={handleShiftFormChange}
                InputLabelProps={{ shrink: true }}
                inputProps={{ step: 300 }}
                disabled={shiftFormData.shiftType !== SHIFT_TYPE_CUSTOM}
              />
            </Grid>
          </Grid>
          
          <TextField
            fullWidth
            label="Notes"
            name="notes"
            value={shiftFormData.notes}
            onChange={handleShiftFormChange}
            margin="normal"
            multiline
            rows={3}
          />
        </DialogContent>
        <DialogActions>
          {selectedEvent && (
            <Button 
              onClick={handleDeleteShift} 
              color="error"
              sx={{ mr: 'auto' }}
            >
              Delete
            </Button>
          )}
          <Button onClick={handleCloseShiftDialog}>Cancel</Button>
          <Button 
            onClick={handleSubmitShift} 
            variant="contained"
            disabled={!shiftFormData.staffId}
          >
            {selectedEvent ? 'Save Changes' : 'Add Shift'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default StaffCalendar;`;
  
  try {
    fs.writeFileSync(staffCalendarPath, fullCalendarImplementation);
    console.log('StaffCalendar.tsx updated successfully!');
  } catch (error) {
    console.error('Error updating StaffCalendar.tsx:', error);
    process.exit(1);
  }
} else {
  console.log('StaffCalendar.tsx is already using FullCalendar.');
}

console.log('Calendar fix script completed successfully!');
