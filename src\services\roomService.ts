import { 
  collection, 
  doc, 
  getDocs, 
  getDoc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  query, 
  where, 
  orderBy,
  Timestamp 
} from 'firebase/firestore';
import { db } from '../firebase/config';
import { Room, RoomStatus, RoomType } from '../types';

/**
 * Get all rooms for a specific hotel
 */
export const getRoomsByHotel = async (hotelId: string): Promise<Room[]> => {
  try {
    const roomsRef = collection(db, 'rooms');
    const q = query(
      roomsRef, 
      where('hotelId', '==', hotelId),
      where('isActive', '==', true),
      orderBy('roomNumber', 'asc')
    );
    
    const querySnapshot = await getDocs(q);
    const rooms: Room[] = [];
    
    querySnapshot.forEach((doc) => {
      rooms.push({
        id: doc.id,
        ...doc.data()
      } as Room);
    });
    
    return rooms;
  } catch (error) {
    console.error('Error getting rooms by hotel:', error);
    throw error;
  }
};

/**
 * Get a specific room by ID
 */
export const getRoomById = async (roomId: string): Promise<Room | null> => {
  try {
    const roomDoc = await getDoc(doc(db, 'rooms', roomId));
    
    if (roomDoc.exists()) {
      return {
        id: roomDoc.id,
        ...roomDoc.data()
      } as Room;
    }
    
    return null;
  } catch (error) {
    console.error('Error getting room by ID:', error);
    throw error;
  }
};

/**
 * Create a new room
 */
export const createRoom = async (roomData: Omit<Room, 'id' | 'createdAt' | 'updatedAt'>): Promise<string> => {
  try {
    const now = Timestamp.now();
    const docRef = await addDoc(collection(db, 'rooms'), {
      ...roomData,
      createdAt: now,
      updatedAt: now
    });
    
    return docRef.id;
  } catch (error) {
    console.error('Error creating room:', error);
    throw error;
  }
};

/**
 * Update an existing room
 */
export const updateRoom = async (roomId: string, roomData: Partial<Room>): Promise<void> => {
  try {
    const roomRef = doc(db, 'rooms', roomId);
    await updateDoc(roomRef, {
      ...roomData,
      updatedAt: Timestamp.now()
    });
  } catch (error) {
    console.error('Error updating room:', error);
    throw error;
  }
};

/**
 * Delete a room (soft delete by setting isActive to false)
 */
export const deleteRoom = async (roomId: string): Promise<void> => {
  try {
    const roomRef = doc(db, 'rooms', roomId);
    await updateDoc(roomRef, {
      isActive: false,
      updatedAt: Timestamp.now()
    });
  } catch (error) {
    console.error('Error deleting room:', error);
    throw error;
  }
};

/**
 * Update room status
 */
export const updateRoomStatus = async (roomId: string, status: RoomStatus): Promise<void> => {
  try {
    const roomRef = doc(db, 'rooms', roomId);
    await updateDoc(roomRef, {
      status,
      updatedAt: Timestamp.now()
    });
  } catch (error) {
    console.error('Error updating room status:', error);
    throw error;
  }
};

/**
 * Get available rooms for a hotel within a date range
 */
export const getAvailableRooms = async (
  hotelId: string, 
  checkInDate: Date, 
  checkOutDate: Date
): Promise<Room[]> => {
  try {
    // First get all rooms for the hotel
    const allRooms = await getRoomsByHotel(hotelId);
    
    // Filter out rooms that are not available or in maintenance
    const availableRooms = allRooms.filter(room => 
      room.status === 'available'
    );
    
    // TODO: Add logic to check against bookings for the date range
    // This would require querying the bookings collection to see which rooms
    // are booked during the specified period
    
    return availableRooms;
  } catch (error) {
    console.error('Error getting available rooms:', error);
    throw error;
  }
};

/**
 * Get room statistics for a hotel
 */
export const getRoomStats = async (hotelId: string) => {
  try {
    const rooms = await getRoomsByHotel(hotelId);
    
    const stats = {
      totalRooms: rooms.length,
      availableRooms: rooms.filter(r => r.status === 'available').length,
      occupiedRooms: rooms.filter(r => r.status === 'occupied').length,
      maintenanceRooms: rooms.filter(r => r.status === 'maintenance').length,
      averagePrice: 0,
      roomTypes: {} as Record<RoomType, number>
    };
    
    // Calculate average price
    if (rooms.length > 0) {
      stats.averagePrice = rooms.reduce((sum, room) => sum + room.price, 0) / rooms.length;
    }
    
    // Count room types
    rooms.forEach(room => {
      stats.roomTypes[room.type] = (stats.roomTypes[room.type] || 0) + 1;
    });
    
    return stats;
  } catch (error) {
    console.error('Error getting room stats:', error);
    throw error;
  }
};

/**
 * Search rooms by criteria
 */
export const searchRooms = async (
  hotelId: string,
  criteria: {
    type?: RoomType;
    status?: RoomStatus;
    minPrice?: number;
    maxPrice?: number;
    capacity?: number;
  }
): Promise<Room[]> => {
  try {
    let rooms = await getRoomsByHotel(hotelId);
    
    // Apply filters
    if (criteria.type) {
      rooms = rooms.filter(room => room.type === criteria.type);
    }
    
    if (criteria.status) {
      rooms = rooms.filter(room => room.status === criteria.status);
    }
    
    if (criteria.minPrice !== undefined) {
      rooms = rooms.filter(room => room.price >= criteria.minPrice!);
    }
    
    if (criteria.maxPrice !== undefined) {
      rooms = rooms.filter(room => room.price <= criteria.maxPrice!);
    }
    
    if (criteria.capacity) {
      rooms = rooms.filter(room => room.capacity >= criteria.capacity!);
    }
    
    return rooms;
  } catch (error) {
    console.error('Error searching rooms:', error);
    throw error;
  }
};

/**
 * Get rooms by vendor ID (across all hotels)
 */
export const getRoomsByVendor = async (vendorId: string): Promise<Room[]> => {
  try {
    const roomsRef = collection(db, 'rooms');
    const q = query(
      roomsRef,
      where('vendorId', '==', vendorId),
      where('isActive', '==', true),
      orderBy('hotelId', 'asc'),
      orderBy('roomNumber', 'asc')
    );
    
    const querySnapshot = await getDocs(q);
    const rooms: Room[] = [];
    
    querySnapshot.forEach((doc) => {
      rooms.push({
        id: doc.id,
        ...doc.data()
      } as Room);
    });
    
    return rooms;
  } catch (error) {
    console.error('Error getting rooms by vendor:', error);
    throw error;
  }
};
