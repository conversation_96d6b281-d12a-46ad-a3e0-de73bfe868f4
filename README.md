# Hotel Dashboard - Firebase Integration

A comprehensive hotel management dashboard built with React, TypeScript, and Firebase. This application provides a complete solution for managing hotels, staff, bookings, and schedules with real-time data synchronization.

## Features

- **User Authentication**: Role-based access control (Super Admin, Admin, Vendor, Staff, Guest)
- **Hotel Management**: Create, update, and manage hotel properties
- **Staff Management**: Manage staff members, roles, and departments
- **Booking System**: Handle reservations, check-ins, and check-outs
- **Staff Scheduling**: Interactive calendar for managing staff shifts and time-off requests
- **Real-time Data**: All data is stored and synchronized with Firebase Firestore
- **Responsive Design**: Works on desktop and mobile devices

## Technology Stack

- **Frontend**: React 18, TypeScript, Material-UI
- **Backend**: Firebase (Firestore, Authentication, Storage)
- **Calendar**: FullCalendar React integration
- **State Management**: React Context API
- **Routing**: React Router v6
- **Date Handling**: date-fns

## Firebase Configuration

### Project Details
- **Project ID**: linkinblink-f544a
- **Project Number**: ************
- **Web API Key**: AIzaSyBpMQS5vgW9f39NNmcN-XFHALaN1_ar5bg

## Setup Instructions

### 1. Clone and Install Dependencies

```bash
# Install dependencies
npm install
```

### 2. Firebase Setup

1. **Service Account Key**: 
   - Go to [Firebase Console](https://console.firebase.google.com/)
   - Select project `linkinblink-f544a`
   - Go to Project Settings > Service Accounts
   - Generate a new private key
   - Save the downloaded JSON file as `scripts/service-account.json`

2. **Environment Variables**: 
   The `.env` file is already configured with your Firebase credentials.

3. **Initialize Firestore Database**:
   ```bash
   # Initialize Firestore with sample data
   npm run init-firestore-admin
   ```

### 3. Deploy Firestore Rules

```bash
# Deploy security rules
firebase deploy --only firestore:rules
```

### 4. Run the Application

```bash
# Start development server
npm start
```

The application will be available at `http://localhost:3000`

## User Roles and Permissions

### Super Admin
- Full access to all features
- Can manage all hotels and vendors
- System-wide analytics and reporting

### Admin/Vendor
- Manage their own hotels
- Staff management for their properties
- Booking and schedule management
- Hotel-specific analytics

### Staff
- View their own schedule
- Limited booking management
- Access to their hotel's information

### Guest
- View booking history
- Basic profile management

## Database Structure

The application uses the following Firestore collections:

- `users` - User accounts and profiles
- `hotels` - Hotel information and settings
- `rooms` - Room details and availability
- `staff` - Staff member profiles and roles
- `bookings` - Reservation and booking data
- `shifts` - Staff scheduling information
- `timeOffRequests` - Time-off requests and approvals
- `notifications` - User notifications
- `vendorRequests` - Vendor application requests

## Available Scripts

- `npm start` - Start development server
- `npm build` - Build for production
- `npm test` - Run tests
- `npm run deploy` - Build and deploy to Firebase Hosting
- `npm run init-firestore-admin` - Initialize Firestore with sample data

## Key Components

### Authentication
- Login/logout functionality
- Protected routes based on user roles
- User profile management

### Dashboard
- Overview statistics
- Quick actions
- Recent activity feed

### Hotel Management
- Add/edit hotel properties
- Manage amenities and settings
- Hotel analytics

### Staff Management
- Employee profiles and roles
- Department organization
- Emergency contact information

### Booking System
- Reservation management
- Check-in/check-out processes
- Payment status tracking

### Staff Calendar
- Interactive shift scheduling
- Time-off request management
- Calendar views (day, week, month)

## Security

- Firestore security rules enforce role-based access
- Authentication required for all operations
- Data validation on both client and server side

## Deployment

### Firebase Hosting

```bash
# Build and deploy
npm run deploy
```

### Manual Deployment

```bash
# Build the project
npm run build

# Deploy to Firebase
firebase deploy
```

## Troubleshooting

### Common Issues

1. **Service Account Error**: Ensure `scripts/service-account.json` exists and has correct permissions
2. **Firestore Rules**: Make sure rules are deployed with `firebase deploy --only firestore:rules`
3. **Environment Variables**: Verify all Firebase config values in `.env` file

### Support

For issues or questions, please check the Firebase Console for error logs and ensure all services are properly configured.

## License

This project is proprietary software for LinkinBlink hotel management system.
