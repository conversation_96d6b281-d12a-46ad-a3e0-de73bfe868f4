import {
  collection,
  doc,
  getDoc,
  getDocs,
  addDoc,
  setDoc,
  updateDoc,
  deleteDoc,
  query,
  where,
  orderBy,
  limit as firestoreLimit,
  Timestamp,
  serverTimestamp
} from 'firebase/firestore';
import { createUserWithEmailAndPassword, updateProfile } from 'firebase/auth';
import { auth, db } from '../firebase/config';
import { User, UserRole } from '../types';

// Collection name
const USERS_COLLECTION = 'users';

/**
 * Get all users (Super Admin only)
 */
export const getAllUsers = async (): Promise<User[]> => {
  try {
    const usersQuery = query(
      collection(db, USERS_COLLECTION),
      orderBy('name', 'asc')
    );
    
    const querySnapshot = await getDocs(usersQuery);
    const users: User[] = [];
    
    querySnapshot.forEach((doc) => {
      users.push({
        id: doc.id,
        ...doc.data() as Omit<User, 'id'>
      });
    });
    
    return users;
  } catch (error) {
    console.error('Error getting all users:', error);
    throw error;
  }
};

/**
 * Get users for a specific hotel (Vendor only)
 */
export const getUsersForHotel = async (hotelId: string): Promise<User[]> => {
  try {
    const usersQuery = query(
      collection(db, USERS_COLLECTION),
      where('hotelId', '==', hotelId),
      orderBy('name', 'asc')
    );
    
    const querySnapshot = await getDocs(usersQuery);
    const users: User[] = [];
    
    querySnapshot.forEach((doc) => {
      users.push({
        id: doc.id,
        ...doc.data() as Omit<User, 'id'>
      });
    });
    
    return users;
  } catch (error) {
    console.error('Error getting users for hotel:', error);
    throw error;
  }
};

/**
 * Get a user by ID
 */
export const getUserById = async (userId: string): Promise<User | null> => {
  try {
    const docRef = doc(db, USERS_COLLECTION, userId);
    const docSnap = await getDoc(docRef);
    
    if (docSnap.exists()) {
      return {
        id: docSnap.id,
        ...docSnap.data() as Omit<User, 'id'>
      };
    } else {
      return null;
    }
  } catch (error) {
    console.error('Error getting user by ID:', error);
    throw error;
  }
};

/**
 * Create a new user
 */
export const createUser = async (
  userData: {
    email: string;
    password: string;
    name: string;
    role: UserRole;
    phone?: string;
    hotelId?: string;
    vendorId?: string;
    isActive?: boolean;
  }
): Promise<User> => {
  try {
    // Create user in Firebase Auth
    const userCredential = await createUserWithEmailAndPassword(auth, userData.email, userData.password);
    const firebaseUser = userCredential.user;
    
    // Update Firebase Auth profile
    await updateProfile(firebaseUser, {
      displayName: userData.name
    });
    
    // Create user document in Firestore
    const now = Timestamp.now();
    const newUser: Omit<User, 'id'> = {
      email: userData.email,
      name: userData.name,
      role: userData.role,
      phone: userData.phone,
      hotelId: userData.hotelId,
      vendorId: userData.vendorId,
      isActive: userData.isActive !== undefined ? userData.isActive : true,
      createdAt: now,
      updatedAt: now
    };
    
    // Use the Firebase Auth UID as the document ID
    await setDoc(doc(db, USERS_COLLECTION, firebaseUser.uid), newUser);
    
    return {
      id: firebaseUser.uid,
      ...newUser
    };
  } catch (error) {
    console.error('Error creating user:', error);
    throw error;
  }
};

/**
 * Update a user
 */
export const updateUser = async (
  userId: string,
  updates: Partial<Omit<User, 'id' | 'createdAt' | 'updatedAt'>>
): Promise<void> => {
  try {
    const updateData = {
      ...updates,
      updatedAt: serverTimestamp()
    };
    
    await updateDoc(doc(db, USERS_COLLECTION, userId), updateData);
  } catch (error) {
    console.error('Error updating user:', error);
    throw error;
  }
};

/**
 * Delete a user (soft delete)
 */
export const deleteUser = async (userId: string): Promise<void> => {
  try {
    await updateDoc(doc(db, USERS_COLLECTION, userId), {
      isActive: false,
      updatedAt: serverTimestamp()
    });
  } catch (error) {
    console.error('Error deleting user:', error);
    throw error;
  }
};

/**
 * Toggle user active status
 */
export const toggleUserStatus = async (userId: string, isActive: boolean): Promise<void> => {
  try {
    await updateDoc(doc(db, USERS_COLLECTION, userId), {
      isActive: isActive,
      updatedAt: serverTimestamp()
    });
  } catch (error) {
    console.error('Error toggling user status:', error);
    throw error;
  }
};

/**
 * Get users by role
 */
export const getUsersByRole = async (role: UserRole): Promise<User[]> => {
  try {
    const usersQuery = query(
      collection(db, USERS_COLLECTION),
      where('role', '==', role),
      where('isActive', '==', true),
      orderBy('name', 'asc')
    );
    
    const querySnapshot = await getDocs(usersQuery);
    const users: User[] = [];
    
    querySnapshot.forEach((doc) => {
      users.push({
        id: doc.id,
        ...doc.data() as Omit<User, 'id'>
      });
    });
    
    return users;
  } catch (error) {
    console.error('Error getting users by role:', error);
    throw error;
  }
};

/**
 * Search users by name or email
 */
export const searchUsers = async (searchTerm: string): Promise<User[]> => {
  try {
    // Note: This is a simple search. For more advanced search,
    // consider using Algolia or similar search service
    const usersQuery = query(
      collection(db, USERS_COLLECTION),
      where('isActive', '==', true),
      orderBy('name', 'asc')
    );
    
    const querySnapshot = await getDocs(usersQuery);
    const users: User[] = [];
    
    querySnapshot.forEach((doc) => {
      const userData = {
        id: doc.id,
        ...doc.data() as Omit<User, 'id'>
      };
      
      // Filter by search term (name or email)
      if (
        userData.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        userData.email.toLowerCase().includes(searchTerm.toLowerCase())
      ) {
        users.push(userData);
      }
    });
    
    return users;
  } catch (error) {
    console.error('Error searching users:', error);
    throw error;
  }
};
