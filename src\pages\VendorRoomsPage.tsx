import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Grid,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Alert,
  Tabs,
  Tab,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  Avatar,
  Badge,
  Tooltip,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Bed as RoomIcon,
  CheckCircle as AvailableIcon,
  Cancel as OccupiedIcon,
  Build as MaintenanceIcon,
  CleaningServices as CleaningIcon,
  Person as GuestIcon,
  Schedule as ScheduleIcon,
  AttachMoney as PriceIcon,
  Hotel as HotelIcon,
  FilterList as FilterIcon,
  Visibility as ViewIcon
} from '@mui/icons-material';
import { useAuth } from '../hooks/useAuth';
import { Room, RoomStatus, RoomType } from '../types';
import { 
  getRoomsByHotel,
  createRoom,
  updateRoom,
  deleteRoom
} from '../services/roomService';
import { getHotelsByVendor } from '../services/hotelService';
import { format } from 'date-fns';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`rooms-tabpanel-${index}`}
      aria-labelledby={`rooms-tab-${index}`}
      {...other}
    >
      {value === index && <Box>{children}</Box>}
    </div>
  );
}

const VendorRoomsPage: React.FC = () => {
  const { user } = useAuth();
  const [hotels, setHotels] = useState<any[]>([]);
  const [selectedHotel, setSelectedHotel] = useState<string>('');
  const [rooms, setRooms] = useState<Room[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [editingRoom, setEditingRoom] = useState<Room | null>(null);
  const [tabValue, setTabValue] = useState(0);
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [filterType, setFilterType] = useState<string>('all');

  // Mock room data for demonstration
  const [mockRooms] = useState<any[]>([
    {
      id: '1',
      roomNumber: '101',
      type: 'standard',
      status: 'available',
      floor: 1,
      capacity: 2,
      pricePerNight: 120,
      amenities: ['WiFi', 'TV', 'AC'],
      description: 'Comfortable standard room',
      currentGuest: null,
      checkInDate: null,
      checkOutDate: null,
      lastCleaned: new Date(Date.now() - 2 * 60 * 60 * 1000),
      maintenanceNotes: ''
    },
    {
      id: '2',
      roomNumber: '102',
      type: 'deluxe',
      status: 'occupied',
      floor: 1,
      capacity: 2,
      pricePerNight: 180,
      amenities: ['WiFi', 'TV', 'AC', 'Mini Bar'],
      description: 'Spacious deluxe room',
      currentGuest: 'John Smith',
      checkInDate: new Date(Date.now() - 24 * 60 * 60 * 1000),
      checkOutDate: new Date(Date.now() + 24 * 60 * 60 * 1000),
      lastCleaned: new Date(Date.now() - 26 * 60 * 60 * 1000),
      maintenanceNotes: ''
    },
    {
      id: '3',
      roomNumber: '103',
      type: 'suite',
      status: 'cleaning',
      floor: 1,
      capacity: 4,
      pricePerNight: 300,
      amenities: ['WiFi', 'TV', 'AC', 'Mini Bar', 'Jacuzzi'],
      description: 'Luxury suite with living area',
      currentGuest: null,
      checkInDate: null,
      checkOutDate: null,
      lastCleaned: new Date(Date.now() - 30 * 60 * 1000),
      maintenanceNotes: ''
    },
    {
      id: '4',
      roomNumber: '201',
      type: 'standard',
      status: 'maintenance',
      floor: 2,
      capacity: 2,
      pricePerNight: 120,
      amenities: ['WiFi', 'TV', 'AC'],
      description: 'Standard room - currently under maintenance',
      currentGuest: null,
      checkInDate: null,
      checkOutDate: null,
      lastCleaned: new Date(Date.now() - 48 * 60 * 60 * 1000),
      maintenanceNotes: 'AC unit needs repair'
    },
    {
      id: '5',
      roomNumber: '202',
      type: 'deluxe',
      status: 'available',
      floor: 2,
      capacity: 3,
      pricePerNight: 180,
      amenities: ['WiFi', 'TV', 'AC', 'Mini Bar', 'Balcony'],
      description: 'Deluxe room with city view',
      currentGuest: null,
      checkInDate: null,
      checkOutDate: null,
      lastCleaned: new Date(Date.now() - 4 * 60 * 60 * 1000),
      maintenanceNotes: ''
    },
    {
      id: '6',
      roomNumber: '301',
      type: 'suite',
      status: 'occupied',
      floor: 3,
      capacity: 4,
      pricePerNight: 300,
      amenities: ['WiFi', 'TV', 'AC', 'Mini Bar', 'Jacuzzi', 'Kitchen'],
      description: 'Presidential suite',
      currentGuest: 'Sarah Johnson',
      checkInDate: new Date(Date.now() - 12 * 60 * 60 * 1000),
      checkOutDate: new Date(Date.now() + 36 * 60 * 60 * 1000),
      lastCleaned: new Date(Date.now() - 14 * 60 * 60 * 1000),
      maintenanceNotes: ''
    }
  ]);

  useEffect(() => {
    const fetchHotels = async () => {
      if (!user?.vendorId) return;
      
      try {
        setLoading(true);
        const vendorHotels = await getHotelsByVendor(user.vendorId);
        setHotels(vendorHotels);
        if (vendorHotels.length > 0) {
          setSelectedHotel(vendorHotels[0].id);
        }
      } catch (err: any) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchHotels();
  }, [user]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'available':
        return '#4caf50';
      case 'occupied':
        return '#f44336';
      case 'cleaning':
        return '#ff9800';
      case 'maintenance':
        return '#9e9e9e';
      default:
        return '#757575';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'available':
        return <AvailableIcon sx={{ color: 'white' }} />;
      case 'occupied':
        return <OccupiedIcon sx={{ color: 'white' }} />;
      case 'cleaning':
        return <CleaningIcon sx={{ color: 'white' }} />;
      case 'maintenance':
        return <MaintenanceIcon sx={{ color: 'white' }} />;
      default:
        return <RoomIcon sx={{ color: 'white' }} />;
    }
  };

  const filteredRooms = mockRooms.filter(room => {
    if (filterStatus !== 'all' && room.status !== filterStatus) return false;
    if (filterType !== 'all' && room.type !== filterType) return false;
    return true;
  });

  const roomsByStatus = {
    available: mockRooms.filter(r => r.status === 'available').length,
    occupied: mockRooms.filter(r => r.status === 'occupied').length,
    cleaning: mockRooms.filter(r => r.status === 'cleaning').length,
    maintenance: mockRooms.filter(r => r.status === 'maintenance').length
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 3 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1">
          Room Management
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => setDialogOpen(true)}
        >
          Add New Room
        </Button>
      </Box>

      {/* Room Statistics */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Avatar sx={{ bgcolor: '#4caf50', mx: 'auto', mb: 1 }}>
                <AvailableIcon />
              </Avatar>
              <Typography variant="h4" color="success.main">
                {roomsByStatus.available}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                Available Rooms
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Avatar sx={{ bgcolor: '#f44336', mx: 'auto', mb: 1 }}>
                <OccupiedIcon />
              </Avatar>
              <Typography variant="h4" color="error.main">
                {roomsByStatus.occupied}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                Occupied Rooms
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Avatar sx={{ bgcolor: '#ff9800', mx: 'auto', mb: 1 }}>
                <CleaningIcon />
              </Avatar>
              <Typography variant="h4" color="warning.main">
                {roomsByStatus.cleaning}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                Being Cleaned
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Avatar sx={{ bgcolor: '#9e9e9e', mx: 'auto', mb: 1 }}>
                <MaintenanceIcon />
              </Avatar>
              <Typography variant="h4" color="text.secondary">
                {roomsByStatus.maintenance}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                Under Maintenance
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Filters */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box display="flex" gap={2} alignItems="center">
            <FilterIcon />
            <FormControl size="small" sx={{ minWidth: 120 }}>
              <InputLabel>Status</InputLabel>
              <Select
                value={filterStatus}
                label="Status"
                onChange={(e) => setFilterStatus(e.target.value)}
              >
                <MenuItem value="all">All Status</MenuItem>
                <MenuItem value="available">Available</MenuItem>
                <MenuItem value="occupied">Occupied</MenuItem>
                <MenuItem value="cleaning">Cleaning</MenuItem>
                <MenuItem value="maintenance">Maintenance</MenuItem>
              </Select>
            </FormControl>
            <FormControl size="small" sx={{ minWidth: 120 }}>
              <InputLabel>Type</InputLabel>
              <Select
                value={filterType}
                label="Type"
                onChange={(e) => setFilterType(e.target.value)}
              >
                <MenuItem value="all">All Types</MenuItem>
                <MenuItem value="standard">Standard</MenuItem>
                <MenuItem value="deluxe">Deluxe</MenuItem>
                <MenuItem value="suite">Suite</MenuItem>
              </Select>
            </FormControl>
            <Typography variant="body2" color="textSecondary" sx={{ ml: 'auto' }}>
              Showing {filteredRooms.length} of {mockRooms.length} rooms
            </Typography>
          </Box>
        </CardContent>
      </Card>

      {/* Rooms Grid */}
      <Grid container spacing={3}>
        {filteredRooms.map((room) => (
          <Grid item xs={12} sm={6} md={4} lg={3} key={room.id}>
            <Card
              sx={{
                height: '100%',
                border: `2px solid ${getStatusColor(room.status)}`,
                '&:hover': {
                  boxShadow: 3,
                  transform: 'translateY(-2px)',
                  transition: 'all 0.2s'
                }
              }}
            >
              <CardContent>
                <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
                  <Typography variant="h6" component="div">
                    Room {room.roomNumber}
                  </Typography>
                  <Chip
                    icon={getStatusIcon(room.status)}
                    label={room.status.charAt(0).toUpperCase() + room.status.slice(1)}
                    size="small"
                    sx={{
                      backgroundColor: getStatusColor(room.status),
                      color: 'white',
                      '& .MuiChip-icon': {
                        color: 'white'
                      }
                    }}
                  />
                </Box>

                <Typography variant="body2" color="textSecondary" gutterBottom>
                  {room.type.charAt(0).toUpperCase() + room.type.slice(1)} • Floor {room.floor}
                </Typography>

                <Box display="flex" alignItems="center" gap={1} mb={1}>
                  <GuestIcon sx={{ fontSize: 16, color: 'text.secondary' }} />
                  <Typography variant="body2">
                    Capacity: {room.capacity} guests
                  </Typography>
                </Box>

                <Box display="flex" alignItems="center" gap={1} mb={2}>
                  <PriceIcon sx={{ fontSize: 16, color: 'text.secondary' }} />
                  <Typography variant="body2">
                    ${room.pricePerNight}/night
                  </Typography>
                </Box>

                {room.currentGuest && (
                  <Box mb={2}>
                    <Typography variant="body2" fontWeight="bold" color="primary">
                      Current Guest: {room.currentGuest}
                    </Typography>
                    <Typography variant="caption" color="textSecondary">
                      Check-out: {room.checkOutDate ? format(room.checkOutDate, 'MMM dd, HH:mm') : 'N/A'}
                    </Typography>
                  </Box>
                )}

                {room.maintenanceNotes && (
                  <Alert severity="warning" sx={{ mb: 2, fontSize: '0.8rem' }}>
                    {room.maintenanceNotes}
                  </Alert>
                )}

                <Typography variant="caption" color="textSecondary" display="block" mb={2}>
                  Last cleaned: {format(room.lastCleaned, 'MMM dd, HH:mm')}
                </Typography>

                <Box display="flex" gap={1}>
                  <Tooltip title="View Details">
                    <IconButton size="small" color="primary">
                      <ViewIcon />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Edit Room">
                    <IconButton size="small" color="secondary">
                      <EditIcon />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Delete Room">
                    <IconButton size="small" color="error">
                      <DeleteIcon />
                    </IconButton>
                  </Tooltip>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {filteredRooms.length === 0 && (
        <Box textAlign="center" py={8}>
          <RoomIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
          <Typography variant="h6" gutterBottom>
            No rooms found
          </Typography>
          <Typography variant="body2" color="textSecondary">
            Try adjusting your filters or add a new room.
          </Typography>
        </Box>
      )}
    </Box>
  );
};

export default VendorRoomsPage;
