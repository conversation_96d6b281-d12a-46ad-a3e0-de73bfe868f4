import { FirebaseError } from 'firebase/app';

export interface AppError {
  code: string;
  message: string;
  details?: any;
}

/**
 * Convert Firebase errors to user-friendly messages
 */
export const getFirebaseErrorMessage = (error: FirebaseError): string => {
  switch (error.code) {
    // Auth errors
    case 'auth/user-not-found':
      return 'No account found with this email address.';
    case 'auth/wrong-password':
      return 'Incorrect password. Please try again.';
    case 'auth/email-already-in-use':
      return 'An account with this email already exists.';
    case 'auth/weak-password':
      return 'Password should be at least 6 characters long.';
    case 'auth/invalid-email':
      return 'Please enter a valid email address.';
    case 'auth/user-disabled':
      return 'This account has been disabled. Please contact support.';
    case 'auth/too-many-requests':
      return 'Too many failed attempts. Please try again later.';
    case 'auth/network-request-failed':
      return 'Network error. Please check your internet connection.';
    
    // Firestore errors
    case 'firestore/permission-denied':
      return 'You do not have permission to perform this action.';
    case 'firestore/not-found':
      return 'The requested document was not found.';
    case 'firestore/already-exists':
      return 'A document with this ID already exists.';
    case 'firestore/resource-exhausted':
      return 'Service is temporarily unavailable. Please try again later.';
    case 'firestore/failed-precondition':
      return 'Operation failed due to a conflict. Please refresh and try again.';
    case 'firestore/aborted':
      return 'Operation was aborted due to a conflict. Please try again.';
    case 'firestore/out-of-range':
      return 'Invalid data provided. Please check your input.';
    case 'firestore/unimplemented':
      return 'This feature is not yet implemented.';
    case 'firestore/internal':
      return 'An internal error occurred. Please try again later.';
    case 'firestore/unavailable':
      return 'Service is temporarily unavailable. Please try again later.';
    case 'firestore/data-loss':
      return 'Data loss occurred. Please contact support.';
    case 'firestore/unauthenticated':
      return 'You must be logged in to perform this action.';
    case 'firestore/invalid-argument':
      return 'Invalid data provided. Please check your input.';
    case 'firestore/deadline-exceeded':
      return 'Operation timed out. Please try again.';
    case 'firestore/cancelled':
      return 'Operation was cancelled.';
    
    // Storage errors
    case 'storage/object-not-found':
      return 'File not found.';
    case 'storage/bucket-not-found':
      return 'Storage bucket not found.';
    case 'storage/project-not-found':
      return 'Project not found.';
    case 'storage/quota-exceeded':
      return 'Storage quota exceeded.';
    case 'storage/unauthenticated':
      return 'You must be logged in to upload files.';
    case 'storage/unauthorized':
      return 'You do not have permission to upload files.';
    case 'storage/retry-limit-exceeded':
      return 'Upload failed after multiple attempts. Please try again.';
    case 'storage/invalid-checksum':
      return 'File upload failed due to corruption. Please try again.';
    case 'storage/canceled':
      return 'File upload was cancelled.';
    case 'storage/invalid-event-name':
      return 'Invalid upload event.';
    case 'storage/invalid-url':
      return 'Invalid file URL.';
    case 'storage/invalid-argument':
      return 'Invalid file data.';
    case 'storage/no-default-bucket':
      return 'No default storage bucket configured.';
    case 'storage/cannot-slice-blob':
      return 'File upload failed. Please try again.';
    case 'storage/server-file-wrong-size':
      return 'File size mismatch. Please try again.';
    
    default:
      return error.message || 'An unexpected error occurred. Please try again.';
  }
};

/**
 * Handle and format errors for display to users
 */
export const handleError = (error: any): AppError => {
  console.error('Error occurred:', error);
  
  if (error instanceof FirebaseError) {
    return {
      code: error.code,
      message: getFirebaseErrorMessage(error),
      details: error
    };
  }
  
  if (error instanceof Error) {
    return {
      code: 'generic-error',
      message: error.message || 'An unexpected error occurred.',
      details: error
    };
  }
  
  if (typeof error === 'string') {
    return {
      code: 'string-error',
      message: error,
      details: null
    };
  }
  
  return {
    code: 'unknown-error',
    message: 'An unknown error occurred. Please try again.',
    details: error
  };
};

/**
 * Show user-friendly error messages
 */
export const showErrorMessage = (error: any): string => {
  const appError = handleError(error);
  return appError.message;
};

/**
 * Log errors for debugging
 */
export const logError = (error: any, context?: string): void => {
  const appError = handleError(error);
  
  console.group(`🚨 Error ${context ? `in ${context}` : ''}`);
  console.error('Code:', appError.code);
  console.error('Message:', appError.message);
  if (appError.details) {
    console.error('Details:', appError.details);
  }
  console.groupEnd();
  
  // In production, you might want to send this to an error tracking service
  // like Sentry, LogRocket, or Firebase Crashlytics
};

/**
 * Retry function with exponential backoff
 */
export const retryWithBackoff = async <T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  baseDelay: number = 1000
): Promise<T> => {
  let lastError: any;
  
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      lastError = error;
      
      if (attempt === maxRetries) {
        throw error;
      }
      
      // Exponential backoff: 1s, 2s, 4s, etc.
      const delay = baseDelay * Math.pow(2, attempt);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  throw lastError;
};

/**
 * Check if error is a network error
 */
export const isNetworkError = (error: any): boolean => {
  if (error instanceof FirebaseError) {
    return error.code === 'auth/network-request-failed' ||
           error.code === 'firestore/unavailable' ||
           error.code === 'firestore/deadline-exceeded';
  }
  
  return false;
};

/**
 * Check if error is a permission error
 */
export const isPermissionError = (error: any): boolean => {
  if (error instanceof FirebaseError) {
    return error.code === 'firestore/permission-denied' ||
           error.code === 'storage/unauthorized' ||
           error.code === 'firestore/unauthenticated';
  }
  
  return false;
};

/**
 * Check if error is retryable
 */
export const isRetryableError = (error: any): boolean => {
  if (error instanceof FirebaseError) {
    return error.code === 'firestore/unavailable' ||
           error.code === 'firestore/deadline-exceeded' ||
           error.code === 'firestore/aborted' ||
           error.code === 'auth/network-request-failed' ||
           error.code === 'storage/retry-limit-exceeded';
  }
  
  return false;
};

export default {
  handleError,
  showErrorMessage,
  logError,
  retryWithBackoff,
  isNetworkError,
  isPermissionError,
  isRetryableError
};
