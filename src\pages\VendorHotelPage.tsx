import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Button,
  TextField,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  CircularProgress,
  Chip,
  Avatar,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  IconButton,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow
} from '@mui/material';
import {
  Hotel as HotelIcon,
  Edit as EditIcon,
  LocationOn as LocationIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  Star as StarIcon,
  Bed as RoomIcon,
  People as PeopleIcon,
  AttachMoney as RevenueIcon,
  TrendingUp as TrendingUpIcon,
  Settings as SettingsIcon,
  Photo as PhotoIcon,
  Save as SaveIcon,
  Cancel as CancelIcon
} from '@mui/icons-material';
import { useAuth } from '../hooks/useAuth';
import { getHotelsByVendor, updateHotel } from '../services/hotelService';
import { Hotel } from '../types';

// Utility function to convert Timestamp to Date
const toDate = (date: any): Date => {
  if (!date) return new Date();
  if (date instanceof Date) return date;
  if (date.toDate && typeof date.toDate === 'function') return date.toDate();
  return new Date(date);
};

const VendorHotelPage: React.FC = () => {
  const { user } = useAuth();
  const [hotel, setHotel] = useState<Hotel | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [editFormData, setEditFormData] = useState<Partial<Hotel>>({});
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    const fetchHotel = async () => {
      if (!user?.vendorId) return;

      try {
        setLoading(true);
        const hotels = await getHotelsByVendor(user.vendorId);
        if (hotels.length > 0) {
          setHotel(hotels[0]); // Vendor should have only one hotel
          setEditFormData(hotels[0]);
        }
      } catch (err: any) {
        console.error('Error fetching hotel:', err);
        setError(err.message || 'Failed to load hotel information');
      } finally {
        setLoading(false);
      }
    };

    fetchHotel();
  }, [user]);

  const handleEditClick = () => {
    if (hotel) {
      setEditFormData(hotel);
      setEditDialogOpen(true);
    }
  };

  const handleSaveChanges = async () => {
    if (!hotel || !editFormData.id) return;

    try {
      setSaving(true);
      await updateHotel(editFormData.id, editFormData);
      setHotel({ ...hotel, ...editFormData });
      setEditDialogOpen(false);
    } catch (err: any) {
      console.error('Error updating hotel:', err);
      setError(err.message || 'Failed to update hotel');
    } finally {
      setSaving(false);
    }
  };

  const handleInputChange = (field: keyof Hotel, value: any) => {
    setEditFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 3 }}>
        {error}
      </Alert>
    );
  }

  if (!hotel) {
    return (
      <Box textAlign="center" py={8}>
        <HotelIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
        <Typography variant="h5" gutterBottom>
          No Hotel Found
        </Typography>
        <Typography variant="body1" color="textSecondary" paragraph>
          You don't have a hotel assigned to your account. Please contact the administrator.
        </Typography>
      </Box>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1">
          My Hotel
        </Typography>
        <Button
          variant="contained"
          startIcon={<EditIcon />}
          onClick={handleEditClick}
        >
          Edit Hotel Details
        </Button>
      </Box>

      {/* Hotel Overview Card */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={3}>
            <Grid item xs={12} md={8}>
              <Box display="flex" alignItems="center" mb={2}>
                <Avatar
                  sx={{
                    width: 80,
                    height: 80,
                    mr: 3,
                    bgcolor: 'primary.main'
                  }}
                >
                  <HotelIcon sx={{ fontSize: 40 }} />
                </Avatar>
                <Box>
                  <Typography variant="h5" gutterBottom>
                    {hotel.name}
                  </Typography>
                  <Box display="flex" alignItems="center" mb={1}>
                    <LocationIcon sx={{ fontSize: 16, mr: 0.5, color: 'text.secondary' }} />
                    <Typography variant="body2" color="textSecondary">
                      {hotel.address}
                    </Typography>
                  </Box>
                  <Box display="flex" alignItems="center" gap={2}>
                    <Chip
                      label={`${hotel.totalRooms} Rooms`}
                      icon={<RoomIcon />}
                      size="small"
                      color="primary"
                    />
                    <Chip
                      label={`${hotel.rating || 4.5} Stars`}
                      icon={<StarIcon />}
                      size="small"
                      color="secondary"
                    />
                  </Box>
                </Box>
              </Box>
            </Grid>
            <Grid item xs={12} md={4}>
              <Box textAlign="right">
                <Typography variant="h6" color="primary" gutterBottom>
                  Hotel Status
                </Typography>
                <Chip
                  label={hotel.isActive ? 'Active' : 'Inactive'}
                  color={hotel.isActive ? 'success' : 'error'}
                  sx={{ mb: 2 }}
                />
                <Typography variant="body2" color="textSecondary">
                  Created: {hotel.createdAt ? toDate(hotel.createdAt).toLocaleDateString() : 'Unknown'}
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Hotel Details */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Contact Information
              </Typography>
              <List>
                <ListItem>
                  <ListItemIcon>
                    <PhoneIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary="Phone"
                    secondary={hotel.phone || 'Not provided'}
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <EmailIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary="Email"
                    secondary={hotel.email || 'Not provided'}
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <LocationIcon />
                  </ListItemIcon>
                  <ListItemText
                    primary="Address"
                    secondary={hotel.address}
                  />
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Hotel Statistics
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Box textAlign="center" p={2}>
                    <RoomIcon sx={{ fontSize: 32, color: 'primary.main', mb: 1 }} />
                    <Typography variant="h4">{hotel.totalRooms}</Typography>
                    <Typography variant="body2" color="textSecondary">
                      Total Rooms
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={6}>
                  <Box textAlign="center" p={2}>
                    <PeopleIcon sx={{ fontSize: 32, color: 'success.main', mb: 1 }} />
                    <Typography variant="h4">12</Typography>
                    <Typography variant="body2" color="textSecondary">
                      Staff Members
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={6}>
                  <Box textAlign="center" p={2}>
                    <RevenueIcon sx={{ fontSize: 32, color: 'warning.main', mb: 1 }} />
                    <Typography variant="h4">$45K</Typography>
                    <Typography variant="body2" color="textSecondary">
                      Monthly Revenue
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={6}>
                  <Box textAlign="center" p={2}>
                    <TrendingUpIcon sx={{ fontSize: 32, color: 'info.main', mb: 1 }} />
                    <Typography variant="h4">85%</Typography>
                    <Typography variant="body2" color="textSecondary">
                      Occupancy Rate
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Hotel Description */}
      {hotel.description && (
        <Card sx={{ mb: 3 }}>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              About Our Hotel
            </Typography>
            <Typography variant="body1" paragraph>
              {hotel.description}
            </Typography>
          </CardContent>
        </Card>
      )}

      {/* Edit Hotel Dialog */}
      <Dialog open={editDialogOpen} onClose={() => setEditDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>Edit Hotel Details</DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Hotel Name"
                value={editFormData.name || ''}
                onChange={(e) => handleInputChange('name', e.target.value)}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Phone"
                value={editFormData.phone || ''}
                onChange={(e) => handleInputChange('phone', e.target.value)}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Email"
                type="email"
                value={editFormData.email || ''}
                onChange={(e) => handleInputChange('email', e.target.value)}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Total Rooms"
                type="number"
                value={editFormData.totalRooms || ''}
                onChange={(e) => handleInputChange('totalRooms', parseInt(e.target.value))}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Address"
                value={editFormData.address || ''}
                onChange={(e) => handleInputChange('address', e.target.value)}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Description"
                multiline
                rows={4}
                value={editFormData.description || ''}
                onChange={(e) => handleInputChange('description', e.target.value)}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEditDialogOpen(false)} startIcon={<CancelIcon />}>
            Cancel
          </Button>
          <Button
            onClick={handleSaveChanges}
            variant="contained"
            startIcon={<SaveIcon />}
            disabled={saving}
          >
            {saving ? <CircularProgress size={20} /> : 'Save Changes'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default VendorHotelPage;
