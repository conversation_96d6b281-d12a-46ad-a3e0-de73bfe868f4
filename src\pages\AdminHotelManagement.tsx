import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>ton,
  Card,
  CardContent,
  CardMedia,
  Grid,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  CircularProgress,
  Alert,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Avatar,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  Badge,
  Tooltip,
  Switch,
  FormControlLabel,
  ImageList,
  ImageListItem,
  ImageListItemBar
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  Hotel as HotelIcon,
  LocationOn as LocationIcon,
  Phone as PhoneIcon,
  Email as EmailIcon,
  Star as StarIcon,
  Image as ImageIcon,
  Upload as UploadIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
  CheckCircle as ActiveIcon,
  Cancel as InactiveIcon,
  People as PeopleIcon,
  Room as RoomIcon,
  AttachMoney as MoneyIcon
} from '@mui/icons-material';
import { useAuth } from '../hooks/useAuth';
import { Hotel } from '../types';
import { 
  getAllHotels,
  getHotelsByVendor, 
  createHotel, 
  updateHotel, 
  deleteHotel 
} from '../services/hotelService';
import { format } from 'date-fns';

// Utility function to convert Timestamp to Date
const toDate = (date: any): Date => {
  if (!date) return new Date();
  if (date instanceof Date) return date;
  if (date.toDate && typeof date.toDate === 'function') return date.toDate();
  return new Date(date);
};

const AdminHotelManagement: React.FC = () => {
  const { user } = useAuth();
  const [hotels, setHotels] = useState<Hotel[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [viewDialogOpen, setViewDialogOpen] = useState(false);
  const [editingHotel, setEditingHotel] = useState<Hotel | null>(null);
  const [viewingHotel, setViewingHotel] = useState<Hotel | null>(null);
  const [imageFiles, setImageFiles] = useState<File[]>([]);
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    address: '',
    city: '',
    state: '',
    country: '',
    zipCode: '',
    phone: '',
    email: '',
    website: '',
    totalRooms: 0,
    amenities: [] as string[],
    images: [] as string[],
    isActive: true,
    vendorId: '',
    vendorName: '',
    rating: 0
  });

  // Mock hotel data for demonstration
  const [mockHotels] = useState<any[]>([
    {
      id: '1',
      name: 'Grand Plaza Hotel',
      description: 'Luxury hotel in the heart of the city with world-class amenities and exceptional service.',
      address: '123 Main Street',
      city: 'New York',
      state: 'NY',
      country: 'USA',
      zipCode: '10001',
      phone: '+****************',
      email: '<EMAIL>',
      website: 'www.grandplaza.com',
      totalRooms: 150,
      amenities: ['WiFi', 'Pool', 'Gym', 'Spa', 'Restaurant', 'Bar', 'Parking', 'Room Service'],
      images: [
        'https://images.unsplash.com/photo-1566073771259-6a8506099945?w=400',
        'https://images.unsplash.com/photo-1551882547-ff40c63fe5fa?w=400',
        'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=400'
      ],
      isActive: true,
      vendorId: 'vendor1',
      vendorName: 'John Smith',
      rating: 4.8,
      createdAt: new Date('2023-01-15'),
      occupancyRate: 85,
      revenue: 125000,
      staff: 45,
      avgRoomPrice: 250
    },
    {
      id: '2',
      name: 'Seaside Resort',
      description: 'Beautiful beachfront resort with stunning ocean views and premium facilities.',
      address: '456 Ocean Drive',
      city: 'Miami',
      state: 'FL',
      country: 'USA',
      zipCode: '33139',
      phone: '+****************',
      email: '<EMAIL>',
      website: 'www.seasideresort.com',
      totalRooms: 200,
      amenities: ['WiFi', 'Beach Access', 'Pool', 'Spa', 'Restaurant', 'Bar', 'Water Sports'],
      images: [
        'https://images.unsplash.com/photo-1520250497591-112f2f40a3f4?w=400',
        'https://images.unsplash.com/photo-1571896349842-33c89424de2d?w=400'
      ],
      isActive: true,
      vendorId: 'vendor2',
      vendorName: 'Sarah Johnson',
      rating: 4.6,
      createdAt: new Date('2023-03-20'),
      occupancyRate: 92,
      revenue: 180000,
      staff: 65,
      avgRoomPrice: 320
    },
    {
      id: '3',
      name: 'Mountain Lodge',
      description: 'Cozy mountain retreat perfect for nature lovers and adventure seekers.',
      address: '789 Mountain View Road',
      city: 'Aspen',
      state: 'CO',
      country: 'USA',
      zipCode: '81611',
      phone: '+****************',
      email: '<EMAIL>',
      website: 'www.mountainlodge.com',
      totalRooms: 80,
      amenities: ['WiFi', 'Fireplace', 'Hiking Trails', 'Restaurant', 'Bar', 'Ski Access'],
      images: [
        'https://images.unsplash.com/photo-1542314831-068cd1dbfeeb?w=400'
      ],
      isActive: false,
      vendorId: 'vendor3',
      vendorName: 'Michael Brown',
      rating: 4.4,
      createdAt: new Date('2023-06-10'),
      occupancyRate: 68,
      revenue: 95000,
      staff: 25,
      avgRoomPrice: 180
    }
  ]);

  useEffect(() => {
    fetchHotels();
  }, [user]);

  const fetchHotels = async () => {
    try {
      setLoading(true);
      if (user?.role === 'super_admin') {
        // Super admin sees all hotels
        setHotels(mockHotels);
      } else if (user?.vendorId) {
        // Vendor sees only their hotels
        const vendorHotels = mockHotels.filter(h => h.vendorId === user.vendorId);
        setHotels(vendorHotels);
      }
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleOpenDialog = (hotel?: Hotel) => {
    if (hotel) {
      setEditingHotel(hotel);
      setFormData({
        name: hotel.name,
        description: hotel.description,
        address: hotel.address,
        city: hotel.city,
        state: hotel.state,
        country: hotel.country,
        zipCode: hotel.zipCode,
        phone: hotel.phone,
        email: hotel.email,
        website: hotel.website || '',
        totalRooms: hotel.totalRooms || 0,
        amenities: hotel.amenities || [],
        images: hotel.images || [],
        isActive: hotel.isActive,
        vendorId: hotel.vendorId || '',
        vendorName: hotel.vendorName || '',
        rating: hotel.rating || 0
      });
    } else {
      setEditingHotel(null);
      setFormData({
        name: '',
        description: '',
        address: '',
        city: '',
        state: '',
        country: '',
        zipCode: '',
        phone: '',
        email: '',
        website: '',
        totalRooms: 0,
        amenities: [],
        images: [],
        isActive: true,
        vendorId: '',
        vendorName: '',
        rating: 0
      });
    }
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setEditingHotel(null);
    setImageFiles([]);
  };

  const handleViewHotel = (hotel: Hotel) => {
    setViewingHotel(hotel);
    setViewDialogOpen(true);
  };

  const handleFormChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    setImageFiles(prev => [...prev, ...files]);
    
    // Create preview URLs
    const newImageUrls = files.map(file => URL.createObjectURL(file));
    setFormData(prev => ({
      ...prev,
      images: [...prev.images, ...newImageUrls]
    }));
  };

  const removeImage = (index: number) => {
    setFormData(prev => ({
      ...prev,
      images: prev.images.filter((_, i) => i !== index)
    }));
    setImageFiles(prev => prev.filter((_, i) => i !== index));
  };

  const handleSaveHotel = async () => {
    try {
      if (editingHotel) {
        // Update existing hotel
        console.log('Updating hotel:', formData);
        // await updateHotel(editingHotel.id, formData);
      } else {
        // Create new hotel
        console.log('Creating hotel:', formData);
        // await createHotel(formData);
      }
      
      handleCloseDialog();
      fetchHotels();
    } catch (err: any) {
      console.error('Error saving hotel:', err);
      setError(err.message);
    }
  };

  const handleDeleteHotel = async (hotelId: string) => {
    if (window.confirm('Are you sure you want to delete this hotel?')) {
      try {
        // await deleteHotel(hotelId);
        console.log('Deleting hotel:', hotelId);
        fetchHotels();
      } catch (err: any) {
        console.error('Error deleting hotel:', err);
        setError(err.message);
      }
    }
  };

  const toggleHotelStatus = async (hotelId: string, currentStatus: boolean) => {
    try {
      // await updateHotel(hotelId, { isActive: !currentStatus });
      console.log('Toggling hotel status:', hotelId, !currentStatus);
      fetchHotels();
    } catch (err: any) {
      console.error('Error updating hotel status:', err);
      setError(err.message);
    }
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 3 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Box>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1">
          Hotel Management
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => handleOpenDialog()}
        >
          Add New Hotel
        </Button>
      </Box>

      {/* Hotel Statistics */}
      <Grid container spacing={3} sx={{ mb: 3 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Avatar sx={{ bgcolor: '#4caf50', mx: 'auto', mb: 1 }}>
                <HotelIcon />
              </Avatar>
              <Typography variant="h4" color="primary">
                {hotels.length}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                Total Hotels
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Avatar sx={{ bgcolor: '#2196f3', mx: 'auto', mb: 1 }}>
                <ActiveIcon />
              </Avatar>
              <Typography variant="h4" color="success.main">
                {hotels.filter(h => h.isActive).length}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                Active Hotels
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Avatar sx={{ bgcolor: '#ff9800', mx: 'auto', mb: 1 }}>
                <RoomIcon />
              </Avatar>
              <Typography variant="h4" color="warning.main">
                {hotels.reduce((sum, h) => sum + (h.totalRooms || 0), 0)}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                Total Rooms
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Avatar sx={{ bgcolor: '#9c27b0', mx: 'auto', mb: 1 }}>
                <MoneyIcon />
              </Avatar>
              <Typography variant="h4" color="secondary.main">
                ${hotels.reduce((sum, h) => sum + (h.revenue || 0), 0).toLocaleString()}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                Total Revenue
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Hotels Grid */}
      <Grid container spacing={3}>
        {hotels.map((hotel) => (
          <Grid item xs={12} md={6} lg={4} key={hotel.id}>
            <Card
              sx={{
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                '&:hover': {
                  boxShadow: 3,
                  transform: 'translateY(-2px)',
                  transition: 'all 0.2s'
                }
              }}
            >
              {hotel.images && hotel.images.length > 0 && (
                <CardMedia
                  component="img"
                  height="200"
                  image={hotel.images[0]}
                  alt={hotel.name}
                />
              )}
              
              <CardContent sx={{ flexGrow: 1 }}>
                <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
                  <Typography variant="h6" component="div">
                    {hotel.name}
                  </Typography>
                  <Box display="flex" alignItems="center" gap={1}>
                    <Chip
                      icon={hotel.isActive ? <ActiveIcon /> : <InactiveIcon />}
                      label={hotel.isActive ? 'Active' : 'Inactive'}
                      size="small"
                      color={hotel.isActive ? 'success' : 'error'}
                    />
                    <FormControlLabel
                      control={
                        <Switch
                          checked={hotel.isActive}
                          onChange={() => toggleHotelStatus(hotel.id, hotel.isActive)}
                          size="small"
                        />
                      }
                      label=""
                    />
                  </Box>
                </Box>

                <Typography variant="body2" color="textSecondary" gutterBottom>
                  {hotel.description}
                </Typography>

                <Box display="flex" alignItems="center" gap={1} mb={1}>
                  <LocationIcon sx={{ fontSize: 16, color: 'text.secondary' }} />
                  <Typography variant="body2">
                    {hotel.city}, {hotel.state}
                  </Typography>
                </Box>

                <Box display="flex" alignItems="center" gap={1} mb={1}>
                  <RoomIcon sx={{ fontSize: 16, color: 'text.secondary' }} />
                  <Typography variant="body2">
                    {hotel.totalRooms} rooms
                  </Typography>
                </Box>

                <Box display="flex" alignItems="center" gap={1} mb={1}>
                  <StarIcon sx={{ fontSize: 16, color: '#ffc107' }} />
                  <Typography variant="body2">
                    {hotel.rating}/5.0 rating
                  </Typography>
                </Box>

                <Box display="flex" alignItems="center" gap={1} mb={2}>
                  <PeopleIcon sx={{ fontSize: 16, color: 'text.secondary' }} />
                  <Typography variant="body2">
                    Vendor: {hotel.vendorName}
                  </Typography>
                </Box>

                {/* Performance Metrics */}
                <Box mb={2}>
                  <Typography variant="subtitle2" gutterBottom>
                    Performance:
                  </Typography>
                  <Grid container spacing={1}>
                    <Grid item xs={6}>
                      <Typography variant="caption" color="textSecondary">
                        Occupancy
                      </Typography>
                      <Typography variant="body2" fontWeight="bold">
                        {hotel.occupancyRate}%
                      </Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="caption" color="textSecondary">
                        Revenue
                      </Typography>
                      <Typography variant="body2" fontWeight="bold">
                        ${hotel.revenue?.toLocaleString()}
                      </Typography>
                    </Grid>
                  </Grid>
                </Box>

                <Typography variant="caption" color="textSecondary" display="block" mb={2}>
                  Created: {format(toDate(hotel.createdAt), 'MMM dd, yyyy')}
                </Typography>

                <Box display="flex" justifyContent="space-between" alignItems="center">
                  <Box>
                    <Tooltip title="View Details">
                      <IconButton size="small" color="primary" onClick={() => handleViewHotel(hotel)}>
                        <ViewIcon />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Edit Hotel">
                      <IconButton size="small" color="secondary" onClick={() => handleOpenDialog(hotel)}>
                        <EditIcon />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Delete Hotel">
                      <IconButton size="small" color="error" onClick={() => handleDeleteHotel(hotel.id)}>
                        <DeleteIcon />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </Box>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {hotels.length === 0 && (
        <Box textAlign="center" py={8}>
          <HotelIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
          <Typography variant="h6" gutterBottom>
            No hotels found
          </Typography>
          <Typography variant="body2" color="textSecondary" mb={3}>
            Start by adding your first hotel to the system.
          </Typography>
          <Button
            variant="contained"
            startIcon={<AddIcon />}
            onClick={() => handleOpenDialog()}
          >
            Add First Hotel
          </Button>
        </Box>
      )}

      {/* Add/Edit Hotel Dialog */}
      <Dialog open={dialogOpen} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingHotel ? 'Edit Hotel' : 'Add New Hotel'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Hotel Name"
                value={formData.name}
                onChange={(e) => handleFormChange('name', e.target.value)}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Email"
                type="email"
                value={formData.email}
                onChange={(e) => handleFormChange('email', e.target.value)}
                required
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Description"
                multiline
                rows={3}
                value={formData.description}
                onChange={(e) => handleFormChange('description', e.target.value)}
              />
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Address"
                value={formData.address}
                onChange={(e) => handleFormChange('address', e.target.value)}
                required
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                label="City"
                value={formData.city}
                onChange={(e) => handleFormChange('city', e.target.value)}
                required
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                label="State"
                value={formData.state}
                onChange={(e) => handleFormChange('state', e.target.value)}
                required
              />
            </Grid>
            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                label="Zip Code"
                value={formData.zipCode}
                onChange={(e) => handleFormChange('zipCode', e.target.value)}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Phone"
                value={formData.phone}
                onChange={(e) => handleFormChange('phone', e.target.value)}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Website"
                value={formData.website}
                onChange={(e) => handleFormChange('website', e.target.value)}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Total Rooms"
                type="number"
                value={formData.totalRooms || ''}
                onChange={(e) => handleFormChange('totalRooms', parseInt(e.target.value) || 0)}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Vendor Name"
                value={formData.vendorName}
                onChange={(e) => handleFormChange('vendorName', e.target.value)}
                required
              />
            </Grid>

            {/* Image Upload Section */}
            <Grid item xs={12}>
              <Typography variant="subtitle1" gutterBottom>
                Hotel Images
              </Typography>
              <Button
                variant="outlined"
                component="label"
                startIcon={<UploadIcon />}
                sx={{ mb: 2 }}
              >
                Upload Images
                <input
                  type="file"
                  hidden
                  multiple
                  accept="image/*"
                  onChange={handleImageUpload}
                />
              </Button>

              {formData.images.length > 0 && (
                <ImageList cols={3} rowHeight={164}>
                  {formData.images.map((image, index) => (
                    <ImageListItem key={index}>
                      <img
                        src={image}
                        alt={`Hotel image ${index + 1}`}
                        loading="lazy"
                        style={{ height: 164, objectFit: 'cover' }}
                      />
                      <ImageListItemBar
                        actionIcon={
                          <IconButton
                            sx={{ color: 'rgba(255, 255, 255, 0.54)' }}
                            onClick={() => removeImage(index)}
                          >
                            <DeleteIcon />
                          </IconButton>
                        }
                      />
                    </ImageListItem>
                  ))}
                </ImageList>
              )}
            </Grid>

            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.isActive}
                    onChange={(e) => handleFormChange('isActive', e.target.checked)}
                  />
                }
                label="Hotel is Active"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog} startIcon={<CancelIcon />}>
            Cancel
          </Button>
          <Button onClick={handleSaveHotel} variant="contained" startIcon={<SaveIcon />}>
            {editingHotel ? 'Update Hotel' : 'Create Hotel'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* View Hotel Dialog */}
      <Dialog open={viewDialogOpen} onClose={() => setViewDialogOpen(false)} maxWidth="lg" fullWidth>
        <DialogTitle>
          Hotel Details: {viewingHotel?.name}
        </DialogTitle>
        <DialogContent>
          {viewingHotel && (
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Typography variant="h6" gutterBottom>
                  Basic Information
                </Typography>
                <List>
                  <ListItem>
                    <ListItemIcon>
                      <HotelIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Hotel Name"
                      secondary={viewingHotel.name}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      <LocationIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Address"
                      secondary={`${viewingHotel.address}, ${viewingHotel.city}, ${viewingHotel.state} ${viewingHotel.zipCode}`}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      <PhoneIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Phone"
                      secondary={viewingHotel.phone}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      <EmailIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Email"
                      secondary={viewingHotel.email}
                    />
                  </ListItem>
                </List>
              </Grid>

              <Grid item xs={12} md={6}>
                <Typography variant="h6" gutterBottom>
                  Performance Metrics
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={6}>
                    <Card>
                      <CardContent sx={{ textAlign: 'center' }}>
                        <Typography variant="h4" color="primary">
                          {viewingHotel.totalRooms}
                        </Typography>
                        <Typography variant="body2">Total Rooms</Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                  <Grid item xs={6}>
                    <Card>
                      <CardContent sx={{ textAlign: 'center' }}>
                        <Typography variant="h4" color="success.main">
                          {viewingHotel.occupancyRate}%
                        </Typography>
                        <Typography variant="body2">Occupancy</Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                  <Grid item xs={6}>
                    <Card>
                      <CardContent sx={{ textAlign: 'center' }}>
                        <Typography variant="h4" color="warning.main">
                          ${viewingHotel.revenue?.toLocaleString()}
                        </Typography>
                        <Typography variant="body2">Revenue</Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                  <Grid item xs={6}>
                    <Card>
                      <CardContent sx={{ textAlign: 'center' }}>
                        <Typography variant="h4" color="secondary.main">
                          {viewingHotel.rating}/5
                        </Typography>
                        <Typography variant="body2">Rating</Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                </Grid>
              </Grid>

              {viewingHotel.images && viewingHotel.images.length > 0 && (
                <Grid item xs={12}>
                  <Typography variant="h6" gutterBottom>
                    Hotel Images
                  </Typography>
                  <ImageList cols={4} rowHeight={200}>
                    {viewingHotel.images.map((image, index) => (
                      <ImageListItem key={index}>
                        <img
                          src={image}
                          alt={`${viewingHotel.name} image ${index + 1}`}
                          loading="lazy"
                          style={{ height: 200, objectFit: 'cover' }}
                        />
                      </ImageListItem>
                    ))}
                  </ImageList>
                </Grid>
              )}

              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom>
                  Amenities
                </Typography>
                <Box display="flex" flexWrap="wrap" gap={1}>
                  {viewingHotel.amenities?.map((amenity, index) => (
                    <Chip key={index} label={amenity} variant="outlined" />
                  ))}
                </Box>
              </Grid>
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setViewDialogOpen(false)}>
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default AdminHotelManagement;
