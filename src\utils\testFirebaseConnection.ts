import { auth, db } from '../firebase/config';
import { collection, getDocs, addDoc, deleteDoc, doc, updateDoc } from 'firebase/firestore';
import { signInAnonymously, signOut } from 'firebase/auth';

/**
 * Test Firebase Authentication connection
 */
export const testAuthConnection = async (): Promise<boolean> => {
  try {
    console.log('🔐 Testing Firebase Authentication...');
    
    // Test anonymous sign in
    const userCredential = await signInAnonymously(auth);
    console.log('✅ Anonymous authentication successful');
    
    // Sign out
    await signOut(auth);
    console.log('✅ Sign out successful');
    
    return true;
  } catch (error) {
    console.error('❌ Authentication test failed:', error);
    return false;
  }
};

/**
 * Test Firestore database connection
 */
export const testFirestoreConnection = async (): Promise<boolean> => {
  try {
    console.log('🗄️ Testing Firestore Database...');
    
    // Test reading from a collection
    const testCollection = collection(db, 'test');
    const snapshot = await getDocs(testCollection);
    console.log(`✅ Firestore read successful (${snapshot.size} documents)`);
    
    // Test writing to Firestore
    const testDoc = await addDoc(testCollection, {
      test: true,
      timestamp: new Date(),
      message: 'Firebase connection test'
    });
    console.log('✅ Firestore write successful');
    
    // Test updating document
    await updateDoc(testDoc, {
      updated: true,
      updateTimestamp: new Date()
    });
    console.log('✅ Firestore update successful');
    
    // Test deleting document
    await deleteDoc(testDoc);
    console.log('✅ Firestore delete successful');
    
    return true;
  } catch (error) {
    console.error('❌ Firestore test failed:', error);
    return false;
  }
};

/**
 * Test all Firebase services
 */
export const testAllFirebaseServices = async (): Promise<{
  auth: boolean;
  firestore: boolean;
  overall: boolean;
}> => {
  console.log('🚀 Starting Firebase Connection Tests...');
  console.log('=====================================');
  
  const authResult = await testAuthConnection();
  const firestoreResult = await testFirestoreConnection();
  
  const overall = authResult && firestoreResult;
  
  console.log('=====================================');
  console.log('📊 Test Results:');
  console.log(`   Authentication: ${authResult ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`   Firestore: ${firestoreResult ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`   Overall: ${overall ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);
  console.log('=====================================');
  
  return {
    auth: authResult,
    firestore: firestoreResult,
    overall
  };
};

/**
 * Test specific collections exist and are accessible
 */
export const testCollections = async (): Promise<{
  hotels: boolean;
  users: boolean;
  bookings: boolean;
  rooms: boolean;
  staff: boolean;
}> => {
  console.log('📋 Testing Collection Access...');
  
  const collections = ['hotels', 'users', 'bookings', 'rooms', 'staff'];
  const results: any = {};
  
  for (const collectionName of collections) {
    try {
      const snapshot = await getDocs(collection(db, collectionName));
      results[collectionName] = true;
      console.log(`✅ ${collectionName}: ${snapshot.size} documents`);
    } catch (error) {
      results[collectionName] = false;
      console.error(`❌ ${collectionName}: Failed to access`);
    }
  }
  
  return results;
};

/**
 * Test Firebase configuration
 */
export const testFirebaseConfig = (): boolean => {
  console.log('⚙️ Testing Firebase Configuration...');
  
  const requiredEnvVars = [
    'REACT_APP_FIREBASE_API_KEY',
    'REACT_APP_FIREBASE_AUTH_DOMAIN',
    'REACT_APP_FIREBASE_PROJECT_ID',
    'REACT_APP_FIREBASE_STORAGE_BUCKET',
    'REACT_APP_FIREBASE_MESSAGING_SENDER_ID',
    'REACT_APP_FIREBASE_APP_ID'
  ];
  
  let allConfigured = true;
  
  for (const envVar of requiredEnvVars) {
    const value = process.env[envVar];
    if (value) {
      console.log(`✅ ${envVar}: Configured`);
    } else {
      console.error(`❌ ${envVar}: Missing`);
      allConfigured = false;
    }
  }
  
  if (allConfigured) {
    console.log('✅ All Firebase environment variables are configured');
  } else {
    console.error('❌ Some Firebase environment variables are missing');
  }
  
  return allConfigured;
};

/**
 * Run comprehensive Firebase tests
 */
export const runComprehensiveTests = async (): Promise<void> => {
  console.log('🧪 Running Comprehensive Firebase Tests...');
  console.log('==========================================');
  
  // Test configuration
  const configResult = testFirebaseConfig();
  
  if (!configResult) {
    console.error('❌ Configuration test failed. Cannot proceed with other tests.');
    return;
  }
  
  // Test services
  const serviceResults = await testAllFirebaseServices();
  
  if (!serviceResults.overall) {
    console.error('❌ Service tests failed. Cannot proceed with collection tests.');
    return;
  }
  
  // Test collections
  const collectionResults = await testCollections();
  
  console.log('==========================================');
  console.log('🎯 Final Results:');
  console.log(`   Configuration: ${configResult ? '✅' : '❌'}`);
  console.log(`   Authentication: ${serviceResults.auth ? '✅' : '❌'}`);
  console.log(`   Firestore: ${serviceResults.firestore ? '✅' : '❌'}`);
  console.log(`   Collections: ${Object.values(collectionResults).every(Boolean) ? '✅' : '❌'}`);
  
  const allPassed = configResult && serviceResults.overall && Object.values(collectionResults).every(Boolean);
  
  if (allPassed) {
    console.log('🎉 ALL TESTS PASSED! Firebase is fully connected and working.');
  } else {
    console.log('⚠️ Some tests failed. Please check the configuration and try again.');
  }
  
  console.log('==========================================');
};
