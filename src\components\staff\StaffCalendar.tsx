import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Grid,
  CircularProgress,
  Alert,
  Chip
} from '@mui/material';
import {
  Add as AddIcon,
  Refresh as RefreshIcon,
  FilterList as FilterIcon,
  Person as PersonIcon,
  Event as EventIcon
} from '@mui/icons-material';
import FullCalendar from '@fullcalendar/react';
import dayGridPlugin from '@fullcalendar/daygrid';
import timeGridPlugin from '@fullcalendar/timegrid';
import interactionPlugin from '@fullcalendar/interaction';
import { EventInput, DateSelectArg, EventClickArg } from '@fullcalendar/core';
import { format, parseISO, setHours, setMinutes } from 'date-fns';
import { Timestamp } from 'firebase/firestore';

// Define shift types
const SHIFT_TYPE_MORNING = 'morning';
const SHIFT_TYPE_AFTERNOON = 'afternoon';
const SHIFT_TYPE_NIGHT = 'night';
const SHIFT_TYPE_CUSTOM = 'custom';

// Define shift times
const shiftTimes = {
  [SHIFT_TYPE_MORNING]: { start: '06:00', end: '14:00' },
  [SHIFT_TYPE_AFTERNOON]: { start: '14:00', end: '22:00' },
  [SHIFT_TYPE_NIGHT]: { start: '22:00', end: '06:00' }
};

interface StaffCalendarProps {
  hotelId: string;
  vendorId: string;
  staffList: any[];
  shifts: any[];
  timeOffRequests: any[];
  loading: boolean;
  error: string | null;
  onRefresh: () => void;
  onShiftCreate: (shift: any) => Promise<void>;
  onShiftUpdate: (shiftId: string, updates: any) => Promise<void>;
  onShiftDelete: (shiftId: string) => Promise<void>;
}

const StaffCalendar: React.FC<StaffCalendarProps> = ({
  hotelId,
  vendorId,
  staffList,
  shifts,
  timeOffRequests,
  loading,
  error,
  onRefresh,
  onShiftCreate,
  onShiftUpdate,
  onShiftDelete
}) => {
  const calendarRef = useRef<FullCalendar>(null);
  const [events, setEvents] = useState<EventInput[]>([]);
  const [selectedStaff, setSelectedStaff] = useState<string>('all');
  const [shiftDialogOpen, setShiftDialogOpen] = useState<boolean>(false);
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [selectedEvent, setSelectedEvent] = useState<EventInput | null>(null);
  const [shiftFormData, setShiftFormData] = useState<{
    staffId: string;
    shiftType: string;
    startTime: string;
    endTime: string;
    notes: string;
  }>({
    staffId: '',
    shiftType: SHIFT_TYPE_MORNING,
    startTime: '',
    endTime: '',
    notes: ''
  });

  // Convert shifts and time off requests to calendar events
  useEffect(() => {
    const calendarEvents: EventInput[] = [];
    
    // Add shifts to calendar events
    shifts.forEach(shift => {
      // Skip if filtering by staff and this shift is for a different staff member
      if (selectedStaff !== 'all' && shift.staffId !== selectedStaff) {
        return;
      }
      
      try {
        const startDate = shift.startTime.toDate();
        const endDate = shift.endTime.toDate();
        
        calendarEvents.push({
          id: `shift_${shift.id}`,
          title: `${shift.staffName} - ${shift.shiftType}`,
          start: startDate,
          end: endDate,
          allDay: false,
          extendedProps: {
            type: 'shift',
            shift: shift
          },
          backgroundColor: getShiftColor(shift.shiftType),
          borderColor: getShiftColor(shift.shiftType)
        });
      } catch (error) {
        console.error('Error processing shift:', error);
      }
    });
    
    // Add approved time off requests to calendar events
    timeOffRequests.forEach(timeOff => {
      // Skip if not approved
      if (timeOff.status !== 'approved') {
        return;
      }
      
      // Skip if filtering by staff and this time off is for a different staff member
      if (selectedStaff !== 'all' && timeOff.staffId !== selectedStaff) {
        return;
      }
      
      try {
        const startDate = timeOff.startDate.toDate();
        const endDate = timeOff.endDate.toDate();
        
        // Add one day to end date for proper display in calendar
        const displayEndDate = new Date(endDate);
        displayEndDate.setDate(displayEndDate.getDate() + 1);
        
        calendarEvents.push({
          id: `timeoff_${timeOff.id}`,
          title: `${timeOff.staffName} - Time Off (${timeOff.type})`,
          start: startDate,
          end: displayEndDate,
          allDay: true,
          extendedProps: {
            type: 'timeoff',
            timeOff: timeOff
          },
          backgroundColor: '#f44336', // Red
          borderColor: '#d32f2f',
          textColor: '#ffffff'
        });
      } catch (error) {
        console.error('Error processing time off request:', error);
      }
    });
    
    setEvents(calendarEvents);
  }, [shifts, timeOffRequests, selectedStaff]);

  const handleDateSelect = (selectInfo: DateSelectArg) => {
    if (selectInfo.view.type === 'timeGridDay' || selectInfo.view.type === 'timeGridWeek') {
      // Open shift dialog for the selected date and time
      setSelectedDate(selectInfo.start);
      setSelectedEvent(null);
      
      // Set default form data
      setShiftFormData({
        staffId: selectedStaff !== 'all' ? selectedStaff : (staffList.length > 0 ? staffList[0].id || '' : ''),
        shiftType: SHIFT_TYPE_CUSTOM,
        startTime: format(selectInfo.start, 'HH:mm'),
        endTime: format(selectInfo.end, 'HH:mm'),
        notes: ''
      });
      
      setShiftDialogOpen(true);
    }
  };

  const getShiftColor = (shiftType: string): string => {
    switch (shiftType) {
      case SHIFT_TYPE_MORNING:
        return '#2196f3'; // Blue
      case SHIFT_TYPE_AFTERNOON:
        return '#ff9800'; // Orange
      case SHIFT_TYPE_NIGHT:
        return '#673ab7'; // Deep Purple
      case SHIFT_TYPE_CUSTOM:
        return '#4caf50'; // Green
      default:
        return '#9e9e9e'; // Grey
    }
  };

  const handleStaffFilterChange = (event: React.ChangeEvent<{ value: unknown }>) => {
    setSelectedStaff(event.target.value as string);
  };

  return (
    <Box>
      <Box sx={{ mb: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <FormControl sx={{ minWidth: 200 }}>
          <InputLabel id="staff-filter-label">Staff Member</InputLabel>
          <Select
            labelId="staff-filter-label"
            value={selectedStaff}
            label="Staff Member"
            onChange={handleStaffFilterChange as any}
            startAdornment={
              <PersonIcon sx={{ mr: 1, color: 'action.active' }} />
            }
          >
            <MenuItem value="all">All Staff</MenuItem>
            {staffList.map((staff) => (
              <MenuItem key={staff.id} value={staff.id || ''}>
                {staff.name}
              </MenuItem>
            ))}
          </Select>
        </FormControl>
        
        <Button
          variant="outlined"
          startIcon={<RefreshIcon />}
          onClick={onRefresh}
          disabled={loading}
        >
          Refresh
        </Button>
      </Box>
      
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}
      
      <Paper sx={{ p: 2 }}>
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 5 }}>
            <CircularProgress />
          </Box>
        ) : (
          <Box sx={{ height: 'calc(100vh - 250px)', minHeight: 600 }}>
            <FullCalendar
              ref={calendarRef}
              plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin]}
              initialView="timeGridWeek"
              headerToolbar={{
                left: 'prev,next today',
                center: 'title',
                right: 'dayGridMonth,timeGridWeek,timeGridDay'
              }}
              events={events}
              selectable={true}
              selectMirror={true}
              dayMaxEvents={true}
              weekends={true}
              select={handleDateSelect}
              height="100%"
              allDaySlot={true}
              slotDuration="00:30:00"
              slotLabelInterval="01:00:00"
              slotLabelFormat={{
                hour: 'numeric',
                minute: '2-digit',
                hour12: false
              }}
              nowIndicator={true}
            />
          </Box>
        )}
      </Paper>
    </Box>
  );
};

export default StaffCalendar;
