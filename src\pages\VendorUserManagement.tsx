import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  <PERSON>po<PERSON>,
  <PERSON><PERSON>,
  Card,
  CardContent,
  Grid,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Avatar,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Alert,
  Tooltip,
  Tabs,
  Tab,
  CircularProgress
} from '@mui/material';
import {
  Add as AddIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  Person as PersonIcon,
  Email as EmailIcon,
  Phone as PhoneIcon,
  Business as BusinessIcon,
  AdminPanelSettings as AdminIcon,
  SupervisorAccount as VendorIcon,
  Group as StaffIcon,
  CheckCircle as ActiveIcon,
  Cancel as InactiveIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
  Security as LockIcon,
  SecurityUpdate as UnlockIcon
} from '@mui/icons-material';
import { useAuth } from '../hooks/useAuth';
import { User, Booking } from '../types';
import { getBookingsForHotel } from '../services/bookingService';
import { collection, query, where, onSnapshot, orderBy } from 'firebase/firestore';
import { db } from '../firebase/config';
import { format } from 'date-fns';

// Utility function to convert Timestamp to Date
const toDate = (date: any): Date => {
  if (!date) return new Date();
  if (date instanceof Date) return date;
  if (date.toDate && typeof date.toDate === 'function') return date.toDate();
  return new Date(date);
};

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
}

interface Guest {
  id: string;
  name: string;
  email: string;
  phone: string;
  totalBookings: number;
  lastBooking: Date;
  totalSpent: number;
  status: 'active' | 'inactive';
  bookings: Booking[];
}

const VendorUserManagement: React.FC = () => {
  const { user } = useAuth();
  const [guests, setGuests] = useState<Guest[]>([]);
  const [loading, setLoading] = useState(true);
  const [tabValue, setTabValue] = useState(0);
  const [viewDialogOpen, setViewDialogOpen] = useState(false);
  const [viewingGuest, setViewingGuest] = useState<Guest | null>(null);

  // Mock data for vendor's hotel guests
  const mockGuests: Guest[] = [
    {
      id: '1',
      name: 'John Smith',
      email: '<EMAIL>',
      phone: '+****************',
      totalBookings: 3,
      lastBooking: new Date('2024-01-20'),
      totalSpent: 1250.00,
      status: 'active',
      bookings: []
    },
    {
      id: '2',
      name: 'Sarah Johnson',
      email: '<EMAIL>',
      phone: '+****************',
      totalBookings: 5,
      lastBooking: new Date('2024-01-18'),
      totalSpent: 2100.00,
      status: 'active',
      bookings: []
    },
    {
      id: '3',
      name: 'Mike Wilson',
      email: '<EMAIL>',
      phone: '+****************',
      totalBookings: 2,
      lastBooking: new Date('2024-01-15'),
      totalSpent: 800.00,
      status: 'active',
      bookings: []
    },
    {
      id: '4',
      name: 'Emily Davis',
      email: '<EMAIL>',
      phone: '+****************',
      totalBookings: 1,
      lastBooking: new Date('2024-01-10'),
      totalSpent: 450.00,
      status: 'inactive',
      bookings: []
    }
  ];

  const fetchGuests = async () => {
    if (!user?.hotelId) {
      console.warn('Vendor user does not have hotelId assigned');
      setGuests(mockGuests);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);

      // Get all bookings for this hotel
      const bookings = await getBookingsForHotel(user.hotelId);

      // Group bookings by guest to create guest profiles
      const guestMap = new Map<string, Guest>();

      bookings.forEach(booking => {
        const guestKey = booking.guestEmail.toLowerCase();

        if (guestMap.has(guestKey)) {
          const existingGuest = guestMap.get(guestKey)!;
          existingGuest.totalBookings += 1;
          existingGuest.totalSpent += booking.totalAmount || 0;
          existingGuest.bookings.push(booking);

          // Update last booking date
          const bookingDate = booking.bookingDate instanceof Date ? booking.bookingDate : new Date(booking.bookingDate);
          if (bookingDate > existingGuest.lastBooking) {
            existingGuest.lastBooking = bookingDate;
          }
        } else {
          const newGuest: Guest = {
            id: `guest-${Date.now()}-${Math.random()}`,
            name: booking.guestName,
            email: booking.guestEmail,
            phone: booking.guestPhone,
            totalBookings: 1,
            lastBooking: booking.bookingDate instanceof Date ? booking.bookingDate : new Date(booking.bookingDate),
            totalSpent: booking.totalAmount || 0,
            status: 'active',
            bookings: [booking]
          };
          guestMap.set(guestKey, newGuest);
        }
      });

      // Convert map to array and sort by last booking date
      const guestList = Array.from(guestMap.values()).sort((a, b) =>
        b.lastBooking.getTime() - a.lastBooking.getTime()
      );

      setGuests(guestList);
    } catch (error) {
      console.error('Error fetching guests:', error);
      // Fallback to mock data
      setGuests(mockGuests);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchGuests();
  }, [user]);

  const handleViewGuest = (guest: Guest) => {
    setViewingGuest(guest);
    setViewDialogOpen(true);
  };
        role: userToEdit.role as 'staff' | 'vendor',
        password: '',
        confirmPassword: '',
        isActive: userToEdit.isActive
      });
    } else {
      setEditingUser(null);
      setFormData({
        name: '',
        email: '',
        phone: '',
        role: 'staff',
        password: '',
        confirmPassword: '',
        isActive: true
      });
    }
    setDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setDialogOpen(false);
    setEditingUser(null);
  };

  const handleFormChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSaveUser = async () => {
    try {
      if (formData.password !== formData.confirmPassword) {
        alert('Passwords do not match');
        return;
      }

      if (editingUser) {
        // Update existing user
        const updateData = {
          name: formData.name,
          email: formData.email,
          phone: formData.phone,
          role: formData.role,
          isActive: formData.isActive
        };
        await updateUser(editingUser.id, updateData);
      } else {
        // Create new user
        await createUser({
          email: formData.email,
          password: formData.password,
          name: formData.name,
          role: formData.role,
          phone: formData.phone,
          hotelId: user?.hotelId || '',
          vendorId: user?.id,
          isActive: formData.isActive
        });
      }

      handleCloseDialog();
      await fetchUsers();
    } catch (error) {
      console.error('Error saving user:', error);
    }
  };

  const handleViewUser = (userToView: User) => {
    setViewingUser(userToView);
    setViewDialogOpen(true);
  };

  const handleDeleteUser = async (userId: string) => {
    if (window.confirm('Are you sure you want to delete this user?')) {
      try {
        await deleteUser(userId);
        await fetchUsers();
      } catch (error) {
        console.error('Error deleting user:', error);
      }
    }
  };

  const handleToggleUserStatus = async (userId: string, currentStatus: boolean) => {
    try {
      await toggleUserStatus(userId, !currentStatus);
      await fetchUsers();
    } catch (error) {
      console.error('Error updating user status:', error);
    }
  };

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'super_admin':
        return <AdminIcon />;
      case 'vendor':
        return <VendorIcon />;
      case 'staff':
        return <StaffIcon />;
      default:
        return <PersonIcon />;
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'super_admin':
        return '#d32f2f';
      case 'vendor':
        return '#1976d2';
      case 'staff':
        return '#388e3c';
      default:
        return '#757575';
    }
  };

  const activeUsers = users.filter(u => u.isActive);
  const inactiveUsers = users.filter(u => !u.isActive);
  const staffUsers = users.filter(u => u.role === 'staff');

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  // Show error message if user doesn't have hotelId
  if (user && !user.hotelId) {
    return (
      <Box>
        <Typography variant="h4" component="h1" mb={3}>
          My Hotel Users
        </Typography>
        <Alert severity="warning" sx={{ mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            Hotel Assignment Required
          </Typography>
          <Typography variant="body2">
            Your vendor account is not assigned to a hotel. Please contact the system administrator to assign you to a hotel so you can manage users.
          </Typography>
        </Alert>
        <Card>
          <CardContent>
            <Typography variant="body1" color="text.secondary">
              Once your account is properly configured with a hotel assignment, you'll be able to:
            </Typography>
            <ul>
              <li>View and manage hotel staff users</li>
              <li>Add new staff members</li>
              <li>Update user permissions</li>
              <li>Monitor user activity</li>
            </ul>
          </CardContent>
        </Card>
      </Box>
    );
  }

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4" component="h1">
          My Hotel Users
        </Typography>
        <Button
          variant="contained"
          startIcon={<AddIcon />}
          onClick={() => handleOpenDialog()}
        >
          Add New User
        </Button>
      </Box>

      {/* Statistics Cards */}
      <Grid container spacing={3} mb={4}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Avatar sx={{ bgcolor: '#1976d2', mx: 'auto', mb: 1 }}>
                <PersonIcon />
              </Avatar>
              <Typography variant="h4" color="primary.main">
                {users.length}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                Total Users
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Avatar sx={{ bgcolor: '#388e3c', mx: 'auto', mb: 1 }}>
                <ActiveIcon />
              </Avatar>
              <Typography variant="h4" color="success.main">
                {activeUsers.length}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                Active Users
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Avatar sx={{ bgcolor: '#388e3c', mx: 'auto', mb: 1 }}>
                <StaffIcon />
              </Avatar>
              <Typography variant="h4" color="secondary.main">
                {staffUsers.length}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                Staff Members
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Avatar sx={{ bgcolor: '#f57c00', mx: 'auto', mb: 1 }}>
                <InactiveIcon />
              </Avatar>
              <Typography variant="h4" color="warning.main">
                {inactiveUsers.length}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                Inactive Users
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Users Table */}
      <Card>
        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={(_, newValue) => setTabValue(newValue)}>
            <Tab label={`All Users (${users.length})`} />
            <Tab label={`Active (${activeUsers.length})`} />
            <Tab label={`Inactive (${inactiveUsers.length})`} />
          </Tabs>
        </Box>

        <TabPanel value={tabValue} index={0}>
          <TableContainer>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>User</TableCell>
                  <TableCell>Role</TableCell>
                  <TableCell>Contact</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Last Login</TableCell>
                  <TableCell align="center">Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {users.map((user) => (
                  <TableRow key={user.id}>
                    <TableCell>
                      <Box display="flex" alignItems="center">
                        <Avatar
                          sx={{
                            bgcolor: getRoleColor(user.role),
                            mr: 2,
                            width: 40,
                            height: 40
                          }}
                        >
                          {getRoleIcon(user.role)}
                        </Avatar>
                        <Box>
                          <Typography variant="body2" fontWeight="bold">
                            {user.name}
                          </Typography>
                          <Typography variant="caption" color="textSecondary">
                            {user.email}
                          </Typography>
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip
                        icon={getRoleIcon(user.role)}
                        label={user.role.replace('_', ' ').toUpperCase()}
                        size="small"
                        sx={{
                          backgroundColor: getRoleColor(user.role),
                          color: 'white',
                          '& .MuiChip-icon': {
                            color: 'white'
                          }
                        }}
                      />
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {user.phone}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Box display="flex" alignItems="center" gap={1}>
                        <Chip
                          icon={user.isActive ? <ActiveIcon /> : <InactiveIcon />}
                          label={user.isActive ? 'Active' : 'Inactive'}
                          size="small"
                          color={user.isActive ? 'success' : 'error'}
                        />
                        <Switch
                          checked={user.isActive}
                          onChange={() => handleToggleUserStatus(user.id, user.isActive)}
                          size="small"
                        />
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {user.lastLogin ? format(toDate(user.lastLogin), 'MMM dd, HH:mm') : 'Never'}
                      </Typography>
                    </TableCell>
                    <TableCell align="center">
                      <Tooltip title="View Details">
                        <IconButton size="small" color="primary" onClick={() => handleViewUser(user)}>
                          <ViewIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Edit User">
                        <IconButton size="small" color="secondary" onClick={() => handleOpenDialog(user)}>
                          <EditIcon />
                        </IconButton>
                      </Tooltip>
                      <Tooltip title="Delete User">
                        <IconButton size="small" color="error" onClick={() => handleDeleteUser(user.id)}>
                          <DeleteIcon />
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          <Box p={3}>
            <Typography variant="h6" gutterBottom>
              Active Users ({activeUsers.length})
            </Typography>
            {activeUsers.length === 0 ? (
              <Alert severity="info">No active users found.</Alert>
            ) : (
              <Grid container spacing={3}>
                {activeUsers.map((user) => (
                  <Grid item xs={12} md={6} lg={4} key={user.id}>
                    <Card>
                      <CardContent>
                        <Box display="flex" alignItems="center" mb={2}>
                          <Avatar
                            sx={{
                              bgcolor: getRoleColor(user.role),
                              mr: 2,
                              width: 56,
                              height: 56
                            }}
                          >
                            {getRoleIcon(user.role)}
                          </Avatar>
                          <Box>
                            <Typography variant="h6">
                              {user.name}
                            </Typography>
                            <Typography variant="body2" color="textSecondary">
                              {user.role.replace('_', ' ').toUpperCase()}
                            </Typography>
                          </Box>
                        </Box>
                        <List dense>
                          <ListItem>
                            <ListItemIcon>
                              <EmailIcon sx={{ fontSize: 16 }} />
                            </ListItemIcon>
                            <ListItemText
                              primary={user.email}
                              primaryTypographyProps={{ variant: 'body2' }}
                            />
                          </ListItem>
                          <ListItem>
                            <ListItemIcon>
                              <PhoneIcon sx={{ fontSize: 16 }} />
                            </ListItemIcon>
                            <ListItemText
                              primary={user.phone}
                              primaryTypographyProps={{ variant: 'body2' }}
                            />
                          </ListItem>
                        </List>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            )}
          </Box>
        </TabPanel>

        <TabPanel value={tabValue} index={2}>
          <Box p={3}>
            <Typography variant="h6" gutterBottom>
              Inactive Users ({inactiveUsers.length})
            </Typography>
            {inactiveUsers.length === 0 ? (
              <Box textAlign="center" py={4}>
                <ActiveIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
                <Typography variant="h6" gutterBottom>
                  No inactive users
                </Typography>
                <Typography variant="body2" color="textSecondary">
                  All users are currently active.
                </Typography>
              </Box>
            ) : (
              <Alert severity="warning" sx={{ mb: 2 }}>
                You have {inactiveUsers.length} inactive users.
              </Alert>
            )}
          </Box>
        </TabPanel>
      </Card>

      {/* Add/Edit User Dialog */}
      <Dialog open={dialogOpen} onClose={handleCloseDialog} maxWidth="md" fullWidth>
        <DialogTitle>
          {editingUser ? 'Edit User' : 'Add New User'}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Full Name"
                value={formData.name}
                onChange={(e) => handleFormChange('name', e.target.value)}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Email"
                type="email"
                value={formData.email}
                onChange={(e) => handleFormChange('email', e.target.value)}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Phone"
                value={formData.phone}
                onChange={(e) => handleFormChange('phone', e.target.value)}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth required>
                <InputLabel>Role</InputLabel>
                <Select
                  value={formData.role}
                  label="Role"
                  onChange={(e) => handleFormChange('role', e.target.value)}
                >
                  <MenuItem value="staff">Staff</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Password"
                type="password"
                value={formData.password}
                onChange={(e) => handleFormChange('password', e.target.value)}
                required={!editingUser}
                helperText={editingUser ? "Leave blank to keep current password" : ""}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Confirm Password"
                type="password"
                value={formData.confirmPassword}
                onChange={(e) => handleFormChange('confirmPassword', e.target.value)}
                required={!editingUser}
              />
            </Grid>
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.isActive}
                    onChange={(e) => handleFormChange('isActive', e.target.checked)}
                  />
                }
                label="User is Active"
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog} startIcon={<CancelIcon />}>
            Cancel
          </Button>
          <Button onClick={handleSaveUser} variant="contained" startIcon={<SaveIcon />}>
            {editingUser ? 'Update User' : 'Create User'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* View User Dialog */}
      <Dialog open={viewDialogOpen} onClose={() => setViewDialogOpen(false)} maxWidth="md" fullWidth>
        <DialogTitle>
          User Details: {viewingUser?.name}
        </DialogTitle>
        <DialogContent>
          {viewingUser && (
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Typography variant="h6" gutterBottom>
                  Basic Information
                </Typography>
                <List>
                  <ListItem>
                    <ListItemIcon>
                      <PersonIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Full Name"
                      secondary={viewingUser.name}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      <EmailIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Email"
                      secondary={viewingUser.email}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      <PhoneIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Phone"
                      secondary={viewingUser.phone}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      {getRoleIcon(viewingUser.role)}
                    </ListItemIcon>
                    <ListItemText
                      primary="Role"
                      secondary={viewingUser.role.replace('_', ' ').toUpperCase()}
                    />
                  </ListItem>
                </List>
              </Grid>

              <Grid item xs={12} md={6}>
                <Typography variant="h6" gutterBottom>
                  Account Information
                </Typography>
                <List>
                  <ListItem>
                    <ListItemIcon>
                      <BusinessIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Hotel"
                      secondary={viewingUser.hotelName || 'Current Hotel'}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      {viewingUser.isActive ? <ActiveIcon /> : <InactiveIcon />}
                    </ListItemIcon>
                    <ListItemText
                      primary="Status"
                      secondary={viewingUser.isActive ? 'Active' : 'Inactive'}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      <PersonIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Last Login"
                      secondary={viewingUser.lastLogin ? format(toDate(viewingUser.lastLogin), 'MMM dd, yyyy HH:mm') : 'Never'}
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      <PersonIcon />
                    </ListItemIcon>
                    <ListItemText
                      primary="Created"
                      secondary={format(toDate(viewingUser.createdAt), 'MMM dd, yyyy')}
                    />
                  </ListItem>
                </List>
              </Grid>

              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom>
                  Permissions
                </Typography>
                <Box display="flex" flexWrap="wrap" gap={1}>
                  {viewingUser.permissions?.map((permission: string, index: number) => (
                    <Chip key={index} label={permission.replace('_', ' ').toUpperCase()} variant="outlined" />
                  ))}
                </Box>
              </Grid>
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setViewDialogOpen(false)}>
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default VendorUserManagement;
