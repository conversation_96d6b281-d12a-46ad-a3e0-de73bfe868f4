const { execSync } = require('child_process');
const path = require('path');

try {
  console.log('🚀 Starting build process...');
  console.log('📁 Working directory:', process.cwd());
  
  // Change to project directory
  process.chdir('C:\\Users\\<USER>\\Desktop\\linkinblink-hotel-management');
  console.log('📁 Changed to:', process.cwd());
  
  // Run build command
  console.log('⚡ Running npm run build...');
  const output = execSync('npm run build', { 
    encoding: 'utf8',
    stdio: 'pipe',
    timeout: 120000 // 2 minutes timeout
  });
  
  console.log('✅ Build completed successfully!');
  console.log('📄 Output:', output);
  
} catch (error) {
  console.error('❌ Build failed!');
  console.error('📄 Error output:', error.stdout || error.message);
  console.error('📄 Error details:', error.stderr || '');
  process.exit(1);
}
