import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Alert,
  CircularProgress,
  Container,
  Avatar,
  IconButton
} from '@mui/material';
import {
  People as StaffIcon,
  ArrowBack as BackIcon
} from '@mui/icons-material';
import { useAuth } from '../../hooks/useAuth';

const StaffPortalLogin: React.FC = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const { signIn } = useAuth();
  const navigate = useNavigate();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError('');
    setLoading(true);

    try {
      const user = await signIn(email, password);
      
      // Check if user has staff role
      if (user.role === 'staff') {
        navigate('/staff-dashboard');
      } else {
        setError('Access denied. This portal is for hotel staff members only.');
      }
    } catch (err: any) {
      setError(err.message || 'Failed to sign in');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #388e3c 0%, #2e7d32 100%)',
        display: 'flex',
        alignItems: 'center',
        py: 4
      }}
    >
      <Container component="main" maxWidth="sm">
        <Box sx={{ position: 'relative' }}>
          <IconButton
            onClick={() => navigate('/')}
            sx={{
              position: 'absolute',
              top: -60,
              left: 0,
              color: 'white',
              bgcolor: 'rgba(255,255,255,0.1)',
              '&:hover': { bgcolor: 'rgba(255,255,255,0.2)' }
            }}
          >
            <BackIcon />
          </IconButton>

          <Card sx={{ boxShadow: 6 }}>
            <CardContent sx={{ p: 6 }}>
              <Box textAlign="center" mb={4}>
                <Avatar
                  sx={{
                    bgcolor: '#388e3c',
                    width: 80,
                    height: 80,
                    mx: 'auto',
                    mb: 2
                  }}
                >
                  <StaffIcon sx={{ fontSize: 40 }} />
                </Avatar>
                <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
                  Staff Portal
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  Access your work schedule and daily tasks
                </Typography>
              </Box>

              <Box component="form" onSubmit={handleSubmit}>
                {error && (
                  <Alert severity="error" sx={{ mb: 3 }}>
                    {error}
                  </Alert>
                )}

                <TextField
                  margin="normal"
                  required
                  fullWidth
                  id="email"
                  label="Staff Email"
                  name="email"
                  autoComplete="email"
                  autoFocus
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  disabled={loading}
                  sx={{ mb: 2 }}
                />

                <TextField
                  margin="normal"
                  required
                  fullWidth
                  name="password"
                  label="Password"
                  type="password"
                  id="password"
                  autoComplete="current-password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  disabled={loading}
                  sx={{ mb: 3 }}
                />

                <Button
                  type="submit"
                  fullWidth
                  variant="contained"
                  size="large"
                  disabled={loading}
                  sx={{ 
                    mb: 3, 
                    py: 1.5,
                    bgcolor: '#388e3c',
                    '&:hover': { bgcolor: '#2e7d32' }
                  }}
                >
                  {loading ? <CircularProgress size={24} /> : 'Sign In to Staff Portal'}
                </Button>

                <Box textAlign="center">
                  <Typography variant="body2" color="text.secondary">
                    Forgot your password?{' '}
                    <Button variant="text" size="small" sx={{ color: '#388e3c' }}>
                      Contact your manager
                    </Button>
                  </Typography>
                </Box>
              </Box>

              <Box sx={{ mt: 4, p: 3, bgcolor: 'grey.50', borderRadius: 1 }}>
                <Typography variant="h6" gutterBottom color="#388e3c">
                  Demo Credentials:
                </Typography>
                <Typography variant="body2" sx={{ mb: 1 }}>
                  <strong>Staff Account:</strong><br />
                  Email: <EMAIL><br />
                  Password: staff123456
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  (This will be created during setup)
                </Typography>
              </Box>
            </CardContent>
          </Card>
        </Box>
      </Container>
    </Box>
  );
};

export default StaffPortalLogin;
