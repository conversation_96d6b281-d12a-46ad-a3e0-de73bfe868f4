# 🔥 Firebase Setup & Data Import Guide

## 📋 **Complete Setup Checklist**

### ✅ **Step 1: Enable Firebase Services**

1. **Go to Firebase Console:**
   👉 [https://console.firebase.google.com/project/linkinblink-f544a](https://console.firebase.google.com/project/linkinblink-f544a)

2. **Enable Authentication:**
   - Click "Authentication" → "Get started"
   - Go to "Sign-in method" tab
   - Enable "Email/Password" (first toggle only)
   - Click "Save"

3. **Enable Firestore Database:**
   - Click "Firestore Database" → "Create database"
   - Choose "Start in test mode"
   - Select your location (choose closest to you)
   - Click "Done"

### ✅ **Step 2: Create User Accounts**

In Firebase Console → Authentication → Users:

**Click "Add user" for each:**

1. **🔴 Super Admin:**
   - Email: `<EMAIL>`
   - Password: `admin123456`

2. **🔵 Vendor:**
   - Email: `<EMAIL>`
   - Password: `vendor123456`

3. **🟢 Staff:**
   - Email: `<EMAIL>`
   - Password: `staff123456`

### ✅ **Step 3: Import Data to Firestore**

**Method 1: Using Firebase CLI (Recommended)**

1. **Install Firebase CLI:**
   ```bash
   npm install -g firebase-tools
   ```

2. **Login to Firebase:**
   ```bash
   firebase login
   ```

3. **Import each collection:**
   ```bash
   firebase firestore:import firebase-data/users.json --project linkinblink-f544a
   firebase firestore:import firebase-data/hotels.json --project linkinblink-f544a
   firebase firestore:import firebase-data/rooms.json --project linkinblink-f544a
   firebase firestore:import firebase-data/staff.json --project linkinblink-f544a
   firebase firestore:import firebase-data/bookings.json --project linkinblink-f544a
   firebase firestore:import firebase-data/shifts.json --project linkinblink-f544a
   ```

**Method 2: Manual Import via Console**

1. **Go to Firestore Database in Firebase Console**
2. **For each collection, click "Start collection"**
3. **Copy the data from the JSON files and create documents manually**

### ✅ **Step 4: Update User Document IDs**

**IMPORTANT:** After creating users in Authentication, you need to update the user document IDs in Firestore:

1. **Go to Authentication → Users**
2. **Copy the UID for each user**
3. **Go to Firestore → users collection**
4. **Update document IDs:**
   - Change `admin-user-id` to the actual <NAME_EMAIL>
   - Change `vendor-user-id` to the actual <NAME_EMAIL>
   - Change `staff-user-id` to the actual <NAME_EMAIL>

### ✅ **Step 5: Test Your Application**

1. **Access your application:** `http://localhost:3000`
2. **Test each portal:**

**🔴 Super Admin Portal:**
- URL: `http://localhost:3000/admin-portal`
- Email: `<EMAIL>`
- Password: `admin123456`

**🔵 Hotel/Vendor Portal:**
- URL: `http://localhost:3000/hotel-portal`
- Email: `<EMAIL>`
- Password: `vendor123456`

**🟢 Staff Portal:**
- URL: `http://localhost:3000/staff-portal`
- Email: `<EMAIL>`
- Password: `staff123456`

## 📊 **What Data You'll Have:**

### 🏨 **Hotels (2 hotels)**
- Grand Plaza Hotel (New York)
- Seaside Resort & Spa (Miami)

### 🛏️ **Rooms (4 rooms)**
- Single Room (Available)
- Double Room (Available)
- Suite (Occupied)
- Ocean View Room (Available)

### 👥 **Staff (3 staff members)**
- John Manager (General Manager)
- Sarah Receptionist (Front Desk Agent)
- Mike Housekeeping (Housekeeping Supervisor)

### 📅 **Bookings (4 bookings)**
- Confirmed booking for Alice Johnson
- Checked-in booking for Robert Smith
- Pending booking for Emily Davis
- Confirmed business booking for Michael Brown

### ⏰ **Shifts (5 scheduled shifts)**
- Management shifts
- Front desk coverage
- Housekeeping schedules

## 🎯 **Features You Can Test:**

✅ **Three separate login portals**
✅ **Role-based dashboard access**
✅ **Hotel management**
✅ **Room management**
✅ **Staff scheduling**
✅ **Booking management**
✅ **Real-time data updates**

## 🚨 **Troubleshooting:**

**If login fails:**
1. Check that Authentication is enabled
2. Verify user accounts are created
3. Ensure user documents exist in Firestore with correct UIDs

**If data doesn't appear:**
1. Check Firestore rules (should be in test mode)
2. Verify collections are imported correctly
3. Check browser console for errors

**If portal access is denied:**
1. Verify user role in Firestore user document
2. Check that role matches portal requirements

## 🎉 **Success!**

Once completed, you'll have a fully functional hotel management system with:
- Three distinct user portals
- Complete sample data
- Real-time Firebase backend
- Professional Material-UI interface

Your LinkinBlink Hotel Management System is ready to use!
